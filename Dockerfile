FROM yiisoftware/yii2-php:8.3-fpm

RUN apt-get update && apt-get install -y \
    curl git unzip libwebp-dev libfreetype6-dev libjpeg62-turbo-dev libpng-dev libzip-dev libicu-dev nginx supervisor cron \
    && docker-php-ext-configure gd --with-freetype --with-jpeg --with-webp \
    && docker-php-ext-install gd pdo pdo_mysql zip sockets intl \
    && rm -rf /var/lib/apt/lists/*

COPY --from=composer:2.5 /usr/bin/composer /usr/bin/composer

COPY ./docker/php.ini /usr/local/etc/php/conf.d/base.ini
COPY ./docker/www.conf /usr/local/etc/php-fpm.d/www.conf
COPY ./docker/supervisord.conf /etc/supervisor/conf.d/supervisord.conf
COPY ./docker/nginx/vhost.conf /etc/nginx/sites-available/default

WORKDIR /app
COPY composer.json composer.lock /app/

#RUN composer install --no-interaction --prefer-dist --optimize-autoloader || \
#    (composer update --no-interaction --optimize-autoloader && exit 0)

RUN composer install --no-interaction --prefer-dist --optimize-autoloader --ignore-platform-reqs

COPY . /app/

RUN mkdir -p /app/api/web/assets /app/api/runtime \
    && chmod -R 777 /app/api/web/assets /app/api/runtime

RUN mkdir -p /app/backend/web/assets /app/backend/runtime \
    && chmod -R 777 /app/backend/web/assets /app/backend/runtime

RUN mkdir -p /app/storage/web/assets /app/storage/runtime \
    && chmod -R 777 /app/storage/web/assets /app/storage/runtime

RUN chmod -R 777 /app/storage/web/source

ENV TZ=Asia/Tashkent
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

RUN php console/yii migrate --interactive=0

EXPOSE 80
CMD ["/usr/bin/supervisord", "-n"]