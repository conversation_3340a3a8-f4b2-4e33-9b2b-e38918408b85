# OrderForm - Shop vs Client Moduli Farqlari

## Um<PERSON>y <PERSON>lumot

`api\modules\client\forms\OrderForm` klassi `api\modules\shop\forms\OrderForm` klassidan namuna olib ya<PERSON><PERSON><PERSON>, lekin client moduli uchun moslash<PERSON>gan.

## Asos<PERSON>y <PERSON>lar

### 1. Atributlar

#### Shop modulida mavjud, Client modulida yo'q:
- `$type` - buyurtma turi
- `$shipping_sum` - yetkazib berish summasi
- `$payment_status` - to'lov holati
- `$payment_date` - to'lov sanasi
- `$cancel_reason` - bekor qilish sababi
- `$cancel_date` - bekor qilish sanasi
- `$shop_end` - do'kon tugash vaqti

#### Client modulida mavjud, Shop modulida yo'q:
- `$address` - yetkazib berish manzili (client uchun muhim)

#### Ikkala modulda ham mavjud:
- `$product_id` - mahsulot ID
- `$classifier_id` - klassifikator ID
- `$plan_schedule_id` - reja grafik ID
- `$count` - miqdor
- `$regions` - hududlar
- `$account_number_id` - hisob raqam ID
- `$pkcs7` - elektron imzo
- `$customer_account_treasury` - mijoz g'azna hisobi
- `$expense_item` - xarajat moddasi
- `$receiver_fio` - qabul qiluvchi F.I.O
- `$receiver_phone` - qabul qiluvchi telefon
- `$delivery_type` - yetkazib berish turi
- `$payment_type` - to'lov turi

### 2. Validatsiya Qoidalari

#### Shop moduli:
- `product_id`, `classifier_id`, `plan_schedule_id`, `count` - majburiy
- Murakkab biznes logika validatsiyalari

#### Client moduli:
- `product_id`, `classifier_id`, `plan_schedule_id`, `count` - majburiy
- Qo'shimcha string uzunlik validatsiyalari
- `count` uchun minimal qiymat (1)
- `regions` uchun har bir element integer bo'lishi kerak

### 3. Biznes Logika

#### Shop moduli:
- To'liq moliyaviy operatsiyalar (zalog, komissiya)
- Virtual tranzaksiyalar yaratish
- BMH (Bazaviy Hisoblash Miqdori) tekshiruvi
- Murakkab balans tekshiruvi

#### Client moduli:
- Soddalashtirilgan buyurtma yaratish
- Asosiy validatsiyalar
- Moliyaviy operatsiyalarsiz
- Tezkor buyurtma jarayoni

### 4. Metodlar

#### Shop moduli:
- `getResult()` - to'liq buyurtma jarayoni (400+ qator)
- Inline biznes logika

#### Client moduli:
- `getResult()` - asosiy koordinator metod
- `validateOrder()` - buyurtma validatsiyasi
- `validateAndUpdatePlanSchedule()` - reja grafik boshqaruvi
- `createOrder()` - buyurtma yaratish
- `saveOrderRegions()` - hududlarni saqlash
- `createOrderList()` - buyurtma ro'yxati
- `createOrderRequest()` - buyurtma so'rovi

### 5. Xatoliklar Boshqaruvi

#### Shop moduli:
- Inline exception handling
- Transaction rollback

#### Client moduli:
- Strukturali exception handling
- Alohida metodlarda xatolik boshqaruvi
- Aniq xatolik xabarlari

## Afzalliklar

### Client Moduli:
- ✅ Toza va o'qilishi oson kod
- ✅ Modulli struktura
- ✅ Oson test qilish
- ✅ Tez ishlash
- ✅ Sodda maintenance

### Shop Moduli:
- ✅ To'liq biznes logika
- ✅ Moliyaviy operatsiyalar
- ✅ Keng funksionallik

## Tavsiyalar

1. **Client moduli** oddiy buyurtmalar uchun ishlatilsin
2. **Shop moduli** to'liq e-commerce funksionallik kerak bo'lganda ishlatilsin
3. Umumiy kod qismlarini alohida service klasslarga ajratish mumkin
4. Interface yoki abstract class orqali umumiy struktura yaratish tavsiya etiladi
