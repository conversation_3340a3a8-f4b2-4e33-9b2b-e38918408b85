# Shop Module - Product Draft Filter API

## Yangilangan Fayl

**Fayl:** `api\modules\shop\filters\ProductDraftFilter.php`

**Maqsad:** Product Draft larni filter qilish va status bo'yicha guruhlab qaytarish (ProductFilterForm kabi)

## API Ishlatish

### Request
```
GET /shop/product-draft?platform_display=e-shop&search=kompyuter&perPage=20&currentPage=1
```

### Query Parameters

| Parameter | Type | Required | Values | Description |
|-----------|------|----------|---------|-------------|
| `platform_display` | string | No | `e-shop`, `n-shop` | Platform turi |
| `search` | string | No | - | Qidiruv matni |
| `perPage` | integer | No | 1-100 | Sahifadagi elementlar soni (default: 10) |
| `currentPage` | integer | No | 0+ | <PERSON><PERSON><PERSON> (0-dan bosh<PERSON>, default: 0) |

### Response Structure (Paginatsiya bilan)

```json
{
  "result": {
    "100": {
      "status": 100,
      "status_name": "Moderator tekshiruvida",
      "count": 15,
      "data": [
        {
          "id": 1,
          "title": "Product Draft nomi",
          "brand_title": "Brand nomi",
          "description_uz": "Tavsif",
          "description_ru": "Описание",
          "price": 100000,
          "quantity": 50,
          "min_order": 1,
          "max_order": 100,
          "state": 100,
          "status": 300,
          "platform_display": "e-shop",
          "type": 1,
          "created_at": "2024-01-01 10:00:00",
          "active_date": "2024-01-01"
        }
      ],
      "meta": {
        "totalCount": 15,
        "pageCount": 2,
        "currentPage": 0,
        "perPage": 10
      }
    },
    "110": {
      "status": 110,
      "status_name": "Moderatordan qaytarilgan",
      "count": 3,
      "data": [...],
      "meta": {
        "totalCount": 3,
        "pageCount": 1,
        "currentPage": 0,
        "perPage": 10
      }
    },
    "200": {
      "status": 200,
      "status_name": "Mablag' yetarli emas",
      "count": 0,
      "data": [],
      "meta": {
        "totalCount": 0,
        "pageCount": 0,
        "currentPage": 0,
        "perPage": 10
      }
    }
  },
  "errors": null
}
```

## Status Qiymatlari (Product Draft)

| Status | Qiymat | Nomi |
|--------|--------|------|
| `SHOP_STATE_NEW` | 100 | Moderator tekshiruvida |
| `SHOP_STATE_RETURN_MODERATOR` | 110 | Moderatordan qaytarilgan |
| `SHOP_STATE_NO_MONEY` | 200 | Mablag' yetarli emas |
| `SHOP_STATE_ACTIVE` | 300 | Sotuvda |
| `SHOP_STATE_IN_ACTIVE` | 400 | Aktiv holatda emas |
| `SHOP_STATE_DELETED` | 500 | O'chirilgan |
| `SHOP_STATE_CANCEL` | 600 | Rad qilingan |

## Filter Logikasi

### Platform Display Filter
```php
if (!empty($this->platform_display)) {
    $query->andWhere(['platform_display' => $this->platform_display]);
}
```

### Search Filter (ilike - PostgreSQL)
```php
if (!empty($this->search)) {
    $query->andWhere([
        'or',
        ['ilike', 'title', $this->search],
        ['ilike', 'brand_title', $this->search],
        ['ilike', 'description_uz', $this->search],
        ['ilike', 'description_ru', $this->search],
    ]);
}
```

## Paginatsiya

### paginate() Helper Funksiyasi
```php
// Har bir status uchun alohida paginatsiya
$paginatedResult = paginate($query);

$result[$status] = [
    'status' => $status,
    'status_name' => $this->getStatusName($status),
    'count' => $paginatedResult['meta']['totalCount'],
    'data' => $paginatedResult['data'],
    'meta' => $paginatedResult['meta'],
];
```

### Meta Ma'lumotlari
- `totalCount` - Jami product draft lar soni
- `pageCount` - Jami sahifalar soni  
- `currentPage` - Joriy sahifa (0-dan boshlanadi)
- `perPage` - Sahifadagi elementlar soni

## Optimizatsiya

1. **Select Fields:** Faqat kerakli ustunlar tanlanadi
2. **Paginatsiya:** `paginate()` helper funksiyasi ishlatiladi
3. **Indexing:** `state`, `platform_display` ustunlari uchun index tavsiya etiladi
4. **Soft Delete:** `deleted_at IS NULL` filter qo'shilgan
5. **Ordering:** `ORDER BY id DESC` - oxirgi qo'shilganlar birinchi
6. **ilike:** PostgreSQL uchun case-insensitive qidiruv

## ProductFilterForm vs ProductDraftFilter

| Xususiyat | ProductFilterForm | ProductDraftFilter |
|-----------|-------------------|-------------------|
| **Model** | Product | ProductDraft |
| **Resource** | Product | ProductDraftCustomResource |
| **Search** | `like` (MySQL) | `ilike` (PostgreSQL) |
| **Response** | Status bo'yicha guruhlangan | Status bo'yicha guruhlangan |
| **Paginatsiya** | ✅ paginate() | ✅ paginate() |
| **Filter** | platform_display, search | platform_display, search |

## Test

```bash
# Barcha product draftlar (default paginatsiya)
curl "http://api.domain.com/shop/product-draft"

# E-shop product draftlari (20 ta elementli sahifa)
curl "http://api.domain.com/shop/product-draft?platform_display=e-shop&perPage=20"

# Qidiruv (2-sahifa)
curl "http://api.domain.com/shop/product-draft?search=kompyuter&currentPage=1"

# To'liq kombinatsiya
curl "http://api.domain.com/shop/product-draft?platform_display=n-shop&search=telefon&perPage=15&currentPage=2"
```

## Endpoint

**URL:** `GET /shop/product-draft`
**Controller:** `ProductDraftController::actionIndex()`
**Filter:** `ProductDraftFilter`

Endi ProductDraftFilter ham ProductFilterForm kabi ishlaydi - status bo'yicha guruhlangan, paginatsiya bilan!
