# Shop Module - Product Filter API

## Yaratilgan Fayllar

### 1. ProductFilterForm
**Fayl:** `api\modules\shop\forms\ProductFilterForm.php`

**Maqsad:** Mahsulotlarni filter qilish va status bo'yicha guruhlab qaytarish

**Xususiyatlar:**
- `platform_display` - Platform turi filter (e-shop yoki n-shop)
- `search` - Mahsulot nomi va tavsif bo'yicha qidiruv
- Status bo'yicha guruhlash
- Optimizatsiya uchun `select()` ishlatilgan

### 2. ProductController - actionFilter
**Fayl:** `api\modules\shop\controllers\ProductController.php`

**Yangi action:** `actionFilter()`

**URL:** `GET /shop/product/filter`

## API Ishlatish

### Request
```
GET /shop/product/filter?platform_display=e-shop&search=kompyuter
```

### Query Parameters

| Parameter | Type | Required | Values | Description |
|-----------|------|----------|---------|-------------|
| `platform_display` | string | No | `e-shop`, `n-shop` | Platform turi |
| `search` | string | No | - | Qidiruv matni |

### Response Structure

```json
{
  "result": {
    "100": {
      "status": 100,
      "status_name": "Moderator tekshiruvida",
      "count": 5,
      "products": [
        {
          "id": 1,
          "title": "Mahsulot nomi",
          "brand_title": "Brand nomi",
          "description_uz": "Tavsif",
          "description_ru": "Описание",
          "price": 100000,
          "quantity": 50,
          "min_order": 1,
          "max_order": 100,
          "state": 100,
          "status": 300,
          "platform_display": "e-shop",
          "type": 1,
          "created_at": "2024-01-01 10:00:00",
          "active_date": "2024-01-01"
        }
      ]
    },
    "110": {
      "status": 110,
      "status_name": "Moderatordan qaytarilgan",
      "count": 2,
      "products": [...]
    },
    "200": {
      "status": 200,
      "status_name": "Mablag' yetarli emas",
      "count": 0,
      "products": []
    },
    "300": {
      "status": 300,
      "status_name": "Sotuvda",
      "count": 15,
      "products": [...]
    },
    "400": {
      "status": 400,
      "status_name": "Aktiv holatda emas",
      "count": 3,
      "products": [...]
    },
    "500": {
      "status": 500,
      "status_name": "O'chirilgan",
      "count": 1,
      "products": [...]
    },
    "600": {
      "status": 600,
      "status_name": "Rad qilingan",
      "count": 0,
      "products": []
    }
  },
  "errors": null
}
```

## Status Qiymatlari

| Status | Qiymat | Nomi |
|--------|--------|------|
| `SHOP_STATE_NEW` | 100 | Moderator tekshiruvida |
| `SHOP_STATE_RETURN_MODERATOR` | 110 | Moderatordan qaytarilgan |
| `SHOP_STATE_NO_MONEY` | 200 | Mablag' yetarli emas |
| `SHOP_STATE_ACTIVE` | 300 | Sotuvda |
| `SHOP_STATE_IN_ACTIVE` | 400 | Aktiv holatda emas |
| `SHOP_STATE_DELETED` | 500 | O'chirilgan |
| `SHOP_STATE_CANCEL` | 600 | Rad qilingan |

## Platform Display Qiymatlari

| Qiymat | Tavsif |
|--------|--------|
| `e-shop` | Elektron do'kon |
| `n-shop` | Milliy do'kon |

## Filter Logikasi

### Platform Display Filter
```php
if (!empty($this->platform_display)) {
    $query->andWhere(['platform_display' => $this->platform_display]);
}
```

### Search Filter
```php
if (!empty($this->search)) {
    $query->andWhere([
        'or',
        ['like', 'title', $this->search],
        ['like', 'brand_title', $this->search],
        ['like', 'description_uz', $this->search],
        ['like', 'description_ru', $this->search],
    ]);
}
```

## Optimizatsiya

1. **Select Fields:** Faqat kerakli ustunlar tanlanadi
2. **Indexing:** `state`, `platform_display` ustunlari uchun index tavsiya etiladi
3. **Soft Delete:** `deleted_at IS NULL` filter qo'shilgan
4. **Ordering:** `ORDER BY id DESC` - oxirgi qo'shilganlar birinchi

## Xavfsizlik

- Input validation (platform_display, search)
- SQL injection himoyasi (Yii2 Query Builder)
- XSS himoyasi (output encoding)

## Test

```bash
# Barcha mahsulotlar
curl "http://api.domain.com/shop/product/filter"

# E-shop mahsulotlari
curl "http://api.domain.com/shop/product/filter?platform_display=e-shop"

# Qidiruv
curl "http://api.domain.com/shop/product/filter?search=kompyuter"

# Kombinatsiya
curl "http://api.domain.com/shop/product/filter?platform_display=n-shop&search=telefon"
```
