<?php

namespace api\modules\auction\controllers;

use Yii;
use api\components\ApiController;
use api\modules\auction\filters\AuctionClassifierFilter;
use api\modules\auction\forms\AuctionClassifierForm;
use common\models\auction\AuctionClassifier;
use yii\filters\AccessControl;

class AuctionClassifierController extends ApiController
{

    public function behaviors(): array
    {
        $parent = parent::behaviors();
        $parent['access'] = [
            'class' => AccessControl::class,
            'rules' => [
                [
                   'allow' => true,
                   'actions' => ['index','create','update', 'delete', 'view'],
                   'roles' => ['user']
                ],
            ],
        ];
        return $parent;
    }
  public function actionIndex()
  {
    return $this->sendResponse(
      new AuctionClassifierFilter(),
      Yii::$app->request->queryParams
    );
  }

  public function actionCreate()
  {
    return $this->sendResponse(
      new AuctionClassifierForm(),
      Yii::$app->request->bodyParams
    );
  }

  public function actionUpdate(AuctionClassifier $auctionClassifier)
  {
    return $this->sendResponse(
      new AuctionClassifierForm($auctionClassifier),
      Yii::$app->request->bodyParams
    );
  }

  public function actionDelete(AuctionClassifier $auctionClassifier)
  {
    return $this->sendResponse(
      new AuctionClassifierFilter($auctionClassifier),
      Yii::$app->request->queryParams
    );
  }

  public function actionView(AuctionClassifier $auctionClassifier)
  {
    return $this->sendResponse(
      new AuctionClassifierFilter($auctionClassifier),
      Yii::$app->request->queryParams
    );
  }
}
