<?php

namespace api\modules\auction\controllers;

use api\components\ApiController;
use api\modules\auction\filters\AuctionActiveFilter;
use api\modules\auction\filters\AuctionFilter;
use api\modules\auction\filters\AuctionOfferFilter;
use api\modules\auction\filters\MyTradesFilter;
use api\modules\auction\forms\AuctionCreateBudgetForm;
use api\modules\auction\forms\AuctionDeleteForm;
use api\modules\auction\forms\AuctionForm;
use api\modules\auction\forms\AuctionOfferForm;
use api\modules\auction\forms\AuctionReloadForm;
use api\modules\auction\forms\AuctionReloadWithUpdatingForm;
use api\modules\auction\forms\AuctionUpdateBudgetForm;
use api\modules\auction\forms\AuctionUpdateForm;
use api\modules\auction\resources\AuctionActiveResource;
use api\modules\auction\resources\AuctionDetailResource;
use common\enums\AuctionEnum;
use common\enums\PkcsEnum;
use common\models\auction\AuctionOffer;
use Yii;
use yii\filters\AccessControl;
use yii\web\NotFoundHttpException;

class AuctionController extends ApiController
{
    public function behaviors(): array
    {
        $parent = parent::behaviors();
        $parent['access'] = [
            'class' => AccessControl::class,
            'except' => ['active'],
            'rules' => [
                [
                    'allow' => true,
                    'actions' => [
                        'my-lots','create','create-budjet', 'update-budjet',
                        'reload',  'reload-with-updating', 'delete', 'view',
                        'auction-view', 'offers', 'auction-view-with-my-offer',
                        'my-trades', 'offer', 'realized',
                    ],
                    'roles' => ['user']
                ],
            ],
        ];
        return $parent;
    }
    public function actionMyLots()
    {
        //auction/auction/my-lots
        return $this->sendResponse(
            new AuctionFilter(),
            Yii::$app->request->queryParams,
        );
    }

    public function actionCreate()
    {
        $decodedPks7 = $this->verifyPkcs7(Yii::$app->request->bodyParams);
        $body = Yii::$app->request->bodyParams;

        return $this->sendResponsePost(
            new AuctionForm(),
            $decodedPks7,
            $body['pkcs7'],
            PkcsEnum::PKCS7_TYPE_AUCTION_CREATE
        );
    }

    public function actionCreateBudget()
    {
        return $this->sendResponse(
            new AuctionCreateBudgetForm(),
            Yii::$app->request->bodyParams,
        );
    }

    public function actionUpdateBudget($id)
    {
        return $this->sendResponse(
            new AuctionUpdateBudgetForm($this->findOne($id)),
            Yii::$app->request->bodyParams,
        );
    }

    public function actionReload($id)
    {
        return $this->sendResponse(
            new AuctionReloadForm($this->findOne($id)),
            Yii::$app->request->bodyParams,
        );
    }

    public function actionReloadWithUpdating($id)
    {
        return $this->sendResponse(
            new AuctionReloadWithUpdatingForm($this->findOne($id)),
            Yii::$app->request->bodyParams,
        );
    }

    public function actionUpdate($id)
    {
        return $this->sendResponse(
            new AuctionUpdateForm($this->findOne($id)),
            Yii::$app->request->bodyParams,
        );
    }

    /**
     * @throws NotFoundHttpException
     */
    public function actionDelete($id): array
    {
        return $this->sendResponse(
            new AuctionDeleteForm($this->findOne($id)),
            Yii::$app->request->bodyParams,
        );
    }

    /**
     * @throws NotFoundHttpException
     */
    private function findOne($id): AuctionDetailResource
    {
        $model = AuctionDetailResource::findOne(['company_id' => Yii::$app->user->identity->company_id, 'id' => $id]);

        if (!$model) throw new NotFoundHttpException("Lot topilmadi");

        return $model;
    }

    private function findActiveOne($id, $state = true)
    {
        $model = AuctionActiveResource::find()->where(['id' => $id]);
        if ($state) {
            $model->andWhere(['status' => AuctionEnum::STATUS_ACTIVE]);
        }
        $model = $model->one();

        if (!$model) throw new NotFoundHttpException(t("Lot topilmadi"));

        return $model;
    }

    // id bo'yicha topilgan Auksionni qaytaradi
    public function actionView($id)
    {
        try {
            return $this->sendModel($this->findOne($id));
        } catch (\Throwable $th) {
            throw $th;
        }
    }

    //aktiv, without auth
    public function actionAuctionView($id)
    {
        //auction/auction/auction-view
        try {
            $model = $this->findActiveOne($id, false);
            if ($model->status == AuctionEnum::STATUS_MODERATING) {
                if (!isset(Yii::$app->user->identity)) {
                    throw new NotFoundHttpException("Foydanaluvchi aniqlanmadi");
                }
                if ($model->company_id != Yii::$app->user->identity->company_id) {
                    throw new NotFoundHttpException("Lot topilmadi");
                }
            }
            return $this->sendModel($model);
        } catch (\Throwable $th) {
            throw $th;
        }
    }

    public function actionOffers($id)
    {
        $model = $this->findActiveOne($id, false);
        if ($model->status == AuctionEnum::STATUS_MODERATING) {
            if (!isset(Yii::$app->user->identity)) {
                throw new NotFoundHttpException("Foydanaluvchi aniqlanmadi");
            }
            if ($model->company_id != Yii::$app->user->identity->company_id) {
                throw new NotFoundHttpException("Lot topilmadi");
            }
        }

        return $this->sendResponse(
            new AuctionOfferFilter($model),
            Yii::$app->request->queryParams,
        );
    }

    public function actionAuctionViewWithMyOffer($id)
    {
        try {
            $model = $this->findActiveOne($id, false);
            if (AuctionOffer::find()->where(['auction_id' => $model->id, 'company_id' => Yii::$app->user->identity->company_id])->exists())
                return $this->sendModel($model);
            else
                return $this->sendModel(null);
        } catch (\Throwable $th) {
            throw $th;
        }
    }


    // qaysi Auksionlarga offer jo'natganligini ko'rish uchun.
    public function actionMyTrades()
    {
        return $this->sendResponse(
            new MyTradesFilter(),
            Yii::$app->request->queryParams,
        );
    }


    public function actionOffer($id)
    {
        return $this->sendResponse(
            new AuctionOfferForm($this->findActiveOne($id)),
            Yii::$app->request->bodyParams,
        );
    }

    // asosiy page, list
    public function actionActive()
    {
        //auction/auction/active
        return $this->sendResponse(
            new AuctionActiveFilter(),
            Yii::$app->request->queryParams,
        );
    }

    //amalga oshgan lotlar
    public function actionRealized()
    {
        //auction/auction/realized
        return $this->sendResponse(
            new AuctionActiveFilter(),
            Yii::$app->request->queryParams,
        );
    }
}
