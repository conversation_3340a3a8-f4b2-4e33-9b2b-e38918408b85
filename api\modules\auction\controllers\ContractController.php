<?php

namespace api\modules\auction\controllers;

use api\components\ApiController;
use api\modules\auction\filters\ContractFilter;
use common\enums\ContractEnum;
use Yii;

class ContractController extends ApiController
{
    public function actionIndex(): array
    {
        return $this->sendResponse(
            new ContractFilter(ContractEnum::TYPE_USER_PRODUCER),
            Yii::$app->request->queryParams
        );
    }
}