<?php

namespace api\modules\auction\controllers;

use Yii;
use api\modules\auction\forms\draft\SendToModeratorForm;
use api\modules\auction\resources\AuctionResource;
use common\enums\PkcsEnum;
use api\modules\auction\filters\draft\DraftFilter;
use api\modules\auction\forms\draft\AuctionDraftUpdateForm;
use api\modules\auction\forms\draft\AuctionDraftDeleteForm;
use api\modules\auction\resources\draft\AuctionDraftResource;
use api\components\ApiController;
use api\modules\auction\forms\draft\AuctionDraftForm;
use yii\db\Exception;
use yii\web\NotFoundHttpException;

class DraftController extends ApiController
{
    public function actionIndex(): array
    {
        return $this->sendResponse(
            new DraftFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionCreate(): array
    {
        return $this->sendResponse(
            new AuctionDraftForm(new AuctionDraftResource()),
            Yii::$app->request->bodyParams,
        );
    }

    /**
     * @throws NotFoundHttpException
     */
    public function actionView($id): array
    {
        return $this->sendModel($this->findOne($id));
    }

    /**
     * @throws NotFoundHttpException
     */
    public function actionUpdate($id): array
    {
        return $this->sendResponse(
            new AuctionDraftUpdateForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    /**
     * @throws Exception
     */
    public function actionSendToModerator(): array
    {
        $body = Yii::$app->request->bodyParams;
//        $decodedPks7 = $this->verifyPkcs7($body);

        return $this->sendResponsePost(
            new SendToModeratorForm(new AuctionResource()),
            $body,
            $body['pkcs7'],
            PkcsEnum::PKCS7_TYPE_AUCTION_DRAFT_SEND_MODERATOR
        );
    }

    /**
     * @throws NotFoundHttpException
     */
    public function actionDelete($id): array
    {
        return $this->sendResponse(
            new AuctionDraftDeleteForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    /**
     * @throws NotFoundHttpException
     */
    private function findOne($id): AuctionDraftResource
    {
        $model = AuctionDraftResource::findOne(['id' => $id]);
        if (!$model) throw new NotFoundHttpException("Auction Draft not found");
        return $model;
    }
}