<?php

namespace api\modules\auction\filters;

use Yii;
use api\components\BaseRequest;
use api\modules\auction\resources\AuctionListResource;
use common\enums\AuctionEnum;

class AuctionFilter extends BaseRequest
{
    public ?int $status = null;
    public ?string $date = null;
    public ?string $search = null;

    public function rules(): array
    {
        return [
            [['status'], 'integer', 'message' => t("{attribute} qiymati son bo'lishi kerak")],

            ['date', 'date', 'format' => 'php:d/m/Y', 'message' => t("Sana formati noto'g'ri")],

            ['search', 'string', 'max' => 255, 'message' => t("Qidiruv maydoni juda uzun")],
            ['search', 'safe'],
            ['status', 'in', 'range' => AuctionEnum::LIST, 'message' => t("Status qiymati noto'g'ri")],
            [['search',], 'trim'],
        ];
    }

    public function getResult()
    {
        $user = Yii::$app->user->identity;

        $query = AuctionListResource::find()->where(['company_id' => $user->company_id]);

        if ($user->isBudget) {
            $query->andWhere(['organ' => $user->organ]);
        }

        $start_date = $this->date . " 00:00:00";
        $end_date = $this->date . " 23:59:59";

        if ($this->date){
            $query->andWhere(['between' , 'created_at' , $start_date , $end_date]);
        }

        if ($this->search) {
            $query->andWhere([
                'or',
                ['lot' => $this->search],
                ['total_sum' => is_numeric($this->search) ? $this->search * 100 : -1],
            ]);
        }
        if ($this->status) {
            $query->andWhere(['status' => $this->status]);
        }
        $query = $query->orderBy("auction.id desc");
        return paginate($query);
    }
}
