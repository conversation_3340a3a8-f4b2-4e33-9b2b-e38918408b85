<?php

namespace api\modules\auction\filters;

use api\components\BaseRequest;
use api\modules\auction\resources\AuctionClassifierResource;
use api\modules\auction\resources\AuctionResource;
use api\modules\common\resources\ClassifierCategoryResource;
use api\modules\common\resources\ClassifierResource;
use api\modules\shop\resources\ContractAuctionResource;
use api\modules\shop\resources\ContractResource;
use common\enums\ContractEnum;

class ContractFilter extends BaseRequest
{
    public ?string $search = null;

    public function rules(): array
    {
        return [
            [['search'], 'safe']
        ];
    }

    public function __construct(
        public ?int $type = ContractEnum::TYPE_USER_PRODUCER,
        $params = []
    )
    {
        parent::__construct($params);
    }

    public function getResult()
    {
        $company_id = \Yii::$app->user->identity->company_id;
        $model = ContractAuctionResource::find()->where(ContractResource::tableName() .'.auction_id is not null');
        if ($this->type == ContractEnum::TYPE_USER_PRODUCER) {
            $model->andWhere(['producer_id' => $company_id]);
        } else {
            $model->andWhere(['customer_id' => $company_id]);
        }

        if ($this->search) {
            $model->leftJoin(AuctionResource::tableName(), AuctionResource::tableName() . '.id' . '=' . ContractResource::tableName() . '.auction_id');
            $model->leftJoin(AuctionClassifierResource::tableName(), AuctionClassifierResource::tableName() . '.auction_id' . '=' . AuctionResource::tableName() . '.id');
            $model->leftJoin(ClassifierResource::tableName(), ClassifierResource::tableName() . '.id' . '=' . AuctionClassifierResource::tableName() . '.classifier_id');
            $model->leftJoin(ClassifierCategoryResource::tableName(), ClassifierCategoryResource::tableName() . '.id' . '=' . ClassifierResource::tableName() . '.classifier_category_id');

            $model->orWhere(['like', ClassifierCategoryResource::tableName() . '.title_ru', $this->search]);
            $model->orWhere(['like', ClassifierCategoryResource::tableName() . '.title_uz', $this->search]);
            $model->orWhere(['like', ClassifierCategoryResource::tableName() . '.title_uzk', $this->search]);
            $model->orWhere(['like', ClassifierCategoryResource::tableName() . '.title_en', $this->search]);
            $model->orWhere(['like', ClassifierCategoryResource::tableName() . '.code', $this->search]);
            $model->orWhere(['like', AuctionResource::tableName() . '.lot', $this->search]);
        }
        $model->orderBy([ContractResource::tableName() . '.created_at' => SORT_DESC]);
        return paginate($model);
    }
}
