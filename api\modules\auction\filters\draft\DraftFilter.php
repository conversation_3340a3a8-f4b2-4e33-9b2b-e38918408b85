<?php

namespace api\modules\auction\filters\draft;

use api\components\BaseRequest;
use api\modules\auction\resources\draft\AuctionDraftResource;
use yii\db\ActiveQuery;

class DraftFilter extends BaseRequest
{
    public ?string $search = null;

    public function rules(): array
    {
        return [
            [['search', 'date',], 'safe'],
        ];
    }

    #[\Override]
    public function getResult(): array
    {
        $query = self::baseQuery()
            ->leftJoin('auction_classifier_draft', 'auction_classifier_draft.auction_id=auction_draft.id')
            ->leftJoin('classifier', 'classifier.id=auction_classifier_draft.auction_id');
        if ($this->search != null) {
            $query->andWhere([
                'or',
                ['classifier.title' => $this->search],
                ['auction_draft.id' => $this->search],
                ['auction_draft.total_sum' => is_numeric($this->search) ? $this->search * 100 : -1],
            ]);
        }
        return paginate($query);
    }

    protected static function baseQuery(): ActiveQuery
    {
        return AuctionDraftResource::find();
    }
}