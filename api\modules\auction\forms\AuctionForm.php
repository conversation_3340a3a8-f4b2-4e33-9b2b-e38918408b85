<?php

namespace api\modules\auction\forms;

use api\components\BaseRequest;
use api\modules\auction\resources\AuctionResource;
use api\modules\common\resources\ClassifierResource;
use api\modules\common\resources\PlanScheduleClassifierCreateResource;
use api\modules\common\resources\PlanScheduleResource;
use common\enums\AuctionEnum;
use common\enums\CompanyEnum;
use common\enums\StatusEnum;
use common\models\auction\AuctionClassifier;
use common\models\auction\AuctionCondition;
use common\models\auction\AuctionFile;
use common\models\auction\AuctionHistory;
use common\models\Bmh;
use common\models\Company;
use Yii;

class AuctionForm extends BaseRequest
{
    public AuctionResource $model;

    public $plan_schedule_id;
    public $delivery_period;
    public $address;
    public $account;
    public $delivery_basis;
    public $responsible_person;
    public $responsible_person_phone;
    public $description;
    public $auction_classifiers = [];
    public $auction_files = [];
    public $auction_conditions = [];

    public $region_id;


    public function __construct($params = [])
    {
        $this->model = new AuctionResource();

        parent::__construct($params);
    }

    public function rules()
    {
        $parent = parent::rules();
        $child = [
            [['plan_schedule_id', 'auction_classifiers', 'delivery_period', 'address', 'account', 'delivery_basis', 'responsible_person', 'responsible_person_phone','region_id'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [['plan_schedule_id','region_id'], 'integer'],
            [['auction_classifiers', 'auction_files', 'auction_conditions'], 'safe'],
            [['delivery_basis'], 'string', 'max' => 10],
            [['address', 'responsible_person', 'responsible_person_phone'], 'string', 'max' => 255],
            [['account'], 'string', 'min' => 20, 'max' => 20, 'message' => t("{attribute} uzunligi 20 ta bo'lishi kerak")],
            [['delivery_period',], 'integer', 'min' => 7, 'message' => t("{attribute} eng kichik qiymati 7")],
            [['plan_schedule_id'], 'exist', 'skipOnError' => true, 'targetClass' => PlanScheduleResource::class, 'targetAttribute' => ['plan_schedule_id' => 'id']],
            ['description', 'string']
        ];
        return array_merge($parent, $child);
    }


    public function attributeLabels()
    {
        return [
            'plan_schedule_id' => Yii::t('main', 'Reja jadvali'),
            'auction_classifiers' => Yii::t('main', 'Mahsulotlar'),
            'auction_conditions' => Yii::t('main', 'Ahohida shartlar'),
            'address' => Yii::t('main', 'Yetkazib berish manzili'),
            'auction_files' => Yii::t('main', "Qo'shimcha hujjatlar"),
            'account' => Yii::t('main', 'Hisob raqamni'),
            'delivery_period' => Yii::t('main', 'Yetkazib berish muddati'),
        ];
    }

    public function getResult()
    {
        /**
         * @var $company Company
         */
        $transaction = \Yii::$app->db->beginTransaction();
        $user = Yii::$app->user->identity;
        $company = $user->company;
        $model = $this->model;
        $totalSum = 0;
        $isBudget = $user->isBudget;
        if ($isBudget) {
            $this->addError("error", t("Ruxsat mavjud emas"));
            return false;
        }

        $model->company_id = $company->id;
        $model->status = AuctionEnum::STATUS_MODERATING;
        $model->receiver_email = $user->email;
        $model->receiver_phone = $company->phone;

        $model->delivery_period = $this->delivery_period;
        $model->address = $this->address;
        $model->responsible_person = $this->responsible_person;
        $model->responsible_person_phone = $this->responsible_person_phone;
        $model->account = $this->account;
        $model->delivery_basis = $this->delivery_basis;
        $model->description = $this->description;
        $model->region_id = $this->region_id;
//        $model->plan_schedule_id = $this->plan_schedule_id;

        if (!$model->save()) {
            $this->addErrors($model->errors);
            $transaction->rollBack();
            return false;
        }

        foreach ($this->auction_classifiers as $classifier) {

            if (!isset($classifier['description']) || $classifier['description'] == null) {
                $transaction->rollBack();
                $this->addError("description", t("Reja jadvali tavsifi yuborish kerak"));
                return false;
            }
            if (!isset($classifier['id']) || $classifier['id'] == null) {
                $transaction->rollBack();
                $this->addError("id", t("Reja jadvali id yuborish kerak"));
                return false;
            }

            $clsObject = $classifier['classifier'];
            $cls = null;
            if (isset($clsObject) && $clsObject != null && isset($clsObject['id'])) {
                $cls = ClassifierResource::findOne($clsObject['id']);
            }

            if ($cls === null) {
                $this->addError('classifier_id', t("Maxsulot guruhi topilmadi"));
                $transaction->rollBack();
                return false;
            }

            $planScheduleClassifier = PlanScheduleClassifierCreateResource::find()->where([
                'plan_schedule_id' => $this->plan_schedule_id,
                'classifier_id' => $cls->id,
                'status' => StatusEnum::STATUS_ACTIVE,
                'id' => $classifier['id']
            ])->one();
            if (!$planScheduleClassifier) {

                $this->addError('classifier_id', t("Mahsulot reja jadvali topilmadi"));
                $transaction->rollBack();
                return false;
            }
            $planScheduleClassifier->description = $classifier['description'];

            $auction_classifier = new AuctionClassifier();
            $auction_classifier->setAttributes($classifier);

            $auction_classifier->classifier_id = $cls->id;
            $auction_classifier->description = $planScheduleClassifier->description;
            $auction_classifier->auction_id = $model->id;
            $auction_classifier->plan_schedule_id = $this->plan_schedule_id;
            $auction_classifier->plan_schedule_classifier_id = $planScheduleClassifier->id;
            $auction_classifier->order = isset($classifier['order']) ? $classifier['order'] : 0;

            $auction_classifier->quantity = $classifier['count'];
            $auction_classifier->price = $classifier['price'] * 100;
            $auction_classifier->total_sum = $classifier['count'] * $auction_classifier->price;

            $auction_classifier->status = StatusEnum::STATUS_ACTIVE;

            if (!$auction_classifier->save()) {

                $this->addErrors($auction_classifier->errors);
                $transaction->rollBack();
                return false;
            }

            if ($planScheduleClassifier && $planScheduleClassifier->count_live >= $classifier['count']) {

                $planScheduleClassifier->count_used = $planScheduleClassifier->count_used + $classifier['count'];
                $planScheduleClassifier->count_live = $planScheduleClassifier->count - $planScheduleClassifier->count_used;
                if (!$planScheduleClassifier->save()) {
                    $this->addErrors($planScheduleClassifier->errors);
                    $transaction->rollBack();
                    return false;
                }

//                $planScheduleClassifier->update([
//                    'count_used' => $classifier['count'],
//                    'count_live' => $planScheduleClassifier->count - $classifier['count'],
//                ]);

                if ($planScheduleClassifier->count_live == 0) {
                    $planScheduleClassifier->update([
                        'status' => 400, // not active
                    ]);
                }
            } else {

                $this->addError("error", t("Reja jadvalida maxsulot soni yetarli emas"));
                $transaction->rollBack();
                return false;
            }

            $totalSum += $auction_classifier->total_sum;
        }

        $bmh = Bmh::getAmount();
        if ($company->organization_type == CompanyEnum::NO_BYUDJET) {
            $bmh = $bmh * 25000;

            if ($totalSum >= $bmh) {
                $transaction->rollBack();
                $this->addError("error", t("Tovarlarning qiymati bir shartnoma bo‘yicha bazaviy hisoblash miqdorining yigirma besh ming baravarigacha bo‘lgan miqdorni tashkil etadi"));
                return false;
            }

        } else {
            $bmh = $bmh * 6000;
            if ($totalSum >= $bmh) {
                $transaction->rollBack();
                $this->addError("error", t("Tovarlarning qiymati bir shartnoma bo‘yicha bazaviy hisoblash miqdorining olti ming baravarigacha bo‘lgan miqdorni tashkil etadi"));
                return false;
            }
        }

        foreach ($this->auction_files as $fileId) {

            $auctionFile = new AuctionFile();
            $auctionFile->auction_id = $model->id;
            $auctionFile->file_id = $fileId;
            if (!$auctionFile->save()) {
                $this->addErrors($auctionFile->errors);
                $transaction->rollBack();
                return false;
            }

        }

        if ($this->auction_conditions && count($this->auction_conditions) > 0) {
            foreach ($this->auction_conditions as $condition) {
                $auction_condition = new AuctionCondition();
                $auction_condition->auction_id = $model->id;
                $auction_condition->condition = $condition['condition'];
                $auction_condition->text = $condition['text'];
                $auction_condition->status = StatusEnum::STATUS_ACTIVE;
                if (!$auction_condition->save()) {
                    $this->addErrors($auction_condition->errors);
                    $transaction->rollBack();
                    return false;
                }
            }
        }

        $model->total_sum = $totalSum;
        $model->lot = $model->generateLotNumber($model->id);
        if (!$model->save()) {
            $this->addErrors($model->errors);
            $transaction->rollBack();
            return false;
        }

        $zalog_sum = $model->total_sum * env('ZALOG_PERCENT', 0.03);
        $commission_sum = $model->total_sum * env('COMMISSION_PERCENT', 0.0015);
        $commission_sum = $commission_sum > 1000000 ? 1000000 : $commission_sum;
        $total_block_sum = $zalog_sum + $commission_sum;


        if (!hasMoney($company, $total_block_sum)) {
            $transaction->rollBack();
            $this->addError('error', t('"Недостаточно средств на балансе покупателя."'));
            return false;
        }

        $history = new AuctionHistory();
        $history->auction_id = $model->id;
        $history->user_id = $user->id;
        $history->status = $this->model->status;
        $history->comment = t("Yaratildi. Moderatsiyaga yuborildi.");
        $history->created_at = date("Y-m-d H:i:s");
        if (!$history->save()) {
            $this->addErrors($history->errors);
            $transaction->rollBack();
            return false;
        }

        $transaction->commit();
        return $this->model->id;
    }
}
