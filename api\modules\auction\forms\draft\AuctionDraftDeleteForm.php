<?php

namespace api\modules\auction\forms\draft;

use api\components\BaseRequest;
use api\modules\auction\resources\draft\AuctionDraftResource;
use yii\db\StaleObjectException;

class AuctionDraftDeleteForm extends BaseRequest
{
    public function __construct(
        public ?AuctionDraftResource $model = null,
        $config = [],
    )
    {
        parent::__construct($config);
    }

    /**
     * @throws \Throwable
     * @throws StaleObjectException
     */
    public function getResult(): false|int
    {
        return $this->model->delete();
    }
}