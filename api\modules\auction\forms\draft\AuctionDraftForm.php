<?php

namespace api\modules\auction\forms\draft;

use Yii;
use api\components\BaseRequest;
use api\modules\auction\resources\draft\AuctionDraftResource;
use api\modules\common\resources\ClassifierResource;
use api\modules\common\resources\PlanScheduleClassifierCreateResource;
use api\modules\common\resources\PlanScheduleResource;
use common\enums\StatusEnum;
use common\models\AuctionClassifierDraft;
use yii\web\UnauthorizedHttpException;

class AuctionDraftForm extends BaseRequest
{
    public ?int $plan_schedule_id = null;
    public array $auction_classifiers = [];

    public function __construct(
        protected ?AuctionDraftResource $model = null,
        $config = [],
    )
    {
        parent::__construct($config);
    }

    public function rules(): array
    {
        return [
            [['plan_schedule_id', 'auction_classifiers',], 'required',],
            [['plan_schedule_id',], 'integer'],
            [['auction_classifiers',], 'safe'],
            [['plan_schedule_id'], 'exist', 'skipOnError' => true, 'targetClass' => PlanScheduleResource::class, 'targetAttribute' => ['plan_schedule_id' => 'id']],
        ];
    }

    /**
     * @throws UnauthorizedHttpException
     */
    #[\Override]
    public function getResult(): int
    {
        $identity = Yii::$app->user->identity;
        if (!$identity) throw new UnauthorizedHttpException('Unauthorized');

        $model = $this->model;
        $model->company_id = $identity->company_id;
        $model->plan_schedule_id = $this->plan_schedule_id;
        $transaction = Yii::$app->db->beginTransaction();
        try {
            if (!$model->save())
            {
                $transaction->rollBack();
                $this->addErrors($this->model->errors);
                return false;
            }
            foreach ($this->auction_classifiers as $classifier) {

                if (!isset($classifier['plan_schedule_classifier_id']) || $classifier['plan_schedule_classifier_id'] == null) {
                    $transaction->rollBack();
                    $this->addError("plan_schedule_classifier_id", t("Reja jadvali id yuborish kerak"));
                    return false;
                }

                if (!isset($classifier['count']) || $classifier['count'] == null) {
                    $transaction->rollBack();
                    $this->addError("count", t("Count yuborish kerak"));
                    return false;
                }

                $cls = null;
                if (isset($classifier['classifier_id']) && $classifier['classifier_id'] != null) {
                    $cls = ClassifierResource::findOne($classifier['classifier_id']);
                }

                if ($cls === null) {
                    $this->addError('classifier_id', t("Classifier topilmadi"));
                    $transaction->rollBack();
                    return false;
                }

                $planScheduleClassifier = PlanScheduleClassifierCreateResource::findOne([
                    'plan_schedule_id' => $this->plan_schedule_id,
                    'classifier_id' => $cls->id,
                    'status' => StatusEnum::STATUS_ACTIVE,
                    'id' => $classifier['plan_schedule_classifier_id']
                ]);
                if (!$planScheduleClassifier) {
                    $this->addError('classifier_id', t("Mahsulot reja jadvali topilmadi"));
                    $transaction->rollBack();
                    return false;
                }

                $auction_classifier = new AuctionClassifierDraft();
                $auction_classifier->setAttributes($classifier);

                $auction_classifier->classifier_id = $cls->id;
                $auction_classifier->auction_id = $model->id;
                $auction_classifier->plan_schedule_id = $this->plan_schedule_id;
                $auction_classifier->plan_schedule_classifier_id = $planScheduleClassifier->id;
                $auction_classifier->order = $classifier['order'] ?? 0;

                $auction_classifier->quantity = $classifier['count'];
                $auction_classifier->price = $planScheduleClassifier->tovarprice;
                $auction_classifier->total_sum = $classifier['count'] * $auction_classifier->price;

                if (!$auction_classifier->save()) {
                    $this->addErrors($auction_classifier->errors);
                    $transaction->rollBack();
                    return false;
                }

                if (($planScheduleClassifier->count - $planScheduleClassifier->count_used)  < $classifier['count']) {
                    $transaction->rollBack();
                    $this->addError('classifier_id', t("Reja jadvalida maxsulot soni yetarli emas"));
                    return false;
                }
            }
            $transaction->commit();
            return $model->id;
        } catch (\Throwable $ex) {
            $transaction->rollBack();
            $this->addError('error', $ex->getMessage());
            return false;
        }
    }
}