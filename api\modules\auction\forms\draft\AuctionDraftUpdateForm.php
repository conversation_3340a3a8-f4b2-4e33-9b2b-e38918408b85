<?php

namespace api\modules\auction\forms\draft;

use api\components\BaseRequest;
use api\modules\auction\resources\draft\AuctionDraftResource;
use api\modules\common\resources\ClassifierResource;
use api\modules\common\resources\PlanScheduleClassifierCreateResource;
use api\modules\common\resources\PlanScheduleClassifierUnitDraftResource;
use api\modules\common\resources\PlanScheduleResource;
use common\enums\CompanyEnum;
use common\enums\StatusEnum;
use common\models\AuctionClassifierDraft;
use common\models\AuctionFileDraft;
use common\models\Bmh;
use common\models\ClassifierProperties;
use common\models\Company;
use common\models\Region;
use Yii;
use yii\db\Exception;
use yii\helpers\ArrayHelper;
use yii\web\NotFoundHttpException;

class AuctionDraftUpdateForm extends BaseRequest
{
    public ?string $title = null;
    public ?int $plan_schedule_id = null;
    public array $auction_classifiers = [];
    public ?int $delivery_period = null;
    public ?string $delivery_basis = null;
    public ?string $description = null;
    public array $auction_files = [];
    public ?int $region_id = null;
    public ?int $district_id = null;
    public ?string $address = null;
    public ?string $organization_phone = null;

    public function __construct(
        public ?AuctionDraftResource $model = null,
        $config = [],
    )
    {
        parent::__construct($config);
    }

    public function rules(): array
    {
        return  [
            [['plan_schedule_id', 'auction_classifiers', 'delivery_period', 'title',], 'safe'],
            [['plan_schedule_id','region_id', 'district_id'], 'integer'],
            [['auction_classifiers', 'auction_files',], 'safe'],
            [['delivery_basis'], 'string', 'max' => 10],
            [['address','organization_phone', 'title'], 'string', 'max' => 255],
//            [['account'], 'string', 'min' => 20, 'max' => 20, 'message' => t("{attribute} uzunligi 20 ta bo'lishi kerak")],
            [['delivery_period',], 'integer', 'min' => 7, 'message' => t("{attribute} eng kichik qiymati 7")],
            [['plan_schedule_id'], 'exist', 'skipOnError' => true, 'targetClass' => PlanScheduleResource::class, 'targetAttribute' => ['plan_schedule_id' => 'id']],
            ['description', 'string'],
            ['region_id', 'exist', 'targetClass' => Region::class, 'targetAttribute' => ['region_id' => 'id'], 'filter' => ['type' => Region::TYPE_REGION]],
            ['district_id', 'exist', 'targetClass' => Region::class, 'targetAttribute' => ['district_id' => 'id'], 'filter' => ['type' => Region::TYPE_DISTRICT]],
        ];
    }

    /**
     * @throws \Throwable
     */
    public function getResult()
    {
        /**
         * @var $company Company
         */
        $user = Yii::$app->user->identity;
        $company = $user->company;
        $model = $this->model;
        $totalSum = 0;
        $isBudget = $user->isBudget;
        if ($isBudget) {
            $this->addError("error", t("Ruxsat mavjud emas"));
            return false;
        }

        $model->title = $this->title;
        $model->delivery_period = $this->delivery_period;
        $model->address = $this->address;
        $model->organization_phone = $this->organization_phone;
        $model->description = $this->description;
        $model->region_id = $this->region_id;
        $model->district_id = $this->district_id;
        $model->delivery_basis = $this->delivery_basis;
        $transaction = Yii::$app->db->beginTransaction();
        try {
            if (!$model->save()) {
                $this->addErrors($model->errors);
                $transaction->rollBack();
                return false;
            }
            $oldClassifierIDs = AuctionClassifierDraft::find()->select('id')->where(['auction_id' => $model->id])->column();
            $classifierIDs = ArrayHelper::getColumn($this->auction_classifiers, 'id');
            if (!empty($deletedIDs = array_diff($oldClassifierIDs, $classifierIDs)))
            {
                AuctionClassifierDraft::deleteAll(['id' => $deletedIDs]);
            }
            foreach ($this->auction_classifiers as $classifier) {
                $classifier_units = $classifier['classifier_units'] ?? [];
                $classifierDescription = $classifier['description'] ?? null;
                $count = $classifier['count'] ?? null;

                if (!isset($classifier['plan_schedule_classifier_id']) || $classifier['plan_schedule_classifier_id'] == null) {
                    $transaction->rollBack();
                    $this->addError("plan_schedule_classifier_id", t("Reja jadvali id yuborish kerak"));
                    return false;
                }

                $cls = null;
                if (isset($classifier['classifier_id']) && $classifier['classifier_id'] != null) {
                    $cls = ClassifierResource::findOne($classifier['classifier_id']);
                }

                if ($cls === null) {
                    $this->addError('classifier_id', t("Maxsulot guruhi topilmadi"));
                    $transaction->rollBack();
                    return false;
                }

                $planScheduleClassifier = PlanScheduleClassifierCreateResource::findOne([
                    'plan_schedule_id' => $this->plan_schedule_id,
                    'classifier_id' => $cls->id,
                    'status' => StatusEnum::STATUS_ACTIVE,
                    'id' => $classifier['plan_schedule_classifier_id']
                ]);

                if (!$planScheduleClassifier) {
                    $this->addError('classifier_id', t("Mahsulot reja jadvali topilmadi"));
                    $transaction->rollBack();
                    return false;
                }

                if (($planScheduleClassifier->count - $planScheduleClassifier->count_used)  < $count) {
                    $this->addError("error", t("Reja jadvalida maxsulot soni yetarli emas"));
                    $transaction->rollBack();
                    return false;
                }

                if ($planScheduleClassifier->count_live == 0) {
                    $planScheduleClassifier->update([
                        'status' => StatusEnum::STATUS_NOT_ACTIVE,
                    ]);
                }

                $planScheduleClassifier->description = $classifierDescription;

                if (isset($classifier['id']) && $classifier['id'] != null) {
                    $auction_classifier = AuctionClassifierDraft::findOne($classifier['id']);
                    if (!$auction_classifier) throw new Exception("AuctionClassifier model not found");
                } else {
                    $auction_classifier = new AuctionClassifierDraft([
                        'auction_id' => $this->model->id,
                        'plan_schedule_id' => $this->plan_schedule_id,
                        'plan_schedule_classifier_id' => $classifier['plan_schedule_classifier_id'],
                        'classifier_id' => $cls->id,
                        'classifier_category_id' => $cls->classifier_category_id
                    ]);
                }
                $auction_classifier->description = $planScheduleClassifier->description;
                $auction_classifier->order = $classifier['order'] ?? 0;
                if (is_int($count)) {
                    $auction_classifier->quantity = $count;
                    $auction_classifier->price = $planScheduleClassifier->tovarprice * 100;
                    $auction_classifier->total_sum = $count * $auction_classifier->price;
                }
                if (!$auction_classifier->save()) {
                    $this->addErrors($auction_classifier->errors);
                    $transaction->rollBack();
                    return false;
                }

                foreach ($classifier_units as $unit)
                {
                    $classifierProperties = ClassifierProperties::findOne(['id' => $unit,'status' => StatusEnum::STATUS_ACTIVE]);
                    if (!$classifierProperties) throw new NotFoundHttpException("Klassifikator biriligi topilmadi !!!");

                    $condition = ['plan_schedule_classifier_id' => $planScheduleClassifier->id, 'classifier_property_id' => $classifierProperties->id, 'status' => StatusEnum::STATUS_ACTIVE];
                    $planScheduleClassifierUnitDraft = PlanScheduleClassifierUnitDraftResource::findOne($condition) ?: new PlanScheduleClassifierUnitDraftResource($condition);
                    if (!$planScheduleClassifierUnitDraft->save()) {
                        $transaction->rollBack();
                        $this->addErrors($planScheduleClassifierUnitDraft->errors);
                        return false;
                    }
                }

                $totalSum += $auction_classifier->total_sum;
            }

            $bmh = Bmh::getAmount();
            if ($company->organization_type == CompanyEnum::NO_BYUDJET) {
                $bmh = $bmh * 25000;
                if ($totalSum >= $bmh) {
                    $transaction->rollBack();
                    $this->addError("error", t("Tovarlarning qiymati bir shartnoma bo‘yicha bazaviy hisoblash miqdorining yigirma besh ming baravarigacha bo‘lgan miqdorni tashkil etadi"));
                    return false;
                }
            } else {
                $bmh = $bmh * 6000;
                if ($totalSum >= $bmh) {
                    $transaction->rollBack();
                    $this->addError("error", t("Tovarlarning qiymati bir shartnoma bo‘yicha bazaviy hisoblash miqdorining olti ming baravarigacha bo‘lgan miqdorni tashkil etadi"));
                    return false;
                }
            }

            foreach ($this->auction_files as $fileId) {
                $condition = ['auction_id' => $model->id, 'file_id' => $fileId];
                $auctionFile = AuctionFileDraft::findOne($condition) ?: new AuctionFileDraft($condition);
                if (!$auctionFile->save()) {
                    $this->addErrors($auctionFile->errors);
                    $transaction->rollBack();
                    return false;
                }
            }

            $model->total_sum = $totalSum;
            if (!$model->save()) {
                $this->addErrors($model->errors);
                $transaction->rollBack();
                return false;
            }

            $zalog_sum = $model->total_sum * env('ZALOG_PERCENT', 0.03);
            $commission_sum = $model->total_sum * env('COMMISSION_PERCENT', 0.0015);
            $commission_sum = min($commission_sum, 1000000);
            $total_block_sum = $zalog_sum + $commission_sum;
            if (!hasMoney($company, $total_block_sum)) {
                $transaction->rollBack();
                $this->addError('error', t('"Недостаточно средств на балансе покупателя."'));
                return false;
            }
            $transaction->commit();
            return $this->model;
        } catch (\Throwable $ex) {
            $this->addError('error', $ex->getMessage());
            return false;
        }
    }
}