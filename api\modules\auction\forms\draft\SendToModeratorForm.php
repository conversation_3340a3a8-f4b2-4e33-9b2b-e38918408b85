<?php

namespace api\modules\auction\forms\draft;

use api\modules\common\resources\ClassifierResource;
use api\modules\common\resources\PlanScheduleClassifierUnitResource;
use common\models\ClassifierProperties;
use common\models\Region;
use Yii;
use api\components\BaseRequest;
use api\modules\auction\resources\AuctionResource;
use api\modules\auction\resources\draft\AuctionDraftResource;
use api\modules\common\resources\PlanScheduleClassifierCreateResource;
use common\enums\AuctionEnum;
use common\enums\CompanyEnum;
use common\enums\StatusEnum;
use common\models\auction\AuctionClassifier;
use common\models\auction\AuctionFile;
use common\models\auction\AuctionHistory;
use common\models\Bmh;
use yii\web\NotFoundHttpException;
use yii\web\UnauthorizedHttpException;

class SendToModeratorForm extends BaseRequest
{
    public ?int $id = null;
    public ?string $title = null;
    public ?int $plan_schedule_id = null;
    public array $auction_classifiers = [];
    public ?int $delivery_period = null;
    public ?string $delivery_basis = null;
    public ?string $description = null;
    public array $auction_files = [];
    public ?int $region_id = null;
    public ?int $district_id = null;
    public ?string $address = null;
    public ?string $organization_phone = null;

    public function __construct(
        public ?AuctionResource $model = null,
        $config = [],
    )
    {
        parent::__construct($config);
    }

    public function rules(): array
    {
        return [
            [['id','title','plan_schedule_id', 'auction_classifiers', 'delivery_period', 'delivery_basis', ], 'required'],
            [['description', 'auction_files', 'region_id',  'district_id', 'address','organization_phone'], 'safe'],
            ['delivery_period', 'integer', 'min' => 7],
            ['region_id', 'exist', 'targetClass' => Region::class, 'targetAttribute' => ['region_id' => 'id'], 'filter' => ['type' => Region::TYPE_REGION]],
            ['district_id', 'exist', 'targetClass' => Region::class, 'targetAttribute' => ['district_id' => 'id'], 'filter' => ['type' => Region::TYPE_DISTRICT]],
        ];
    }

    /**
     * @throws NotFoundHttpException
     * @throws UnauthorizedHttpException
     * @throws \Throwable
     */
    #[\Override]
    public function getResult(): bool|int
    {
        $user = \Yii::$app->user->identity;
        if (!$user || !$company = $user->company) throw new UnauthorizedHttpException('Unauthorized');

        $draft = AuctionDraftResource::findOne(['id' => $this->id]);
        if (!$draft) throw new NotFoundHttpException('Черновик не найден');

        $transaction = Yii::$app->db->beginTransaction();

        try {
            $model = $this->model;
            $model->title = $this->title;
            $model->company_id = $company->id;
            $model->status = AuctionEnum::STATUS_MODERATING;
            $model->delivery_period = $this->delivery_period;
            $model->address = $this->address;
            $model->delivery_basis = $this->delivery_basis;
            $model->description = $this->description;
            $model->region_id = $this->region_id;
            $model->district_id = $this->district_id;
            $model->organization_phone = $this->organization_phone;

            if (!$model->save()) {
                $this->addErrors($model->errors);
                $transaction->rollBack();
                return false;
            }
            $total = 0;
            foreach ($this->auction_classifiers as $classifier) {
                if (!isset($classifier['classifier_units']) || $classifier['classifier_units'] == null) {
                    $transaction->rollBack();
                    $this->addError("classifier_units", t("Classifier birliklari yuborish kerak"));
                    return false;
                }

                if (!isset($classifier['description']) || $classifier['description'] == null) {
                    $transaction->rollBack();
                    $this->addError("description", t("Classifier description yuborish kerak"));
                    return false;
                }

                if (!isset($classifier['count']) || $classifier['count'] == null) {
                    $transaction->rollBack();
                    $this->addError("count", t("Classifier count yuborish kerak"));
                    return false;
                }

                if (!isset($classifier['plan_schedule_classifier_id']) || $classifier['plan_schedule_classifier_id'] == null) {
                    $transaction->rollBack();
                    $this->addError("plan_schedule_classifier_id", t("Reja jadvali id yuborish kerak"));
                    return false;
                }

                $cls = null;
                if (isset($classifier['classifier_id']) && $classifier['classifier_id'] != null) {
                    $cls = ClassifierResource::findOne($classifier['classifier_id']);
                }

                if ($cls === null) {
                    $this->addError('classifier_id', t("Maxsulot guruhi topilmadi"));
                    $transaction->rollBack();
                    return false;
                }

                $plan_schedule_classifier_id = $classifier['plan_schedule_classifier_id'];
                $classifier_units = $classifier['classifier_units'];
                $classifier_description = $classifier['description'];
                $classifier_count = $classifier['count'];
                $plan_schedule_id = $this->plan_schedule_id;

                $planScheduleClassifier = PlanScheduleClassifierCreateResource::findOne([
                    'id' => $plan_schedule_classifier_id,
                    'plan_schedule_id' => $plan_schedule_id,
                    'classifier_id' => $classifier['classifier_id'],
                    'status' => StatusEnum::STATUS_ACTIVE,
                ]);

                if (!$planScheduleClassifier) {
                    $transaction->rollBack();
                    $this->addError('plan_schedule_id', t("Mahsulot reja jadvali topilmadi"));
                    return false;
                }

                if (($planScheduleClassifier->count - $planScheduleClassifier->count_used)  < $classifier_count) {
                    $this->addError("error", t("Reja jadvalida maxsulot soni yetarli emas"));
                    $transaction->rollBack();
                    return false;
                }

                if ($planScheduleClassifier->count_live == 0) {
                    $planScheduleClassifier->update([
                        'status' => StatusEnum::STATUS_NOT_ACTIVE,
                    ]);
                }

                $planScheduleClassifier->description = $classifier_description;

                $auction_classifier = new AuctionClassifier();
                $auction_classifier->auction_id = $this->model->id;
                $auction_classifier->plan_schedule_id = $plan_schedule_id;
                $auction_classifier->plan_schedule_classifier_id = $plan_schedule_classifier_id;
                $auction_classifier->classifier_id = $cls->id;
                $auction_classifier->classifier_category_id = $cls->classifier_category_id;
                $auction_classifier->quantity = $classifier_count;
                $auction_classifier->price = $planScheduleClassifier->tovarprice * 100;
                $auction_classifier->total_sum = $auction_classifier->quantity * $auction_classifier->price;
                $auction_classifier->status = StatusEnum::STATUS_ACTIVE;
                if (!$auction_classifier->save()) {
                    $this->addErrors($auction_classifier->errors);
                    $transaction->rollBack();
                    return false;
                }

                if ($planScheduleClassifier->count_live >= $classifier_count) {
                    $planScheduleClassifier->count_used = $planScheduleClassifier->count_used + $classifier_count;
                    $planScheduleClassifier->count_live = $planScheduleClassifier->count - $planScheduleClassifier->count_used;
                    if (!$planScheduleClassifier->save()) {
                        $this->addErrors($planScheduleClassifier->errors);
                        $transaction->rollBack();
                        return false;
                    }
                    if ($planScheduleClassifier->count_live == 0) {
                        $planScheduleClassifier->update([
                            'status' => StatusEnum::STATUS_NOT_ACTIVE, // not active
                        ]);
                    }
                }  else {
                    $this->addError("error", t("Reja jadvalida maxsulot soni yetarli emas"));
                    $transaction->rollBack();
                    return false;
                }

                foreach ($classifier_units as $unit)
                {
                    $classifierProperties = ClassifierProperties::findOne(['id' => $unit,'status' => StatusEnum::STATUS_ACTIVE]);
                    if (!$classifierProperties) throw new NotFoundHttpException("Klassifikator biriligi topilmadi !!!");

                    $params = ['plan_schedule_classifier_id' => $planScheduleClassifier->id, 'classifier_property_id' => $classifierProperties->id, 'status' => StatusEnum::STATUS_ACTIVE];
                    $planScheduleClassifierUnitDraft = new PlanScheduleClassifierUnitResource($params);
                    if (!$planScheduleClassifierUnitDraft->save()) {
                        $transaction->rollBack();
                        $this->addErrors($planScheduleClassifierUnitDraft->errors);
                        return false;
                    }
                }

                $total += $auction_classifier->total_sum;
            }

            $bmh = Bmh::getAmount();
            if ($company->organization_type == CompanyEnum::NO_BYUDJET) {
                $bmh = $bmh * 25000;
                if ($total >= $bmh) {
                    $transaction->rollBack();
                    $this->addError("error", t("Tovarlarning qiymati bir shartnoma bo‘yicha bazaviy hisoblash miqdorining yigirma besh ming baravarigacha bo‘lgan miqdorni tashkil etadi"));
                    return false;
                }
            } else {
                $bmh = $bmh * 6000;
                if ($total >= $bmh) {
                    $transaction->rollBack();
                    $this->addError("error", t("Tovarlarning qiymati bir shartnoma bo‘yicha bazaviy hisoblash miqdorining olti ming baravarigacha bo‘lgan miqdorni tashkil etadi"));
                    return false;
                }
            }

            foreach ($this->auction_files as $fileId) {
                $params = ['auction_id' => $model->id, 'file_id' => $fileId];
                $auctionFile = new AuctionFile($params);
                if (!$auctionFile->save()) {
                    $this->addErrors($auctionFile->errors);
                    $transaction->rollBack();
                    return false;
                }
            }

            $model->total_sum = $total;
            $model->lot = AuctionResource::generateLotNumber($model->id);
            if (!$model->save()) {
                $this->addErrors($model->errors);
                $transaction->rollBack();
                return false;
            }

            $deposit = $model->total_sum * env('ZALOG_PERCENT', 0.03);
            $commission = $model->total_sum * env('COMMISSION_PERCENT', 0.0015);
            $commission = min($commission, 1000000);
            $total_block_sum = $deposit + $commission;

            if (!hasMoney($company, $total_block_sum)) {
                $transaction->rollBack();
                $this->addError('error', t('"Недостаточно средств на балансе покупателя."'));
                return false;
            }

            $history = new AuctionHistory();
            $history->auction_id = $model->id;
            $history->user_id = $user->id;
            $history->status = $model->status;
            $history->comment = t("Chernovikdan asosiyga ko'chirildi. Moderatsiyaga yuborildi.");
            $history->created_at = date("Y-m-d H:i:s");
            if (!$history->save()) {
                $this->addErrors($history->errors);
                $transaction->rollBack();
                return false;
            }

            $transaction->commit();

            return $model->id;
        } catch (\Throwable $exception) {
            $transaction->rollBack();
            $this->addError('error', $exception->getMessage());
            return false;
        }
    }
}