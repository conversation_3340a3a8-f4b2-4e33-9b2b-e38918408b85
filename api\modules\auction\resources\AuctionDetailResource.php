<?php

namespace api\modules\auction\resources;

use api\modules\auction\resources\draft\AuctionDraftResource;

class AuctionDetailResource extends AuctionResource
{
    public function fields(): array
    {
        return [
            'id',
            'title',
            'planSchedule',
            'classifiers',
            'files',
            'company',
            'account',
            'total_sum' => function (AuctionDraftResource $model) {
                return $model->total_sum / 100;
            },
            'commission' => function (AuctionDraftResource $model) {
                return $model->total_sum * env('ZALOG_PERCENT', 0.03) / 100;
            },
            'deposit' => function (AuctionDraftResource $model) {
                return $model->total_sum * env('COMMISSION_PERCENT', 0.0015) / 100;
            },
            'region',
            'district',
            'address',
            'delivery_period',
            'delivery_basis',
            'description',
            'organization_phone',
        ];
    }
}
