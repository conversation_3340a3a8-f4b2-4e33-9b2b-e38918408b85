<?php

namespace api\modules\auction\resources;

class AuctionListResource extends AuctionResource
{
    public function fields(): array
    {
        return [
            'id',
            'title',
            'lot',
            'auction_end',
            'total_sum' => function (AuctionListResource $model) {
                return $model->total_sum / 100;
            },
            'cancel_reason',
            'status'
        ];
    }
}