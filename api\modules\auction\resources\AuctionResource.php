<?php

namespace api\modules\auction\resources;

use api\modules\common\resources\RegionResource;
use app\modules\client\resources\AuctionListResource;
use common\enums\StatusEnum;
use common\models\auction\Auction;
use common\models\auction\AuctionClassifier;
use common\models\auction\AuctionHistory;
use common\models\auction\AuctionOffer;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;

class AuctionResource extends Auction
{
    public function fields(): array
    {
        return [
            'id',
            'title',
            'lot',
            'company',
            'region',
            'district',
            'address',
            'begin_date',
            'auction_end',
            'product_count' => 'auctionProductCount',
            'participant_count' => 'auctionRequestCount',
            'total_sum' => function (AuctionResource $model) {
                return $model->total_sum / 100;
            },
            'current_price' => 'currentPrice',
        ];
    }

    public function getAuctionProductCount(): bool|int|string|null
    {
        return AuctionClassifier::find()->where(['auction_id' => $this->id, 'status' => StatusEnum::STATUS_ACTIVE])->count();
    }

    public function getAuctionRequestCount(): bool|int|string|null
    {
        return AuctionOffer::find()->where(['auction_id' => $this->id])->count();
    }

    /**
     * @return ActiveQuery
     */
    public function getRegion(): ActiveQuery
    {
        return $this->hasOne(RegionResource::class, ['id' => 'region_id']);
    }

    public function getAuctionClassifiers(): ActiveQuery|array
    {
        return AuctionClassifierResource::find()->where(['auction_id' => $this->id, 'status' => StatusEnum::STATUS_ACTIVE])->all();
    }

    public function getHistory(): array
    {
        return AuctionHistory::find()->where(['auction_id' => $this->id])->orderBy(['created_at' => SORT_ASC])->all();
    }

    public function getModerating(): array|ActiveRecord|null
    {
        return TenderModeratorLogResource::find()->where(['auction_id' => $this->id])->orderBy(['created_at' => SORT_ASC])->one();
    }

    public function getAuctionFiles(): array
    {
        return AuctionFileResource::find()->where(['auction_id' => $this->id, 'deleted_at' => null])->orderBy(['created_at' => SORT_ASC])->all();
    }

    public function getDistrict(): ActiveQuery
    {
        return $this->hasOne(RegionResource::class, ['id' => 'district_id']);
    }
}
