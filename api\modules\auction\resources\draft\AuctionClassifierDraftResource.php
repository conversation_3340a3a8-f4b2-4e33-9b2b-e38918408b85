<?php

namespace api\modules\auction\resources\draft;

use api\modules\common\resources\ClassifierPropertiesResource;
use api\modules\common\resources\ClassifierResource;
use api\modules\common\resources\PlanScheduleClassifierResource;
use common\enums\StatusEnum;
use common\models\AuctionClassifierDraft;

class AuctionClassifierDraftResource extends AuctionClassifierDraft
{
    public function fields(): array
    {
        return [
            'id',
            'auction_id',
            'plan_schedule_id',
            'planScheduleClassifier',
            'classifierUnits',
            'classifier',
            'quantity',
            'price' => function (AuctionClassifierDraftResource $model) {
                return $model->price / 100;
            },
            'total_sum' => function (AuctionClassifierDraftResource $model) {
                return $model->total_sum / 100;
            },
            'order',
            'description',
        ];
    }

    public function getClassifierUnits()
    {
        if (!$this->plan_schedule_classifier_id)
            return null;

        return ClassifierPropertiesResource::find()
            ->leftJoin('plan_schedule_classifier_unit_draft', 'plan_schedule_classifier_unit_draft.classifier_property_id=classifier_properties.id')
            ->where(['plan_schedule_classifier_id' => $this->plan_schedule_classifier_id, 'plan_schedule_classifier_unit_draft.status' => StatusEnum::STATUS_ACTIVE])->all();
    }

    public function getClassifier()
    {
        return $this->hasOne(ClassifierResource::class, ['id' => 'classifier_id']);
    }

    public function getPlanScheduleClassifier()
    {
        return $this->hasOne(PlanScheduleClassifierResource::class, ['id' => 'plan_schedule_classifier_id']);
    }
}