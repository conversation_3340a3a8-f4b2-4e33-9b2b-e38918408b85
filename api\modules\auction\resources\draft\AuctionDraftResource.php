<?php

namespace api\modules\auction\resources\draft;

use api\modules\common\resources\PlanScheduleResource;
use app\modules\auction\resources\CompanyResource;
use common\models\AuctionDraft;

class AuctionDraftResource extends AuctionDraft
{
    public function fields(): array
    {
        return [
            'id',
            'title',
            'planSchedule',
            'classifiers',
            'files',
            'company',
            'account',
            'total_sum' => function (AuctionDraftResource $model) {
                return $model->total_sum / 100;
            },
            'commission' => function (AuctionDraftResource $model) {
                return $model->total_sum * env('ZALOG_PERCENT', 0.03) / 100;
            },
            'deposit' => function (AuctionDraftResource $model) {
                return $model->total_sum * env('COMMISSION_PERCENT', 0.0015) / 100;
            },
            'region_id',
            'district_id',
            'address',
            'delivery_period',
            'delivery_basis',
            'description',
            'organization_phone',
        ];
    }

    /**
     * Gets query for [[AuctionClassifierDrafts]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getAuctionClassifier()
    {
        return $this->hasMany(AuctionClassifierDraftResource::class, ['auction_id' => 'id']);
    }

    public function getCompany()
    {
        return $this->hasOne(CompanyResource::class, ['id' => 'company_id']);
    }

    public function getFiles()
    {
        return $this->hasMany(AuctionDraftFileResource::class, ['auction_id' => 'id']);
    }

    public function getClassifiers()
    {
        return $this->hasMany(AuctionClassifierDraftResource::class, ['auction_id' => 'id']);
    }

    public function getPlanSchedule()
    {
        return $this->hasOne(PlanScheduleResource::class, ['id' => 'plan_schedule_id']);
    }
}