<?php

namespace app\modules\auth\forms;

use Yii;
use api\components\BaseRequest;
use Dotenv\Exception\ValidationException;
use yii\web\UnauthorizedHttpException;

class CheckPhoneForm extends BaseRequest
{
    public ?string $phone;
    public function rules(): array
    {
        return [
            [['phone'], 'required'],
            [['phone'], 'trim'],
            [['phone'], 'match', 'pattern' => '/^\+998\s\d{2}\s\d{3}\s\d{2}\s\d{2}$/', 'message' => t('Неверный формат номера телефона. Используйте +998 xx xxx xx xx.')],
        ];
    }

    /**
     * @throws UnauthorizedHttpException
     */
    public function getResult(): bool
    {
        $identity = Yii::$app->user->identity;
        if (!$identity || !($company = $identity->company)) {
            throw new UnauthorizedHttpException('Unauthorized');
        }
        $this->phone = clear_phone_full($this->phone);
        if (!$this->phone) {
            throw new ValidationException('Invalid phone number');
        }
        if ($company->phone != $this->phone)
        {
            return false;
        }
        return $company->is_phone_confirmed;
    }
}