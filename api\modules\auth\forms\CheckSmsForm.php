<?php

namespace app\modules\auth\forms;

use api\components\BaseRequest;
use api\modules\common\resources\SmsResource;
use common\enums\StatusEnum;
use common\models\Sms;
use Yii;
use yii\web\UnauthorizedHttpException;

class CheckSmsForm extends BaseRequest
{
    public ?string $code = null;
    public ?string $phone = null;
    public function rules(): array
    {
        return [
            [['phone','code'], 'required'],
            [['phone', 'code'], 'trim'],
            [['phone'], 'match', 'pattern' => '/^\+998\s\d{2}\s\d{3}\s\d{2}\s\d{2}$/', 'message' => t('Неверный формат номера телефона. Используйте +998 xx xxx xx xx.')],
        ];
    }

    /**
     * @throws UnauthorizedHttpException
     */
    public function getResult(): bool
    {
        $identity = Yii::$app->user->identity;
        $phone = clear_phone_full($this->phone);
        if (!$identity || !($company = $identity->company)) {
            throw new UnauthorizedHttpException('Unauthorized.');
        }
        /** @var SmsResource $sms */
        $sms = SmsResource::getLastActiveSmsCode($phone);
        if (!$sms) {
            $this->addError('code', 'Код SMS не найден.');
            return false;
        }
        if ($sms->checkPhoneBlocked())
        {
            $this->addError('code', 'Множество неудачных попыток.');
            return false;
        }
        $transaction = Yii::$app->db->beginTransaction();
        try {
            if ($sms->created_at <= strtotime('-' . env('SMS_CONFIRM_TIME') .  ' seconds')) {
                $sms->updateAttributes(['status' => StatusEnum::STATUS_DELETED]);
                $this->addError('code', 'Время ввода СМС-кода истекло.');
                return false;
            }
            if ($sms->code != $this->code) {
                $sms->updateAttributes(['last_failed_attempt' => time(), 'failed_attempt' => $sms->failed_attempt + 1]);
                $this->addError('code', 'Неправильный код');
                $transaction->commit();
                return false;
            }
            $sms->updateAttributes(['status' => StatusEnum::STATUS_CHANGED]);
            $company->updateAttributes(['phone' => $this->phone, 'is_phone_confirmed' => true]);
            $transaction->commit();
            return true;
        } catch (\Throwable $e) {
            $transaction->rollBack();
            $this->addError('code', $e->getMessage());
            return false;
        }
    }
}