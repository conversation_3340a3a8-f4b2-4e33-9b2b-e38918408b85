<?php

namespace app\modules\auth\forms;

use api\components\BaseRequest;
use api\modules\common\resources\SmsResource;
use common\enums\SmsEnum;
use common\enums\StatusEnum;
use common\models\Sms;
use common\services\SmsService;
use Yii;
use yii\base\InvalidConfigException;
use yii\db\Exception;
use yii\web\UnauthorizedHttpException;

class SendSms extends BaseRequest
{
    public ?string $phone;
    
    public function __construct(
        protected ?SmsResource $model = null,
        $config = [],
    )
    {
        parent::__construct($config);
    }

    public function rules(): array
    {
        return [
            [['phone'], 'required'],
            [['phone'], 'trim'],
            [['phone'], 'match', 'pattern' => '/^\+998\s\d{2}\s\d{3}\s\d{2}\s\d{2}$/', 'message' => t('Неверный формат номера телефона. Используйте +998 xx xxx xx xx.')],
        ];
    }

    /**
     * @throws UnauthorizedHttpException
     * @throws Exception
     */
    public function getResult(): bool
    {
        $lang = Yii::$app->language;
        $phone = clear_phone_full($this->phone);
        $identity = Yii::$app->user->identity;
        if (!$identity || !($company = $identity->company)) {
            throw new UnauthorizedHttpException('Unauthorized');
        }
        if (clear_phone_full($company->phone) == $phone && $company->is_phone_confirmed) {
            $this->addError('phone','Phone already confirmed');
            return false;
        }
        $check = Sms::find()
            ->where(['phone' => $phone])
            ->andWhere(['>', 'created_at', strtotime('-' . env('SMS_CONFIRM_TIME', 180) . ' seconds')])
            ->exists();
        if ($check) {
            $this->addError('phone','Sms already sent');
            return false;
        }
        $text = $lang == 'ru' ? SmsEnum::CONFIRM_TEXT_RU : SmsEnum::CONFIRM_TEXT_UZ;
        $code = rand(1001, 9999);
        $message = SmsService::generateSmsMessage($text, $code);
        $this->model->user_id = $identity->id;
        $this->model->phone = $phone;
        $this->model->code = $code;
        $this->model->message = $message;
        $this->model->status = StatusEnum::STATUS_NEW;
        $this->model->created_at = time();
        $this->model->updated_at = time();
        $transaction = Yii::$app->db->beginTransaction();
        try {
            if (!$this->model->save()) {
                $this->addErrors($this->model->errors);
                return false;
            }
            if (SmsService::sendSms($this->model->phone, $this->model->message)) {
                $transaction->commit();
                return true;
            }
            $transaction->rollBack();
        } catch (\Throwable $e) {
            $transaction->rollBack();
            $this->addError('phone', $e->getMessage());
        }
        return false;
    }
}