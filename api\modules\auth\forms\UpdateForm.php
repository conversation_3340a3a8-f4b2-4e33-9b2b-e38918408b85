<?php

namespace api\modules\auth\forms;

use api\components\BaseRequest;
use api\modules\auth\resources\UserResource;
use common\enums\CompanyEnum;
use common\enums\UserEnum;
use common\models\Company;
use common\models\CompanyBankAccount;
use common\models\EconomicActivitiesType;
use common\models\OrganizationLegalForm;
use common\models\Region;
use common\models\User;
use Exception;
use Yii;

class UpdateForm extends BaseRequest
{
    public $email;
    public $phone;
    public $password;
    public $confirmPassword;
    public $district_id;
    public $address;

    public function rules()
    {
        return [
            [
                [
                    'email',
                    'phone',
                    'district_id',
                    'address'
                ],
                'required',
            ],
            [
                [
                    'email',
                    'phone',
                    'password',
                    'confirmPassword',
                    'address'
                ],
                'trim',
            ],
            [
                [
                    'email',
                    'phone',
                    'password',
                    'confirmPassword',
                    'address',
                ],
                'string',
            ],
            [
                ['email'],
                'email',
            ],
            [
                ['password'],
                'string',
                'min' => 6,
            ],
            [
                ['password'],
                'compare',
                'compareAttribute' => 'confirmPassword',
            ],
            [
                ['phone'],
                'string',
                'min' => 9,
                'max' => 13,
            ],
        ];
    }

    public function getResult()
    {
        try {
            $user = Yii::$app->user->identity;
            $company = $user->company;

            if ($company) {


                if($company->district_id != $this->district_id){
                    $district = Region::findOne($this->district_id);
                    if($company->region_id != $district->parent_id){
                        $this->addError("error", t("Yuridik manzilni (viloyatni) o'zgartirish mumkin emas"));
                        return false;
                    }
                }

                $transaction = Yii::$app->db->beginTransaction();


                $company->phone = $this->phone;
                $company->district_id = $this->district_id;
                $company->address = $this->address;
                if (!$company->is_phone_confirmed || $company->isAttributeChanged('phone')) {
                    $this->addError('phone', 'Telefon raqamni tasdiqlash kerak.');
                }

                if (!$company->save(false)) {
                    $transaction->rollBack();
                    throw new Exception('Company save error');
                }

                $user->email = $this->email;
                if ($this->password != null) {
                    $user->setPassword($this->password);
                }

                if (!$user->save(false)) {
                    $transaction->rollBack();
                    throw new Exception('User save error');
                }
                $transaction->commit();
                return true;
            } else {
                throw new Exception(t("Korxona topilmadi"));
            }

        } catch (\Throwable $th) {
            throw $th;
        }
    }

    protected function getAccessToken()
    {
        $access_token = Yii::$app->security->generateRandomString(40);
        $this->user->access_token = $access_token;

        if (!$this->user->save(false)) {
            throw new Exception('User access token save error');
        }

        return $access_token;
    }
}
