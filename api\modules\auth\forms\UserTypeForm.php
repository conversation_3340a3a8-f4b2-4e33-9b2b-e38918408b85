<?php

namespace app\modules\auth\forms;

use api\modules\common\resources\PlanScheduleResource;
use common\enums\StatusEnum;
use common\enums\UserEnum;
use Yii;
use api\components\BaseRequest;
use api\modules\auth\resources\UserResource;
use common\models\UserToken;
use yii\db\Exception;
use yii\web\UnauthorizedHttpException;

class UserTypeForm extends BaseRequest
{
    /**
     * @throws Exception
     * @throws UnauthorizedHttpException
     */
    public function getResult()
    {
        /** @var UserResource $user */
        $user = Yii::$app->user->identity;

        if (!$user) {
            throw new UnauthorizedHttpException("Avval tizimga kiring");
        }

        if (!is_null($user->user_type) && is_int($user->user_type)) {
            // Foydalanuvchi turini almashtirish
            $user->user_type = ($user->user_type == UserEnum::USER_TYPE_CUSTOMER)
                ? UserEnum::USER_TYPE_SUPPLIER
                : UserEnum::USER_TYPE_CUSTOMER;

            $quarters = PlanScheduleResource::find()
                ->notDeleted()
                ->where(['company_id' => $user->company_id])
                ->exists();

            if (!$quarters) {
                $transaction = Yii::$app->db->beginTransaction();
                try {
                    $this->createQuarters();
                    $user->save();
                    $transaction->commit();
                    return true;
                } catch (\Throwable $e) {
                    $transaction->rollBack();
                    throw $e;
                }
            }

            return $user->save();
        }
    }

    private function createQuarters():bool
    {
        $count = 4;
        for ($i = 1;$i <= $count;$i++){
            $quarter = new PlanScheduleResource([
                'company_id' => Yii::$app->user->identity->company_id,
                'quarter' => $i,
                'status' => StatusEnum::STATUS_ACTIVE,
                'year' => date('Y'),
                'title' => date('Y') . " " . $i . " - chorak uchun plan grafik",
            ]);
            if (!$quarter->save()){
                throw new Exception("Plan grafik yaratishd xatolik");
            }
        }

        return true;
    }
}
