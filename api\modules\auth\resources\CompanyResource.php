<?php

namespace api\modules\auth\resources;

use api\modules\common\resources\DistrictResource;
use api\modules\common\resources\RegionResource;
use common\models\CompanyProfile;

/**
 * <AUTHOR> <<EMAIL>>
 */
class CompanyResource extends \common\models\Company
{
    public function fields()
    {
        return [
            'title', 'tin', 'pinfl', 'id', 'address', 'region', 'district', 'phone', 'is_phone_confirmed','is_phone_confirmed','director', 'updated_at', 'resident',
            'organization_type', 'profiles',
            'available' => function (CompanyResource $model) {
                return $model->availableBalance / 100;
            },
            'blocked' => function (CompanyResource $model) {
                return $model->blockedBalance / 100;
            }
        ];
    }
    
    public function extraFields()
    {
        return [
            'economicActivitiesType', 'organizationLegalForm',
        ];
    }

    public function getRegion()
    {
        return $this->hasOne(RegionResource::class, ['id' => 'region_id']);
    }

    public function getDistrict()
    {
        return $this->hasOne(DistrictResource::class, ['id' => 'district_id']);
    }

    public function getProfiles()
    {
        $profiles = CompanyProfileResource::find()->where(['company_id' => $this->id])->all();
        return $profiles;
    }

}
