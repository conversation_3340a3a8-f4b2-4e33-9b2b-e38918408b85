<?php

namespace api\modules\auth\resources;

use api\modules\tender\resources\CommissionMemberResource;
use backend\modules\rbac\models\RbacAuthAssignment;
use common\enums\RoleEnum;
use common\enums\TenderEnum;
use common\models\Company;
use common\models\CompanyBankAccount;

/**
 * <AUTHOR> <<EMAIL>>
 */
class UserResource extends \common\models\User
{
    public function fields()
    {
        return [
            'id', 'username', 'access_token', 'email',
            'type' => function ($model) {
                if ($model->role == 'commission') {
                    return ['commission'];
                }
                $company = $model->company;
                if ($company != null && $company->organization_type == Company::BUDJET) {
                    $account = CompanyBankAccount::findOne(['company_id' => $model->company_id, 'type' => CompanyBankAccount::TYPE_CORPORATIVE]);
                    if ($account) {
                        return ['customer', 'producer'];
                    } else {
                        return ['customer'];
                    }
                } else {
                    return ['customer', 'producer'];
                }
            },
            'company' => function ($model) {
                $company = $model->company;

                if ($model->role && $model->role->item_name == RoleEnum::ROLE_COMMISSION) {

                    return [
                        'user' => CommissionMemberResource::findOne(['user_id' => $model->id, 'status' => TenderEnum::STATUS_ACTIVE]),
                        'company' => [
                            'title' => $company->title,
                            'tin' => $company->tin,
                            'address' => $company->address,
                            'phone' => $company->phone,
                            'is_phone_confirmed' => $company->is_phone_confirmed,
                            'district' => $company->district,
                            'region' => $company->region,
                        ],
                    ];
                } else {
                    return $company;
                }
            },
            'role' => function ($model) {
                return $model->role ? $model->role->item_name : null;
            },
            'user_type'
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getUserProfile()
    {
        return $this->hasOne(UserProfileResource::class, ['user_id' => 'id']);
    }

    public function getRole()
    {
        return $this->hasOne(RbacAuthAssignment::class, ['user_id' => 'id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getCompany()
    {
        return $this->hasOne(CompanyResource::class, ['id' => 'company_id']);
    }
}
