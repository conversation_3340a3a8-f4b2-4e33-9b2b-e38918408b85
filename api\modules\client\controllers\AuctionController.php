<?php

namespace api\modules\client\controllers;

use api\components\ApiController;
use api\modules\client\resources\AuctionDetailResource;
use app\modules\client\filters\AuctionFilter;
use common\enums\StatusEnum;
use Yii;
use yii\filters\auth\HttpBearerAuth;
use yii\web\NotFoundHttpException;

class AuctionController extends ApiController
{
    public function behaviors(): array
    {
        $parent = parent::behaviors();
        $parent['bearerAuth'] = [
            'class' => HttpBearerAuth::class,
            'except' => ['index', 'view']
        ];
        return $parent;
    }

    public function actionIndex(): array
    {
        return $this->sendResponse(
            new AuctionFilter(),
            Yii::$app->request->queryParams
        );
    }

    /**
     * @throws NotFoundHttpException
     */
    public function actionView($id): array
    {
        return $this->sendModel($this->findDetailOne($id));
    }

    /**
     * @throws NotFoundHttpException
     */
    private function findDetailOne($id): AuctionDetailResource
    {
        $model = AuctionDetailResource::findOne(['id' => $id, 'status' => StatusEnum::STATUS_ACTIVE]);
        if (!$model) throw new NotFoundHttpException("Resource not found");
        return $model;
    }
}