<?php

namespace api\modules\client\controllers;

use api\components\ApiController;
use api\modules\client\filters\DateTimeFilter;
use Yii;
use yii\filters\auth\HttpBearerAuth;

class CommonController extends ApiController
{
    public function behaviors(): array
    {
        $parent = parent::behaviors();
        $parent['bearerAuth'] = [
            'class' => HttpBearerAuth::class,
            'except' => ['date-time']
        ];
        return $parent;
    }

    public function actionDateTime(): array
    {
        return $this->sendResponse(
            new DateTimeFilter(),
            Yii::$app->request->queryParams
        );
    }
}