<?php

namespace api\modules\client\controllers;

use api\components\ApiController;
use api\modules\client\filters\ProductFilter;
use api\modules\client\resources\ProductResource;
use api\modules\client\resources\ProductViewResource;
use common\enums\ProductEnum;
use Yii;
use yii\filters\auth\HttpBearerAuth;
use yii\web\NotFoundHttpException;

class EShopController extends ApiController
{
    public function behaviors(): array
    {
        $parent = parent::behaviors();
        $parent['bearerAuth'] = [
             'class' => HttpBearerAuth::class,
             'except' => ['index','view']
        ];
        return $parent;
    }

    public function actionIndex(): array
    {
        return $this->sendResponse(
            new ProductFilter(ProductEnum::PLATFORM_DISPLAY_E_SHOP),
            Yii::$app->request->queryParams
        );
    }

    /**
     * @throws NotFoundHttpException
     */
    public function actionView($id): array
    {
        return $this->sendModel($this->findProductOne($id));
    }

    /**
     * @throws NotFoundHttpException
     */
    private function findProductOne($id): ProductViewResource
    {
        $model = ProductViewResource::findOne(['id' => $id, 'platform_display' => ProductEnum::PLATFORM_DISPLAY_E_SHOP]);
        if (!$model) throw new NotFoundHttpException("Product not found");
        return $model;
    }
}