<?php

namespace api\modules\client\controllers;

use api\modules\client\resources\ProductViewResource;
use Yii;
use api\components\ApiController;
use api\modules\client\filters\ProductFilter;
use api\modules\client\resources\ProductResource;
use common\enums\ProductEnum;
use yii\filters\auth\HttpBearerAuth;
use yii\web\NotFoundHttpException;

class NShopController extends ApiController
{
    public function behaviors(): array
    {
        $parent = parent::behaviors();
        $parent['bearerAuth'] = [
            'class' => HttpBearerAuth::class,
            'except' => ['index','view']
        ];
        return $parent;
    }

    public function actionIndex(): array
    {
        return $this->sendResponse(
            new ProductFilter(ProductEnum::PLATFORM_DISPLAY_NATIONAL),
            Yii::$app->request->queryParams
        );
    }

    /**
     * @throws NotFoundHttpException
     */
    public function actionView($id): array
    {
        return $this->sendModel($this->findProductOne($id));
    }

    /**
     * @throws NotFoundHttpException
     */
    private function findProductOne($id): ProductViewResource
    {
        $model = ProductViewResource::findOne(['id' => $id, 'platform_display' => ProductEnum::PLATFORM_DISPLAY_NATIONAL]);
        if (!$model) throw new NotFoundHttpException("Product not found");
        return $model;
    }
}