<?php


namespace api\modules\client\controllers;


use api\components\ApiController;
use api\modules\client\resources\OrderResource;
use api\modules\client\forms\OrderForm;
use common\enums\PkcsEnum;
use Yii;

class OrderController extends ApiController
{
    public function behaviors(): array
    {
        $parent = parent::behaviors();
        $parent['bearerAuth'] = [
            'class' => \yii\filters\auth\HttpBearerAuth::class,
            'except' => [],
        ];
        return $parent;
    }

    public function actionCreate()
    {
        $body = \Yii::$app->request->bodyParams;
        $decodedPks7 = $this->verifyPkcs7($body);

        return $this->sendResponsePost(
            new OrderForm(new OrderResource()),
            $decodedPks7,
            $body['pkcs7'] ?? null,
            $body,
            PkcsEnum::PKCS7_TYPE_SHOP_ORDER_CREATE
        );
    }
}