<?php


namespace api\modules\client\controllers;


use api\components\ApiController;
use api\modules\client\filters\RegionFilter;
use api\modules\client\filters\RegionWithDistrictFilter;
use Yii;

class RegionController extends ApiController
{
    public function actionRegionList()
    {
        return $this->sendResponse(
            new RegionFilter(),
            Yii::$app->request->queryParams
        );
    }
    public function actionRegionWithDistrict()
    {
        return $this->sendResponse(
            new RegionWithDistrictFilter(),
            Yii::$app->request->queryParams
        );
    }
}