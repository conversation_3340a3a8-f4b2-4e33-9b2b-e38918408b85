<?php

namespace api\modules\client\controllers;

use api\modules\client\filters\LoginStatisticFilter;
use Yii;
use api\components\ApiController;
use api\modules\client\filters\ContractStatisticFilter;
use yii\filters\auth\HttpBearerAuth;

class StatisticController extends ApiController
{
    public function behaviors(): array
    {
        $parent = parent::behaviors();
        $parent['bearerAuth'] = [
            'class' => HttpBearerAuth::class,
            'except' => ['index', 'login-statistic']
        ];
        return $parent;
    }

    public function actionIndex(): array
    {
        return $this->sendResponse(
            new ContractStatisticFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionLoginStatistic(): array
    {
        return $this->sendResponse(
            new LoginStatisticFilter(),
            Yii::$app->request->queryParams
        );
    }
}