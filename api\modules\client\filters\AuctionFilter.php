<?php

namespace app\modules\client\filters;

use api\components\BaseRequest;
use app\modules\client\resources\AuctionListResource;
use common\enums\AuctionEnum;
use DateTime;

class AuctionFilter extends BaseRequest
{
    public ?string $search = null;
    public ?string $title = null;
    public ?string $lot = null;
    public ?int $from_price = null;
    public ?int $to_price = null;
    public ?int $region_id = null;
    public ?int $district_id = null;
    public ?string $tin = null;
    public ?int $classifier_category_id = null;
    public ?int $classifier_id = null;
    public ?string $from_date = null;
    public ?string $end_date = null;

    public function rules(): array
    {
        return [
            [['title', 'search', 'lot', 'from_price', 'to_price', 'region_id', 'district_id', 'tin', 'classifier_category_id', 'classifier_id', 'from_date', 'end_date'], 'safe'],
            [['from_date','end_date'],'date','format' => 'php:d/m/Y'],
        ];
    }

    #[\Override]
    public function getResult(): array
    {
        $query = AuctionListResource::find()->where(['status' => AuctionEnum::STATUS_ACTIVE]);

        $query->andFilterWhere([
            'lot' => $this->lot,
            'title' => $this->title,
            'region_id' => $this->region_id,
            'district_id' => $this->district_id,
        ]);
        if ($this->search) {
            $query->andWhere([
                'or',
                ['lot' => $this->search],
                ['title' => $this->search],
            ]);
        }

        if ($this->from_price !== null) {
            $query->andWhere(['>=', 'total_sum', $this->from_price * 100]);
        }
        if ($this->to_price !== null) {
            $query->andWhere(['<=', 'total_sum', $this->to_price * 100]);
        }


        if ($this->tin) {
            $query->joinWith(['company' => function ($q) {
                $q->andWhere(['tin' => trim($this->tin)]);
            }]);
        }

        if ($this->from_date) {
            $fromDate = $this->formatDate($this->from_date);
            $query->andWhere(['>=', 'auction_end', $fromDate]);
        }
        if ($this->end_date) {
            $endDate = $this->formatDate($this->end_date);
            $query->andWhere(['<=', 'auction_end', $endDate]); // Fixed: <= instead of >=
        }

        if ($this->classifier_category_id || $this->classifier_id) {
            $query->joinWith(['auctionClassifiers' => function ($q) {
                $q->andWhere([
                    'or',
                    ['classifier_category_id' => $this->classifier_category_id],
                    ['classifier_id' => $this->classifier_id],
                ]);
            }]);
        }
        return paginate($query);
    }

    private function formatDate(string $date): ?string
    {
        $dateTime = DateTime::createFromFormat('d/m/Y H:i:s', $date . ' 00:00:00');
        return $dateTime ? $dateTime->format('Y-m-d H:i:s') : null;
    }
}