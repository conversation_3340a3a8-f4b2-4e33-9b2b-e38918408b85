<?php

namespace api\modules\client\filters;

use api\components\BaseRequest;
use common\enums\ContractEnum;
use common\models\Contract;
use Yii;
use yii\caching\DbDependency;
use yii\db\Query;
use yii\helpers\ArrayHelper;

class ContractStatisticFilter extends BaseRequest
{
    public ?int $year = null;
    public ?string $procedure = null;

    public function rules(): array
    {
        return [
            [['procedure','year'], 'required'],
            ['procedure', 'string', 'max' => 255],
            ['procedure', 'in', 'range' => ContractEnum::PROCEDURE_TYPE_LIST],
            ['year', 'integer', 'min' => 1970, 'max' => date('Y')]
        ];
    }

    public function getResult(): array
    {
        $cache         = Yii::$app->cache;
        $cacheDuration = 86400;
        $dependency    = new DbDependency(['sql' => "SELECT MAX(updated_at) FROM " . Contract::tableName()]);

        $diagrams = $cache->getOrSet('landing_diagram', function () {
            return $this->getDiagrams();
        }, $cacheDuration, $dependency);

        $diagramMap = ArrayHelper::map($diagrams, 'month', 'count');
        $diagram    = array_replace(array_fill(1, 12, 0), $diagramMap);

        $statistics = $cache->getOrSet('landing_statistic', function () {
            return $this->getStatistic();
        }, $cacheDuration, $dependency);

        $_statistics = array_combine(array_column($statistics, 'procedure_type'), $statistics);

        $statistic = [];
        foreach (ContractEnum::PROCEDURE_TYPE_LIST as $procedure)
        {
            if (array_key_exists($procedure, $_statistics)) {
                $_statistics[$procedure]['total'] /= 100;
                $statistic[] = $_statistics[$procedure];
            } else {
                $statistic[] = [
                    'procedure_type' => $procedure,
                    'count' => 0,
                    'total' => 0
                ];
            }
        }
        return [
            'diagram' => $diagram,
            'statistic' => $statistic
        ];
    }

    private function getDiagrams(): array
    {
        return (new Query())
            ->select("EXTRACT(MONTH FROM created_at) AS month, COUNT(*) AS count")
            ->from(Contract::tableName())
            ->where("procedure_type = '{$this->procedure}' AND EXTRACT(YEAR FROM created_at) = {$this->year}")
            ->groupBy("EXTRACT(MONTH FROM created_at)")
            ->orderBy('month')
            ->all();
    }

    private function getStatistic(): array
    {
        return (new Query())
            ->select("procedure_type, COUNT(*) AS count, SUM(price) as total")
            ->from('contract')
            ->where("EXTRACT(YEAR FROM created_at) = {$this->year}")
            ->groupBy('procedure_type')
            ->orderBy(['procedure_type' => SORT_ASC])
            ->all();
    }
}