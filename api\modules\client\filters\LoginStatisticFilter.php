<?php

namespace api\modules\client\filters;

use api\components\BaseRequest;
use common\enums\ContractEnum;
use yii\db\Query;

class LoginStatisticFilter extends BaseRequest
{

    #[\Override] public function getResult(): array
    {
        $beforeSixMonth = date('Y-m-d H:i:s', strtotime('-6 months'));
        $result = (new Query())
            ->select("COUNT(*) as count, SUM(price) as price")
            ->from('contract')
            ->where([
                'or',
                ['status' => ContractEnum::STATUS_DONE],
                ['status' => ContractEnum::STATUS_RECEIVED_PRODUCT],
            ])
            ->andWhere(['>=', 'created_at', $beforeSixMonth])->one();
        $total = (new Query())
            ->from('contract')
            ->where([
                'or',
                ['status' => ContractEnum::STATUS_DONE],
                ['status' => ContractEnum::STATUS_RECEIVED_PRODUCT],
            ])->count();
        $count = $result['count'] ?? 0;
        $price = $result['price'] ? $result['price'] / 100 : 0;
        $percent = ($count * 100) / $total;

         return [
            'count' => $count,
            'price' => $price,
            'percent' => $percent
        ];
    }
}