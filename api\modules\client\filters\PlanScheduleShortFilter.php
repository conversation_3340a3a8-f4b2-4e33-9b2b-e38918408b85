<?php


namespace api\modules\client\filters;


use api\components\BaseRequest;
use api\modules\client\resources\PlanScheduleShortResource;
use Exception;

class PlanScheduleShortFilter extends BaseRequest
{
    public $classifier_id;

    public function rules()
    {
        return [
            [['classifier_id'], 'safe']
        ];
    }
    public function getResult()
    {

        $query = PlanScheduleShortResource::find()->notDeletedAndFromCompany();

        if ($this->classifier_id){
            $query->leftJoin('plan_schedule_classifier','plan_schedule_classifier.plan_schedule_id=plan_schedule.id');
            $query->andWhere(['plan_schedule_classifier.classifier_id'=>$this->classifier_id]);
        }
        if ($query->exists()) {
            return $query->all();
        } else {
            throw new Exception(t("Sizda reja jadval mavjud emas!!!"));
        }
    }
}