<?php

namespace api\modules\client\filters;

use api\components\BaseRequest;
use api\modules\client\resources\ProductResource;
use common\enums\ProductEnum;

class ProductFilter extends BaseRequest
{
    public ?string $search = null;
    public function __construct(
        protected string $type = ProductEnum::PLATFORM_DISPLAY_E_SHOP,
        $config = [],
    )
    {
        parent::__construct($config);
    }

    public function rules(): array
    {
        return [
            ['search', 'safe']
        ];
    }

    public function getResult(): array
    {
        $query = ProductResource::find()->joinWith('order')
            ->where(['product.status' => ProductEnum::SHOP_STATE_ACTIVE, 'platform_display' => $this->type])
            ->andFilterWhere([
                'or',
                ['ilike', 'title', $this->search],
                ['order.lot_number' => $this->search]
            ]);
        return paginate($query);
    }
}