<?php

namespace api\modules\client\forms;

use api\components\BaseRequest;
use api\modules\client\resources\ClassifierResource;
use api\modules\client\resources\OrderResource;
use api\modules\common\resources\PlanScheduleClassifierResource;
use api\modules\common\resources\VirtualTransactionResource;
use common\enums\CompanyEnum;
use common\enums\OperationTypeEnum;
use common\enums\ProductEnum;
use common\enums\ShopEnum;
use common\enums\StatusEnum;
use common\enums\UserEnum;
use common\models\Bmh;
use common\models\Classifier;
use common\models\Company;
use common\models\CompanyBankAccount;
use common\models\PlanSchedule;
use common\models\shop\OrderList;
use common\models\shop\OrderRegion;
use common\models\shop\OrderRequest;
use common\models\shop\Product;
use common\models\shop\ProductRegion;
use common\models\WorkdayCalendar;
use Yii;
use yii\base\Exception;
use yii\helpers\ArrayHelper;

class OrderForm extends BaseRequest
{
    public OrderResource $model;

    // Asosiy atributlar
    public $product_id;
    public $classifier_id;
    public $plan_schedule_id;
    public $count;
    public $regions = [];
    public $type;

    // Client modulida mavjud bo'lgan atributlar
    public $address;
    public $account_number_id;
    public $pkcs7;

    // Shop modulidan olingan qo'shimcha atributlar (client uchun zarur bo'lganlar)
    public $customer_account_treasury;
    public $expense_item;
    public $receiver_fio;
    public $receiver_phone;
    public $delivery_type;
    public $payment_type;

    public function rules()
    {
        $parent = parent::rules();
        $child = [
            // Majburiy maydonlar
            [['product_id', 'classifier_id', 'plan_schedule_id', 'count'], 'required', 'message' => t('{attribute} yuborish majburiy')],

            // Xavfsiz maydonlar
            [['regions', 'lot_number', 'address'], 'safe'],
            [['receiver_fio', 'receiver_phone', 'delivery_type', 'payment_type'], 'safe'],
            [['customer_account_treasury', 'expense_item'], 'safe'],

            // Integer maydonlar
            [['product_id', 'classifier_id', 'plan_schedule_id', 'count', 'account_number_id'], 'integer'],

            // String maydonlar
            [['address', 'receiver_fio', 'receiver_phone'], 'string', 'max' => 255],
            [['delivery_type', 'payment_type'], 'string', 'max' => 50],

            // Mavjudlik tekshiruvi
            [['product_id'], 'exist', 'skipOnError' => true, 'targetClass' => Product::class, 'targetAttribute' => ['product_id' => 'id']],
            [['classifier_id'], 'exist', 'skipOnError' => true, 'targetClass' => ClassifierResource::class, 'targetAttribute' => ['classifier_id' => 'id']],
            [['plan_schedule_id'], 'exist', 'skipOnError' => true, 'targetClass' => PlanSchedule::class, 'targetAttribute' => ['plan_schedule_id' => 'id']],
//            [['account_number_id'], 'exist', 'skipOnError' => true, 'targetClass' => CompanyBankAccount::class, 'targetAttribute' => ['account_number_id' => 'id']],

            // Byudjet tashkilotlari uchun shartli validatsiya
//            [['account_number_id', 'expense_item'], 'required','message' => "Byudjet tashkilotlari uchun account number yuborish majburiy", 'when' => function ($model) {
//                return \Yii::$app->user->identity->isBudget;
//            }],
//            [['account_number_id'], 'required', 'when' => function ($model) {
//                return !\Yii::$app->user->identity->isBudget;
//            }],

        ];
        return array_merge($parent, $child);
    }


    public function __construct(OrderResource $model, $params = [])
    {
        $this->model = $model;
        parent::__construct($params);
    }

    public function getResult()
    {
        $transaction = \Yii::$app->db->beginTransaction();

        try {
            $user = Yii::$app->user->identity;

            if ($user->user_type != UserEnum::USER_TYPE_CUSTOMER) {
                $this->addError("error", t("Buyurtmachi taklif beroladi"));
                return false;
            }

            $company = $user->company;
            $product = Product::findOne($this->product_id);

            if (!$product) {
                throw new Exception(t("Mahsulot topilmadi"));
            }

            $this->validateOrder($product, $company);

            // Reja grafik tekshiruvi va yangilash
            $plan_schedule = $this->validateAndUpdatePlanSchedule();

            // Buyurtma yaratish
            $orderId = $this->createOrder($product, $company, $user);

            if ($orderId) {
                $transaction->commit();
                return $orderId;
            }

            $transaction->rollBack();
            return false;

        } catch (Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    /**
     * Buyurtma validatsiyasi
     * @param Product $product
     * @param $company
     * @throws Exception
     */
    private function validateOrder($product, $company)
    {
        if ($this->count < $product->min_order || $this->count > $product->max_order) {
            throw new Exception(t("Mahsulot soni noto'g'ri"));
        }

        if (count($this->regions) == 0) {
            throw new Exception(t("Hudud tanlash majburiy"));
        }

        $productRegionIds = ArrayHelper::getColumn(
            ProductRegion::find()->andWhere(['product_id' => $product->id])->all(),
            'region_id'
        );

        if ($product->platform_display == ProductEnum::PLATFORM_DISPLAY_E_SHOP &&
            !in_array($this->regions[0], $productRegionIds)) {
            throw new Exception(t("Siz tanlagan hududga yetkazib berilmaydi"));
        }

        if ($product->platform_display == ProductEnum::PLATFORM_DISPLAY_NATIONAL &&
            $company->region_id != $product->company->region_id) {
            throw new Exception(t("Yetkazib beruvchi siz bilan bir hududda emas!!!"));
        }

        if ($this->count > $product->quantity) {
            throw new Exception(t("Mahsulot sonidan ortiqcha buyurtma qilyapsiz"));
        }

        $this->validateBalanceAndBmh($product, $company);
    }

    /**
     * Balans va BMH tekshiruvi
     * @param Product $product
     * @param $company
     * @throws Exception
     */
    private function validateBalanceAndBmh($product, $company)
    {
        // Zalog va komissiya hisoblash
        $zalog = $this->count * $product->unit_price * env('SHOP_ZALOG_PERSENT', 0.03);
        $commission = $this->count * $product->unit_price * env('COMMISSION_PERCENT', 0.0015);

        if ($commission >= env('SHOP_CUSTOMER_MAX_COMMISSION_SUM', 1000000)) {
            $commission = env('SHOP_CUSTOMER_MAX_COMMISSION_SUM', 1000000);
        }

        $total_block_sum = $zalog + $commission;
        if (!hasMoney($company, $total_block_sum)) {
            throw new Exception(t("Balansda yetarli mablag' mavjud emas"));
        }

        // BMH tekshiruvi
        $bmh = Bmh::getAmount();
        $price = $product->unit_price * $this->count;

        if ($product->type == ProductEnum::PRODUCT_TYPE_PRODUCT) {
            if ($company->organization_type == CompanyEnum::NO_BYUDJET) {
                $bmh = $bmh * 25000;
                if ($price > $bmh) {
                    throw new Exception(t("Tovarlarning qiymati bir shartnoma bo'yicha bazaviy hisoblash miqdorining yigirma besh ming baravarigacha bo'lgan miqdorni tashkil etadi"));
                }
            } else {
                $bmh = $bmh * 2500;
                if ($price > $bmh) {
                    throw new Exception(t("Tovarlarning qiymati bir shartnoma bo'yicha bazaviy hisoblash miqdorining ikki yarim ming baravarigacha bo'lgan miqdorni tashkil etadi"));
                }
            }
        }

        if ($product->type == ProductEnum::PRODUCT_TYPE_SERVICE) {
            if ($company->organization_type == CompanyEnum::NO_BYUDJET) {
                $bmh = $bmh * 100;
                if ($price > $bmh) {
                    throw new Exception(t("Tovarlarning qiymati bir shartnoma bo'yicha bazaviy hisoblash miqdorining yuz baravarigacha bo'lgan miqdorni tashkil etadi"));
                }
            } else {
                $bmh = $bmh * 50;
                if ($price > $bmh) {
                    throw new Exception(t("Tovarlarning qiymati bir shartnoma bo'yicha bazaviy hisoblash miqdorining ellik baravarigacha bo'lgan miqdorni tashkil etadi"));
                }
            }
        }
    }

    private function validateAndUpdatePlanSchedule()
    {
        $plan_schedule = PlanScheduleClassifierResource::find()
            ->andWhere(['plan_schedule_id' => $this->plan_schedule_id])
            ->andWhere(['classifier_id' => $this->classifier_id])
            ->one();

        if (!$plan_schedule) {
            throw new Exception(t("Reja grafik topilmadi"));
        }

        if ($plan_schedule->count_live < $this->count) {
            throw new Exception(t("Reja grafikdagi joriy miqdordan ko'p kiritdingiz"));
        }

        $plan_schedule->updateAttributes([
            'count_used' => $this->count,
            'count_live' => $plan_schedule->count - $this->count,
        ]);

        if ($plan_schedule->count_live == 0) {
            $plan_schedule->updateAttributes([
                'status' => StatusEnum::STATUS_NOT_ACTIVE,
            ]);
        }

        return $plan_schedule;
    }

    private function createOrder($product, $company, $user)
    {
        // Ish kunlari va bayramlar
        $holidays = ArrayHelper::map(
            WorkdayCalendar::findAll(['type' => WorkdayCalendar::HOLIDAY]),
            'local_date',
            'local_date'
        );
        $workDays = ArrayHelper::map(
            WorkdayCalendar::findAll(['type' => WorkdayCalendar::WORKDAY]),
            'local_date',
            'local_date'
        );

        // Model atributlarini to'ldirish
        $this->model->total_sum = $product->unit_price * $this->count;
        $this->model->company_id = $product->company_id;
        $this->model->user_id = \Yii::$app->user->id;
        $this->model->status = $user->isBudget ? ShopEnum::ORDER_STATUS_WAITING : ShopEnum::ORDER_STATUS_ACTIVE;
        $this->model->created_at = date("Y-m-d H:i:s");
        $this->model->begin_date = startWorkDay(date("Y-m-d H:i:s"), $workDays, $holidays);
        $endDate = addDaysExcludingWeekends($this->model->begin_date, 2, $workDays, $holidays);
        $this->model->request_end = $endDate;

        // Form atributlarini modelga o'tkazish
        $att = $this->attributes;
        $this->model->setAttributes($att, false);

        if ($this->model->validate() && $this->model->save()) {
            $orderId = $this->model->id;

            // Virtual transactionlarni yaratish
            $this->createVirtualTransactions($orderId, $product, $company);

            // Hududlarni saqlash
            $this->saveOrderRegions($orderId);

            // Buyurtma ro'yxatini yaratish
            $this->createOrderList($orderId, $product);

            // Buyurtma so'rovini yaratish
            $this->createOrderRequest($orderId, $product);

            // Mahsulot miqdorini kamaytirish
            $product->updateAttributes(['quantity' => $product->quantity - $this->count]);

            // Lot raqamini yaratish
            $this->model->lot_number = OrderResource::generateLotNumber($this->model->id);
            if (!$this->model->save()) {
                throw new Exception("Lot raqamini saqlashda xatolik");
            }

            // Klassifikator statistikasini yangilash
            $classifier = Classifier::findOne($product->classifier_id);
            if ($classifier) {
                $classifier->updateAttributes(['saled' => $classifier->saled + 1]);
            }

            return $orderId;
        }

        $this->addErrors($this->model->errors);
        return false;
    }

    /**
     * Buyurtma hududlarini saqlash
     * @param int $orderId
     * @throws Exception
     */
    private function saveOrderRegions($orderId)
    {
        foreach ($this->regions as $region) {
            $orderRegion = new OrderRegion();
            $orderRegion->region_id = $region;
            $orderRegion->order_id = $orderId;

            if (!($orderRegion->validate() && $orderRegion->save())) {
                throw new Exception("Hududlarni saqlashda xatolik: " . json_encode($orderRegion->errors));
            }
        }
    }

    /**
     * Buyurtma ro'yxatini yaratish
     * @param int $orderId
     * @param Product $product
     * @throws Exception
     */
    private function createOrderList($orderId, $product)
    {
        $cache = [
            'product' => array_merge(
                $product->attributes,
                [
                    'category_name' => $product->classifierCategory->title_uz ?? '',
                    'description' => $product->description_uz ?? ''
                ]
            ),
        ];

        $orderList = new OrderList([
            'order_id' => $orderId,
            'product_id' => $product->id,
            'quantity' => $this->count,
            'price' => $this->model->total_sum,
            'cache' => json_encode($cache)
        ]);

        if (!($orderList->validate() && $orderList->save())) {
            throw new Exception("Buyurtma ro'yxatini saqlashda xatolik: " . json_encode($orderList->errors));
        }
    }

    /**
     * Buyurtma so'rovini yaratish
     * @param int $orderId
     * @param Product $product
     * @throws Exception
     */
    private function createOrderRequest($orderId, $product)
    {
        $orderRequest = new OrderRequest([
            'company_id' => $product->company_id,
            'price' => $this->count * $product->unit_price,
            'order_id' => $orderId,
            'created_at' => date('Y-m-d H:i:s'),
            'is_winner' => 0,
            'status' => ShopEnum::ORDER_REQUEST_STATUS_DONE,
            'type' => ShopEnum::ORDER_REQUEST_TYPE_PRODUCER,
        ]);

        if (!($orderRequest->validate() && $orderRequest->save())) {
            throw new Exception("Buyurtma so'rovini saqlashda xatolik: " . json_encode($orderRequest->errors));
        }
    }

    private function createVirtualTransactions($orderId, $product, $company)
    {
        // Zalog va komissiya hisoblash
        $zalog = $this->count * $product->unit_price * env('SHOP_ZALOG_PERSENT', 0.03);
        $commission = $this->count * $product->unit_price * env('COMMISSION_PERCENT', 0.0015);

        if ($commission >= env('SHOP_CUSTOMER_MAX_COMMISSION_SUM', 1000000)) {
            $commission = env('SHOP_CUSTOMER_MAX_COMMISSION_SUM', 1000000);
        }

        try {
            // Buyurtmachi uchun zalog bloklash
            VirtualTransactionResource::saveTransaction(
                $company,
                $company,
                OperationTypeEnum::P_K_30101,
                OperationTypeEnum::P_K_30201,
                $zalog,
                Yii::t("main", "Mahsulotga narx so'rovi uchun zalog bandlandi"),
                OperationTypeEnum::PRODUCT_NAME_ORDER,
                $orderId,
                null,
                OperationTypeEnum::BLOCK_SALE_DEPOSIT
            );

            // Buyurtmachi uchun komissiya bloklash
            VirtualTransactionResource::saveTransaction(
                $company,
                $company,
                OperationTypeEnum::P_K_30101,
                OperationTypeEnum::P_K_30202,
                $commission,
                Yii::t("main", "Mahsulotga narx so'rovi uchun kommissiya bandlandi"),
                OperationTypeEnum::PRODUCT_NAME_ORDER,
                $orderId,
                null,
                OperationTypeEnum::BLOCK_SALE_COMMISSION
            );

            // Yetkazib beruvchi uchun zalog bloklash
            VirtualTransactionResource::saveTransaction(
                $product->company,
                $product->company,
                OperationTypeEnum::P_K_30101,
                OperationTypeEnum::P_K_30201,
                $zalog,
                Yii::t("main", "Maxsulotga narx so'rovi berilgani uchun garov bandlandi"),
                OperationTypeEnum::PRODUCT_NAME_ORDER,
                $orderId,
                null,
                OperationTypeEnum::BLOCK_SALE_DEPOSIT
            );

            // Yetkazib beruvchi uchun komissiya bloklash
            VirtualTransactionResource::saveTransaction(
                $product->company,
                $product->company,
                OperationTypeEnum::P_K_30101,
                OperationTypeEnum::P_K_30202,
                $commission,
                Yii::t("main", "Maxsulotga narx so'rovi berilgani uchun kommissiya bandlandi"),
                OperationTypeEnum::PRODUCT_NAME_ORDER,
                $orderId,
                null,
                OperationTypeEnum::BLOCK_SALE_COMMISSION
            );

        } catch (Exception $ex) {
            throw new Exception("Virtual transaction yaratishda xatolik: " . $ex->getMessage());
        }
    }
}