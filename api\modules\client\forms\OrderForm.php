<?php

namespace api\modules\client\forms;

use api\components\BaseRequest;
use api\modules\client\resources\ClassifierResource;
use api\modules\client\resources\OrderResource;
use api\modules\common\resources\PlanScheduleClassifierResource;
use api\modules\common\resources\VirtualTransactionResource;
use common\enums\CompanyEnum;
use common\enums\OperationTypeEnum;
use common\enums\ProductEnum;
use common\enums\ShopEnum;
use common\enums\StatusEnum;
use common\enums\UserEnum;
use common\models\Bmh;
use common\models\Classifier;
use common\models\CompanyBankAccount;
use common\models\PlanSchedule;
use common\models\shop\OrderList;
use common\models\shop\OrderRegion;
use common\models\shop\OrderRequest;
use common\models\shop\Product;
use common\models\shop\ProductRegion;
use common\models\WorkdayCalendar;
use Yii;
use yii\base\Exception;
use yii\helpers\ArrayHelper;

class OrderForm extends BaseRequest
{
    public OrderResource $model;

    // Asosiy atributlar
    public $product_id;
    public $classifier_id;
    public $plan_schedule_id;
    public $count;
    public $regions = [];

    // Client modulida mavjud bo'lgan atributlar
    public $address;
    public $account_number_id;
    public $pkcs7;

    // Shop modulidan olingan qo'shimcha atributlar (client uchun zarur bo'lganlar)
    public $customer_account_treasury;
    public $expense_item;
    public $receiver_fio;
    public $receiver_phone;
    public $delivery_type;
    public $payment_type;

    public function rules()
    {
        $parent = parent::rules();
        $child = [
            // Majburiy maydonlar
            [['product_id', 'classifier_id', 'plan_schedule_id', 'count'], 'required', 'message' => t('{attribute} yuborish majburiy')],

            // Xavfsiz maydonlar
            [['regions', 'lot_number', 'address', 'pkcs7'], 'safe'],
            [['receiver_fio', 'receiver_phone', 'delivery_type', 'payment_type'], 'safe'],
            [['customer_account_treasury', 'expense_item'], 'safe'],

            // Integer maydonlar
            [['product_id', 'classifier_id', 'plan_schedule_id', 'count', 'account_number_id'], 'integer'],

            // String maydonlar
            [['address', 'receiver_fio', 'receiver_phone'], 'string', 'max' => 255],
            [['delivery_type', 'payment_type'], 'string', 'max' => 50],

            // Mavjudlik tekshiruvi
            [['product_id'], 'exist', 'skipOnError' => true, 'targetClass' => Product::class, 'targetAttribute' => ['product_id' => 'id']],
            [['classifier_id'], 'exist', 'skipOnError' => true, 'targetClass' => ClassifierResource::class, 'targetAttribute' => ['classifier_id' => 'id']],
            [['plan_schedule_id'], 'exist', 'skipOnError' => true, 'targetClass' => PlanSchedule::class, 'targetAttribute' => ['plan_schedule_id' => 'id']],
            [['account_number_id'], 'exist', 'skipOnError' => true, 'targetClass' => CompanyBankAccount::class, 'targetAttribute' => ['account_number_id' => 'id']],

            // Byudjet tashkilotlari uchun shartli validatsiya
            [['account_number_id', 'expense_item'], 'required', 'when' => function ($model) {
                return \Yii::$app->user->identity->isBudget;
            }],
            [['account_number_id'], 'required', 'when' => function ($model) {
                return !\Yii::$app->user->identity->isBudget;
            }],

            // Qo'shimcha validatsiyalar
            [['count'], 'integer', 'min' => 1],
            [['regions'], 'each', 'rule' => ['integer']],
        ];
        return array_merge($parent, $child);
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'product_id' => t('Mahsulot'),
            'classifier_id' => t('Klassifikator'),
            'plan_schedule_id' => t('Reja grafik'),
            'count' => t('Miqdor'),
            'regions' => t('Hududlar'),
            'address' => t('Manzil'),
            'account_number_id' => t('Hisob raqam'),
            'customer_account_treasury' => t('Mijoz g\'azna hisobi'),
            'expense_item' => t('Xarajat moddasi'),
            'receiver_fio' => t('Qabul qiluvchi F.I.O'),
            'receiver_phone' => t('Qabul qiluvchi telefon'),
            'delivery_type' => t('Yetkazib berish turi'),
            'payment_type' => t('To\'lov turi'),
            'pkcs7' => t('Elektron imzo'),
        ];
    }

    public function __construct(OrderResource $model, $params = [])
    {
        $this->model = $model;
        parent::__construct($params);
    }

    /**
     * Buyurtma yaratish (Client moduli uchun soddalashtirilgan versiya)
     * @return int|false
     * @throws Exception
     */
    public function getResult()
    {
        $transaction = \Yii::$app->db->beginTransaction();

        try {
            $user = Yii::$app->user->identity;

            // Foydalanuvchi turi tekshiruvi
            if ($user->user_type != UserEnum::USER_TYPE_CUSTOMER) {
                $this->addError("error", t("Buyurtmachi taklif beroladi"));
                return false;
            }

            $company = $user->company;
            $product = Product::findOne($this->product_id);

            if (!$product) {
                throw new Exception(t("Mahsulot topilmadi"));
            }

            // Asosiy validatsiyalar
            $this->validateOrder($product, $company);

            // Reja grafik tekshiruvi va yangilash
            $plan_schedule = $this->validateAndUpdatePlanSchedule();

            // Buyurtma yaratish
            $orderId = $this->createOrder($product, $company, $user);

            if ($orderId) {
                $transaction->commit();
                return $orderId;
            }

            $transaction->rollBack();
            return false;

        } catch (Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    /**
     * Buyurtma validatsiyasi
     * @param Product $product
     * @param $company
     * @throws Exception
     */
    private function validateOrder($product, $company)
    {
        // Mahsulot miqdori tekshiruvi
        if ($this->count < $product->min_order || $this->count > $product->max_order) {
            throw new Exception(t("Mahsulot soni noto'g'ri"));
        }

        // Hudud tekshiruvi
        if (count($this->regions) == 0) {
            throw new Exception(t("Hudud tanlash majburiy"));
        }

        $productRegionIds = ArrayHelper::getColumn(
            ProductRegion::find()->andWhere(['product_id' => $product->id])->all(),
            'region_id'
        );

        if ($product->platform_display == ProductEnum::PLATFORM_DISPLAY_E_SHOP &&
            !in_array($this->regions[0], $productRegionIds)) {
            throw new Exception(t("Siz tanlagan hududga yetkazib berilmaydi"));
        }

        if ($product->platform_display == ProductEnum::PLATFORM_DISPLAY_NATIONAL &&
            $company->region_id != $product->company->region_id) {
            throw new Exception(t("Yetkazib beruvchi siz bilan bir hududda emas!!!"));
        }

        if ($this->count > $product->quantity) {
            throw new Exception(t("Mahsulot sonidan ortiqcha buyurtma qilyapsiz"));
        }
    }

    /**
     * Reja grafik tekshiruvi va yangilash
     * @return PlanScheduleClassifierResource
     * @throws Exception
     */
    private function validateAndUpdatePlanSchedule()
    {
        $plan_schedule = PlanScheduleClassifierResource::find()
            ->andWhere(['plan_schedule_id' => $this->plan_schedule_id])
            ->andWhere(['classifier_id' => $this->classifier_id])
            ->one();

        if (!$plan_schedule) {
            throw new Exception(t("Reja grafik topilmadi"));
        }

        if ($plan_schedule->count_live < $this->count) {
            throw new Exception(t("Reja grafikdagi joriy miqdordan ko'p kiritdingiz"));
        }

        $plan_schedule->updateAttributes([
            'count_used' => $this->count,
            'count_live' => $plan_schedule->count - $this->count,
        ]);

        if ($plan_schedule->count_live == 0) {
            $plan_schedule->updateAttributes([
                'status' => StatusEnum::STATUS_NOT_ACTIVE,
            ]);
        }

        return $plan_schedule;
    }

    /**
     * Buyurtma yaratish
     * @param Product $product
     * @param $company
     * @param $user
     * @return int|false
     * @throws Exception
     */
    private function createOrder($product, $company, $user)
    {
        // Ish kunlari va bayramlar
        $holidays = ArrayHelper::map(
            WorkdayCalendar::findAll(['type' => WorkdayCalendar::HOLIDAY]),
            'local_date',
            'local_date'
        );
        $workDays = ArrayHelper::map(
            WorkdayCalendar::findAll(['type' => WorkdayCalendar::WORKDAY]),
            'local_date',
            'local_date'
        );

        // Model atributlarini to'ldirish
        $this->model->total_sum = $product->unit_price * $this->count;
        $this->model->company_id = $product->company_id;
        $this->model->user_id = \Yii::$app->user->id;
        $this->model->status = $user->isBudget ? ShopEnum::ORDER_STATUS_WAITING : ShopEnum::ORDER_STATUS_ACTIVE;
        $this->model->created_at = date("Y-m-d H:i:s");
        $this->model->begin_date = startWorkDay(date("Y-m-d H:i:s"), $workDays, $holidays);
        $endDate = addDaysExcludingWeekends($this->model->begin_date, 2, $workDays, $holidays);
        $this->model->request_end = $endDate;

        // Form atributlarini modelga o'tkazish
        $att = $this->attributes;
        $this->model->setAttributes($att, false);

        if ($this->model->validate() && $this->model->save()) {
            $orderId = $this->model->id;

            // Hududlarni saqlash
            $this->saveOrderRegions($orderId);

            // Buyurtma ro'yxatini yaratish
            $this->createOrderList($orderId, $product);

            // Buyurtma so'rovini yaratish
            $this->createOrderRequest($orderId, $product);

            // Mahsulot miqdorini kamaytirish
            $product->updateAttributes(['quantity' => $product->quantity - $this->count]);

            // Lot raqamini yaratish
            $this->model->lot_number = OrderResource::generateLotNumber($this->model->id);
            if (!$this->model->save()) {
                throw new Exception("Lot raqamini saqlashda xatolik");
            }

            // Klassifikator statistikasini yangilash
            $classifier = Classifier::findOne($product->classifier_id);
            if ($classifier) {
                $classifier->updateAttributes(['saled' => $classifier->saled + 1]);
            }

            return $orderId;
        }

        $this->addErrors($this->model->errors);
        return false;
    }

    /**
     * Buyurtma hududlarini saqlash
     * @param int $orderId
     * @throws Exception
     */
    private function saveOrderRegions($orderId)
    {
        foreach ($this->regions as $region) {
            $orderRegion = new OrderRegion();
            $orderRegion->region_id = $region;
            $orderRegion->order_id = $orderId;

            if (!($orderRegion->validate() && $orderRegion->save())) {
                throw new Exception("Hududlarni saqlashda xatolik: " . json_encode($orderRegion->errors));
            }
        }
    }

    /**
     * Buyurtma ro'yxatini yaratish
     * @param int $orderId
     * @param Product $product
     * @throws Exception
     */
    private function createOrderList($orderId, $product)
    {
        $cache = [
            'product' => array_merge(
                $product->attributes,
                [
                    'category_name' => $product->classifierCategory->title_uz ?? '',
                    'description' => $product->description_uz ?? ''
                ]
            ),
        ];

        $orderList = new OrderList([
            'order_id' => $orderId,
            'product_id' => $product->id,
            'quantity' => $this->count,
            'price' => $this->model->total_sum,
            'cache' => json_encode($cache)
        ]);

        if (!($orderList->validate() && $orderList->save())) {
            throw new Exception("Buyurtma ro'yxatini saqlashda xatolik: " . json_encode($orderList->errors));
        }
    }

    /**
     * Buyurtma so'rovini yaratish
     * @param int $orderId
     * @param Product $product
     * @throws Exception
     */
    private function createOrderRequest($orderId, $product)
    {
        $orderRequest = new OrderRequest([
            'company_id' => $product->company_id,
            'price' => $this->count * $product->unit_price,
            'order_id' => $orderId,
            'created_at' => date('Y-m-d H:i:s'),
            'is_winner' => 0,
            'status' => ShopEnum::ORDER_REQUEST_STATUS_DONE,
            'type' => ShopEnum::ORDER_REQUEST_TYPE_PRODUCER,
        ]);

        if (!($orderRequest->validate() && $orderRequest->save())) {
            throw new Exception("Buyurtma so'rovini saqlashda xatolik: " . json_encode($orderRequest->errors));
        }
    }
}