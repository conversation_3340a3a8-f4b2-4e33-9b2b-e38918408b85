<?php


namespace api\modules\client\forms;


use api\components\BaseRequest;
use api\modules\client\resources\ClassifierResource;
use api\modules\client\resources\OrderResource;
use common\models\CompanyBankAccount;
use common\models\PlanSchedule;
use common\models\shop\Product;

class OrderForm extends BaseRequest
{
    public OrderResource $model;

    public int $product_id;
    public int $classifier_id;
    public int $plan_schedule_id;
    public int $count;
    public ?array $regions = [];
    public string $address;
    public ?int $account_number_id;
    public $pkcs7;

    public function rules()
    {
        $parent = parent::rules();
        $child = [
            [['plan_schedule_id', 'count'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [['regions', 'lot_number'], 'safe'],
            [['product_id'], 'exist', 'skipOnError' => true, 'targetClass' => Product::class, 'targetAttribute' => ['product_id' => 'id']],
            [['classifier_id'], 'exist', 'skipOnError' => true, 'targetClass' => ClassifierResource::class, 'targetAttribute' => ['classifier_id' => 'id']],
            [['plan_schedule_id'], 'exist', 'skipOnError' => true, 'targetClass' => PlanSchedule::class, 'targetAttribute' => ['plan_schedule_id' => 'id']],
            [['account_number_id'], 'exist', 'skipOnError' => true, 'targetClass' => CompanyBankAccount::class, 'targetAttribute' => ['account_number_id' => 'id']],

            [['account_number_id', 'expense_item'], 'required', 'when' => function ($model) {
                return \Yii::$app->user->identity->isBudget;
            }],
            [['account_number_id'], 'required', 'when' => function ($model) {
                return !\Yii::$app->user->identity->isBudget;
            }],
        ];
        return array_merge($parent, $child);
    }

    public function __construct(OrderResource $model, $params = [])
    {
        $this->model = $model;
        parent::__construct($params);
    }

    public function getResult()
    {
        
    }
}