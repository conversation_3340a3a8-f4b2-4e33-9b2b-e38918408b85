<?php


namespace api\modules\client\resources;


use api\modules\shop\resources\OrderRequestResource;
use common\enums\ShopEnum;
use common\models\shop\Order;
use common\models\shop\OrderRequest;
use yii\helpers\ArrayHelper;

class OrderResource extends Order
{
    public function fields()
    {
        return [
            'product_id',
            'plan_schedule_id',
            'classifier_id',
//            'account_number' => function ($model) {
//                return CompanyBankAccountResource::findOne($model->account_number_id);
//            },
            'type',
            'user_id',
            'company_id',
            'customer_account_treasury',
            'expense_item',
            'receiver_fio',
            'receiver_phone',
            'delivery_type',
            'payment_type',
            'total_sum' => function($model){
                return $model->total_sum / 100;
            },
            'created_at' => "begin_date",
            'shipping_sum',
            'status',
            'payment_status',
            'payment_date',
            'cancel_reason',
            'shop_end',
            'request_end',
            'count',
            'lot_number',
            'is_can_offer_price'=>'IsCanOfferPrice',
            'id',
            'start_one_sided_customer',
            'start_one_sided_producer',
            'one_sided_customer',
            'stateName',
        ];
    }

    public function extraFields()
    {
        return [
            'product',
            'planSchedule',
            'classifier',
            'user',
            'classifierCategory',
            'company',
            'region',
            'orderRequest',
            'orderRequestCancel',
            'minPrice' => function($model){
                $orderRequest = OrderRequest::find()->where(['order_id' => $model->id])->min('price');
                return $orderRequest / 100;
            }
        ];
    }
    public function getIsCanOfferPrice()
    {
        $orderRequest = OrderRequestResource::find()->andWhere(['order_id'=>$this->id]);
        $companyId = \Yii::$app->user->identity->company_id;
        if ($companyId == $this->company_id){
            if ($orderRequest->andWhere(['company_id'=>$companyId])->count()==2){
                return false;
            }
        }
        else{
            if ($orderRequest->andWhere(['company_id'=>$companyId])->exists()){
                return false;
            }
        }
        return true;

    }
    public function getStateName()
    {
        return ArrayHelper::getValue(
            [
                ShopEnum::SHOP_STATE_NEW => \Yii::t('app', 'Moderator tekshiruvida'),
                ShopEnum::ORDER_STATUS_REJECT_DMBAT => \Yii::t('app', 'DBMAT dan rad etilgan'),
                ShopEnum::ORDER_STATUS_WAITING => \Yii::t('app', 'DBMAT dan kutilyapti'),
                ShopEnum::ORDER_STATUS_CANCEL => \Yii::t('app', 'Rad qilingan'),
                ShopEnum::ORDER_STATUS_ACTIVE => \Yii::t('app', 'Aktiv holatda'),
                ShopEnum::ORDER_STATUS_INACTIVE => \Yii::t('app', 'Aktiv holatda emas'),
                ShopEnum::ORDER_STATUS_ACCEPT_ONE_SIDED => \Yii::t('app', 'Skidka sorash holatida'),
                ShopEnum::ORDER_STATUS_PROCESS => \Yii::t('app', '1 tomonlama shartnomada'),
            ],
            $this->status);

    }
    public static function generateLotNumber($id)
    {

        $YY =  substr(date('Y'), -2);
        $P= env('LOT_OPERATOR_NUMBER',5);
        $CUSTOMER_TYPE = env('LOT_CUSTOMER_TYPE',2);
        $K = 1;
        $CCC = '008';

        return $YY.$P.$CUSTOMER_TYPE.$K.$CCC. sprintf('%06d', $id);

    }

    public function getProduct()
    {
        return $this->hasOne(ProductResource::class , ['id' => 'product_id']);
    }
}