<?php


namespace api\modules\client\resources;


use common\models\Classifier;

class ProductViewResource extends ProductResource
{
    public function fields(): array
    {
        return [
            'id',
            'images',
            'lotNumber',
            "classifier_category_id",
            "classifier_id",
            "company_id",
            "organ",
            "title",
            "brand_title",
            "description_uz",
            "description_ru",
            "year",
            "quantity",
            "price",
            "min_order",
            "max_order",
            "delivery_period",
            "warranty_period",
            "expiry_period",
            "is_have_license",
            "created_at",
            "type",
            "unit_price",
            "made_in",
            "platform_display",
            "country_id",
            "account_number",
            "active_date",
            "inactive_date",
            "unit_id",
            "auto_renewal",
        ];
    }
    public function extraFields(): array
    {
        return [
            'region',
            'classifier',
            'unit',
        ];
    }

    public function getClassifier()
    {
        return $this->hasOne(Classifier::class , ['id' => 'classifier_id']);
    }
}