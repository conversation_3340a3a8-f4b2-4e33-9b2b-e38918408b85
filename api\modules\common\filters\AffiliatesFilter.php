<?php


namespace api\modules\common\filters;


use api\components\BaseRequest;
use api\modules\common\resources\AffiliatesResource;
use Yii;

class AffiliatesFilter extends BaseRequest
{
    public function getResult()
    {
        $company_id = Yii::$app->user->identity->company_id;
        $query = AffiliatesResource::find()->where(['company_id' => $company_id, 'deleted_at' => null]);

        return paginate($query);
    }
}