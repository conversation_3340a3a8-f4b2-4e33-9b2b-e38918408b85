<?php

namespace api\modules\common\filters;

use api\modules\common\resources\VirtualTransactionResource;
use common\widgets\ExcelExportWidget;
use Yii;

class CompanyTransactionExcelExportFilter extends CompanyTransactionFilter
{
    public array $fields = [];

    public function rules(): array
    {
        return [
            ['fields', 'required'],
            ['fields', 'checkFields'],
        ];
    }

    public function checkFields($attribute): void
    {
        foreach ($this->fields as $field) {
            if (!array_key_exists($field, self::_fields()) && !in_array($field, self::_fields())) {
                $this->addError($attribute, 'The role must be a valid key or value.');
            }
        }

    }

    /**
     * @throws \Throwable
     */
    public function generateFile(): string
    {
        $labels = array_filter(self::_labels(), fn($key) => in_array($key, $this->fields), ARRAY_FILTER_USE_KEY);
        $fields = array_filter(self::_fields(), fn($key) => in_array($key, $this->fields), ARRAY_FILTER_USE_KEY);
        return ExcelExportWidget::widget([
            'models' => $this->_query()->all(),
            'labels' => $labels,
            'fields' => $fields,
        ]);
    }

    public static function _labels(): array
    {
        return [
            'id' => '№',
            'created_at' => 'Дата',
            'contract_id' => 'Номер договора',
            'procedure_type' => 'Тип процедуры',
            'payer_info' => 'Плательщик/ИНН',
            'recipient_info' => 'Получатель/ИНН',
            'debit' => 'Дебет',
            'credit' => 'Кредит',
            'description' => 'Детали платежа',
        ];
    }

    public static function _fields()
    {
        return [
            'id',
            'created_at' => function (VirtualTransactionResource $model) {
                if ($model->created_at)
                {
                    return date('d/m/Y', strtotime($model->created_at)) . "\n" .  date('H:i', strtotime($model->created_at));
                }
                return null;
            },
            'contract_id',
            'procedure_type' => function (VirtualTransactionResource $model) {
                return $model->getProcedureLabel();
            },
            'payer_info' => function (VirtualTransactionResource $model) {
                return $model->debitCompany->title . "\n" . $model->debitCompany->tin;
            },
            'recipient_info' => function (VirtualTransactionResource $model) {
                return $model->creditCompany->title . "\n" . $model->creditCompany->tin;
            },
            'debit',
            'credit',
            'description' => function (VirtualTransactionResource $model) {
                return $model->getOperationTypeLabel();
            }
        ];
    }
}