<?php

namespace api\modules\common\filters;

use Yii;
use api\modules\common\resources\ContractCustomerFilterResource;
use api\components\BaseModel;
use common\enums\ContractEnum;
use common\enums\UserEnum;
use yii\db\Exception;

class ContractFilter extends BaseModel
{
    public ?string $procedure = null;
    public ?string $search = null;
    public ?string $date = null;
    public ?int $status = null;

    public function rules(): array
    {
        return [
            ['procedure', 'required'],
            ['procedure', 'in', 'range' => ContractEnum::PROCEDURE_TYPE_LIST],
            [['search', 'date', 'status',], 'safe'],
            ['date', 'date', 'format' => 'php:d/m/Y',],
        ];
    }

    /**
     * @throws Exception
     * @throws \Exception
     */
    public function getResult()
    {
        $identity = Yii::$app->user->identity;
        if (!$identity || !$company = $identity->company)
            throw new Exception('Unauthorized');
        $user_type = $identity->user_type;

        $query = ContractCustomerFilterResource::find();

        switch ($user_type) {
            case UserEnum::USER_TYPE_CUSTOMER:
                $query->with('customer')->andWhere(['customer_id' => $company->id]);
                break;
            case UserEnum::USER_TYPE_SUPPLIER:
                $query->with('producer')->andWhere(['producer_id' => $company->id]);
                break;
            default:
                $query->andWhere('0=1');
        }

        $query->andWhere(['procedure_type' => $this->procedure]);

        if ($this->date)
        {
            $date = \DateTime::createFromFormat('d/m/Y', $this->date)->format('Y-m-d');
            $query->andWhere(['between', 'created_at', $date . ' 00:00:00', $date . ' 23:59:59',]);
        }

        if ($this->search)
        {
            $query->andFilterWhere([
                'or',
                ['number' => $this->search],
                ['producer.tin' => $this->search],
                ['price' => is_numeric($this->search) ? $this->search * 100 : null],
            ]);
        }

        $query->andFilterWhere(['status' => $this->status]);

        return paginate($query->orderBy(['id' => SORT_DESC]));
    }
}