<?php


namespace api\modules\common\filters;


use api\components\BaseRequest;
use api\modules\common\resources\PlanScheduleClassifierResource;
use api\modules\common\resources\PlanScheduleResource;
use common\enums\StatusEnum;

class PlanScheduleClassifierFilter extends BaseRequest
{
    public PlanScheduleResource $model;

    public function __construct(PlanScheduleResource $model, $params = [])
    {
        $this->model = $model;
        parent::__construct($params);
    }
    
    public function getResult()
    {
        $query = PlanScheduleClassifierResource::find()
            ->notDeleted()
            ->andWhere(['plan_schedule_id' => $this->model->id, 'status' => StatusEnum::STATUS_ACTIVE])
            ->orderBy(['id' => SORT_ASC]);

        return paginate($query);
    }
}