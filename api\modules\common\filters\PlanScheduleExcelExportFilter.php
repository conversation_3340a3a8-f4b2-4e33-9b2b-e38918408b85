<?php

namespace api\modules\common\filters;

use api\modules\common\resources\ClassifierResource;
use api\modules\common\resources\PlanScheduleClassifierResource;
use api\modules\common\resources\PlanScheduleResource;
use common\enums\StatusEnum;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use Yii;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use yii\web\Response;

class PlanScheduleExcelExportFilter extends PlanScheduleFilter
{
    public $quarter;

    public function rules()
    {
        return [
            ['quarter', 'integer'],
            ['quarter', 'in', 'range' => [1,2,3,4]],
        ];
    }

    public function generateFile()
    {
        // 1. Yangi Excel fayl yaratish
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Quarter nomlari
        $quarter_names = [
            1 => 'I chorak (<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>)',
            2 => 'II chorak (<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>)',
            3 => 'III chorak (I<PERSON><PERSON>, Avgus<PERSON>, Sentabr)',
            4 => 'IV chorak (Oktabr, Noyabr, Dekabr)'
        ];

        // 2. 1-qatordagi sarlavhalar
        $sheet->setCellValue('A1', 'T/r');
        $sheet->setCellValue('B1', 'Xarid predmeti');
        $sheet->setCellValue('C1', 'O\'lchov birligi');
        $sheet->setCellValue('D1', 'Klassifikator kodi');

        $last_col = 'D';
        $merge_ranges = [];

        if ($this->quarter) {
            // Faqat bitta quarter
            $sheet->setCellValue('E1', $quarter_names[$this->quarter]);
            $sheet->mergeCells('E1:F1');
            $sheet->setCellValue('E2', 'miqdori');
            $sheet->setCellValue('F2', 'narxi');
            $last_col = 'F';
            $merge_ranges = ['A1:A2', 'B1:B2', 'C1:C2', 'D1:D2'];
        } else {
            // Barcha quarterlar
            $sheet->setCellValue('E1', $quarter_names[1]);
            $sheet->mergeCells('E1:F1');
            $sheet->setCellValue('G1', $quarter_names[2]);
            $sheet->mergeCells('G1:H1');
            $sheet->setCellValue('I1', $quarter_names[3]);
            $sheet->mergeCells('I1:J1');
            $sheet->setCellValue('K1', $quarter_names[4]);
            $sheet->mergeCells('K1:L1');

            // 3. 2-qatordagi kichik sarlavhalar
            $sheet->setCellValue('E2', 'miqdori');
            $sheet->setCellValue('F2', 'narxi');
            $sheet->setCellValue('G2', 'miqdori');
            $sheet->setCellValue('H2', 'narxi');
            $sheet->setCellValue('I2', 'miqdori');
            $sheet->setCellValue('J2', 'narxi');
            $sheet->setCellValue('K2', 'miqdori');
            $sheet->setCellValue('L2', 'narxi');
            $last_col = 'L';
            $merge_ranges = ['A1:A2', 'B1:B2', 'C1:C2', 'D1:D2'];
        }

        // Hujayralarni birlashtirish
        foreach ($merge_ranges as $range) {
            $sheet->mergeCells($range);
        }

        $company_id = 3;
        $this->quarter = Yii::$app->request->get('quarter'); // Query param dan quarter olish

        // Agar quarter berilgan bo'lsa faqat o'sha quarter, aks holda barcha quarterlar
        $quarter_condition = ['company_id' => $company_id, 'status' => StatusEnum::STATUS_ACTIVE];
        if ($this->quarter && in_array($this->quarter, [1, 2, 3, 4])) {
            $quarter_condition['quarter'] = $this->quarter;
        }

        $plan_schedules = PlanScheduleResource::find()->where($quarter_condition)->all();

        if (!$plan_schedules) {
            $this->addError('plan_schedule', 'Plan schedule topilmadi');
            return false;
        }

        // PlanScheduleClassifier lardan ma'lumotlarni olish
        $data = [];

        foreach ($plan_schedules as $plan_schedule) {
            // Har bir PlanSchedule ning childlarini olish
            $classifiers = PlanScheduleClassifierResource::find()
                ->where(['plan_schedule_id' => $plan_schedule->id, 'status' => StatusEnum::STATUS_ACTIVE])
                ->all();

            foreach ($classifiers as $classifier_item) {
                // Classifier ma'lumotlarini olish
                $classifier = ClassifierResource::findOne($classifier_item->classifier_id);
                if (!$classifier) continue;

                // Har bir PlanScheduleClassifier uchun alohida qator yaratamiz
                $row_data = [
                    'product_name' => $classifier->title_uz ?? '',
                    'unit' => $classifier->unit ?? '',
                    'code' => $classifier->code ?? '',
                    'quarter_1' => ['count' => 0, 'price' => 0],
                    'quarter_2' => ['count' => 0, 'price' => 0],
                    'quarter_3' => ['count' => 0, 'price' => 0],
                    'quarter_4' => ['count' => 0, 'price' => 0]
                ];

                // Tegishli quarterga ma'lumotlarni qo'yamiz
                $quarter_key = 'quarter_' . $plan_schedule->quarter;
                if (isset($row_data[$quarter_key])) {
                    $row_data[$quarter_key]['count'] = $classifier_item->count ?? 0;
                    $row_data[$quarter_key]['price'] = $classifier_item->tovarprice ?? 0;
                }

                $data[] = $row_data;
            }
        }

        // 5. Ma'lumotlarni yozish (3-qatordan boshlanadi)
        $row = 3;
        $counter = 1;

        foreach ($data as $record) {
            // T/r
            $sheet->setCellValue("A{$row}", $counter);

            // Xarid predmeti
            $sheet->setCellValue("B{$row}", $record['product_name']);

            // O'lchov birligi
            $sheet->setCellValue("C{$row}", $record['unit']);

            // Klassifikator kodi
            $sheet->setCellValue("D{$row}", $record['code']);

            if ($this->quarter) {
                // Faqat bitta quarter
                $quarter_key = 'quarter_' . $this->quarter;
                if (isset($record[$quarter_key])) {
                    $sheet->setCellValue("E{$row}", $record[$quarter_key]['count']);
                    $sheet->setCellValue("F{$row}", $record[$quarter_key]['price']);
                }
            } else {
                // Barcha quarterlar
                // I chorak
                if (isset($record['quarter_1'])) {
                    $sheet->setCellValue("E{$row}", $record['quarter_1']['count']);
                    $sheet->setCellValue("F{$row}", $record['quarter_1']['price']);
                }

                // II chorak
                if (isset($record['quarter_2'])) {
                    $sheet->setCellValue("G{$row}", $record['quarter_2']['count']);
                    $sheet->setCellValue("H{$row}", $record['quarter_2']['price']);
                }

                // III chorak
                if (isset($record['quarter_3'])) {
                    $sheet->setCellValue("I{$row}", $record['quarter_3']['count']);
                    $sheet->setCellValue("J{$row}", $record['quarter_3']['price']);
                }

                // IV chorak
                if (isset($record['quarter_4'])) {
                    $sheet->setCellValue("K{$row}", $record['quarter_4']['count']);
                    $sheet->setCellValue("L{$row}", $record['quarter_4']['price']);
                }
            }

            $row++;
            $counter++;
        }

        // Style qo'llash
        $headerStyle = [
            'font' => ['bold' => true, 'size' => 10],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
                'wrapText' => true
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
        ];

        $sheet->getStyle("A1:{$last_col}2")->applyFromArray($headerStyle);

        // Ma'lumotlar qismi uchun style
        $dataStyle = [
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
        ];

        if ($row > 3) {
            $sheet->getStyle("A3:{$last_col}" . ($row - 1))->applyFromArray($dataStyle);

            // Mahsulot nomlari uchun chap tomonga tekislash
            $sheet->getStyle("B3:B" . ($row - 1))->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);

            // Raqamlar uchun format (faqat miqdor va narx ustunlari)
            if ($this->quarter) {
                $sheet->getStyle("E3:F" . ($row - 1))->getNumberFormat()->setFormatCode('#,##0.0');
            } else {
                $sheet->getStyle("E3:{$last_col}" . ($row - 1))->getNumberFormat()->setFormatCode('#,##0.0');
            }
        }

        // Ustun kengligini sozlash
        $sheet->getColumnDimension('A')->setWidth(5);
        $sheet->getColumnDimension('B')->setWidth(25);
        $sheet->getColumnDimension('C')->setWidth(10);
        $sheet->getColumnDimension('D')->setWidth(20);

        if ($this->quarter) {
            // Faqat bitta quarter uchun
            $sheet->getColumnDimension('E')->setWidth(10);
            $sheet->getColumnDimension('F')->setWidth(12);
        } else {
            // Barcha quarterlar uchun
            $sheet->getColumnDimension('E')->setWidth(10);
            $sheet->getColumnDimension('F')->setWidth(12);
            $sheet->getColumnDimension('G')->setWidth(10);
            $sheet->getColumnDimension('H')->setWidth(12);
            $sheet->getColumnDimension('I')->setWidth(10);
            $sheet->getColumnDimension('J')->setWidth(12);
            $sheet->getColumnDimension('K')->setWidth(10);
            $sheet->getColumnDimension('L')->setWidth(12);
        }

        // Qator balandligini sozlash
        $sheet->getRowDimension(1)->setRowHeight(30);
        $sheet->getRowDimension(2)->setRowHeight(20);

        $filename = 'plan_schedule_' . date('Y-m-d') . '.xlsx';

        // Set response headers for Excel download
        Yii::$app->response->format = Response::FORMAT_RAW;
        Yii::$app->response->headers->add('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        Yii::$app->response->headers->add('Content-Disposition', "attachment;filename={$filename}");
        Yii::$app->response->headers->add('Cache-Control', 'max-age=0');

        // Create Excel writer and output to buffer
        $writer = new Xlsx($spreadsheet);
        ob_start();
        @$writer->save('php://output');

        // Return the Excel file content
        return ob_get_clean();
    }

    
    public function query()
    {
        $user = Yii::$app->user->identity;
        $query = PlanScheduleResource::find()
            ->andWhere(['organ' => null, 'status' => StatusEnum::STATUS_ACTIVE]);

        if ($user)
            $query->andWhere(['company_id' => $user->company_id]);

        if ($this->search) {
            if(is_numeric($this->search)){
                $query->andWhere(['or',
                    ['year' => $this->search],
                    ['quarter' => $this->search]
                ]);
            } else {
                $query->andWhere(['like', 'title', $this->search]);
            }
        }
        $query->orderBy('id desc');
        return $query;
    }
}