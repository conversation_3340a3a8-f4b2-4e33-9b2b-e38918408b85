<?php


namespace api\modules\common\filters;


use api\components\BaseRequest;
use api\modules\common\resources\PlanScheduleFileResources;

class PlanScheduleFileFilter extends BaseRequest
{
    public function getResult()
    {
        $company_id = \Yii::$app->user->identity->company_id;
        $query = PlanScheduleFileResources::find()->where(['deleted_at' => null, 'company_id' => $company_id]);
        return paginate($query);
    }
}