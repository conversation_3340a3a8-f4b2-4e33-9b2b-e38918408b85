<?php

namespace api\modules\common\filters;

use api\components\BaseRequest;
use api\modules\common\resources\PlanScheduleResource;
use common\enums\StatusEnum;
use Yii;

class PlanScheduleFilter extends BaseRequest
{

    public $search;
    public function rules()
    {
        return [
            ['search', 'safe']
        ];
    }

    public function getResult()
    {
//        $user = Yii::$app->user->identity;
//        $query = PlanScheduleResource::find()
//            ->andWhere(['organ' => null, 'status' => TenderEnum::STATUS_ACTIVE]);
//
//        if ($user)
//            $query->andWhere(['company_id' => $user->company_id]);
//
//        if ($this->search) {
//            if(is_numeric($this->search)){
//                $query->andWhere(['or',
//                ['year' => $this->search],
//                ['quarter' => $this->search]
//            ]);
//            } else {
//                $query->andWhere(['like', 'title', $this->search]);
//            }
//        }
//        $query->orderBy('id desc');
        return paginate($this->_query());
    }

    public function _query()
    {
        $user = Yii::$app->user->identity;
        $query = PlanScheduleResource::find()
            ->andWhere(['organ' => null, 'status' => StatusEnum::STATUS_ACTIVE]);

        if ($user)
            $query->andWhere(['company_id' => $user->company_id]);

        if ($this->search) {
            if(is_numeric($this->search)){
                $query->andWhere(['or',
                ['year' => $this->search],
                ['quarter' => $this->search]
            ]);
            } else {
                $query->andWhere(['like', 'title', $this->search]);
            }
        }
        $query->orderBy('id desc');
        return $query;
    }
}
