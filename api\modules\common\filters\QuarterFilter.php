<?php


namespace api\modules\common\filters;


use api\components\BaseRequest;
use api\modules\common\resources\PlanScheduleResource;
use common\enums\StatusEnum;

class QuarterFilter extends BaseRequest
{
    public function getResult()
    {
        $query = PlanScheduleResource::find()
            ->where(['company_id' => \Yii::$app->user->identity->company_id,'status' => StatusEnum::STATUS_ACTIVE,'deleted_at' => null])
            ->orderBy(['quarter' => SORT_ASC]);

        return paginate($query);
    }
}