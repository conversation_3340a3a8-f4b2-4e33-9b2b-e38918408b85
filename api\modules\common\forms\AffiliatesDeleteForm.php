<?php


namespace api\modules\common\forms;


use api\components\BaseRequest;
use api\modules\common\resources\AffiliatesResource;
use Yii;

class AffiliatesDeleteForm extends BaseRequest
{
    public AffiliatesResource $model;

    public function __construct(AffiliatesResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function getResult()
    {
        $model = $this->model;
        $model->deleted_at = date('Y-m-d H:i:s');
        $model->deleted_by = Yii::$app->user->identity->id;
        if (!$model->save()){
            $this->addErrors($model->errors);
            return false;
        }
        return true;
    }
}