<?php

namespace api\modules\common\forms;

use api\components\BaseRequest;
use api\modules\common\filters\ClassifierPropertiesFilter;
use api\modules\common\resources\ClassifierPropertiesResource;
use api\modules\common\resources\ClassifierResource;
use api\modules\common\resources\PlanScheduleClassifierResource;
use api\modules\common\resources\PlanScheduleClassifierUnitResource;
use api\modules\common\resources\PlanScheduleResource;
use common\enums\StatusEnum;
use common\models\File;
use common\models\PlanScheduleFile;
use phpseclib3\Math\PrimeField\Integer;
use yii\web\UploadedFile;
use Yii;
use PhpOffice\PhpSpreadsheet\IOFactory;
use function GuzzleHttp\Promise\all;

class ImportFromExcelForm extends BaseRequest 
{
    public $file;
    public $quarter;
    public $year;
    public function rules() 
    {
        return [
            // ['file', 'required'],
            ['file', 'file', 'extensions' => 'xlsx, xls'],
            ['year', 'integer']
//            ['quarter', 'integer'],
//            ['quarter', 'in', 'range' => PlanScheduleResource::getPlanScheduleByKvartalId()],
        ];
    }
    
    public function getResult()
    {
        $year = (int)$this->year;
        $file = UploadedFile::getInstanceByName('file');



        if ($file && $this->validateFile($file)) {
            $result = $this->processExcelFile($file, $year);

            return  $result;
        } else {
            $this->addError('file', 'File formatida xatolik yoki fayl juda katta!');
            return false;
        }
       
    }

    private function validateFile($file)
    {
        if ($file->size > 5 * 1024 * 1024) {
            return false;
        }

        $allowedExtensions = ['xlsx', 'xls', 'csv'];
        $extension = strtolower(pathinfo($file->name, PATHINFO_EXTENSION));
        
        return in_array($extension, $allowedExtensions);
    }

    private function processExcelFile($file, $year)
    {

        $transaction = Yii::$app->db->beginTransaction();
        $company_id = Yii::$app->user->identity->company_id;
        try {
            $uploadDir = Yii::getAlias('@storage') . '/web/plan_schedule/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            $fayl_filename = '/' . str_replace(' ', '_', str_replace('.' . $file->extension, '', $file->name)) . '_' . (int)microtime(true) . '.' . $file->extension;
            $fayl_title = 'company_id_' . $company_id . '_' . str_replace(' ', '_', str_replace('.' . $file->extension, '', $file->name)) . '_' . (int)microtime(true);
            $full_path = $uploadDir . $fayl_filename;
            if (!$file->saveAs($full_path)) {
                throw new \Exception('Faylni saqlashda xatolik!');
            }

            $excel_file = new File();
            $excel_file->path = $fayl_filename;
            $excel_file->day = date('Y-m-d');
            $excel_file->title = $fayl_title;
            $excel_file->size = $file->size;
            $excel_file->type = $file->extension;
            $excel_file->status = StatusEnum::STATUS_ACTIVE;

            if (!$excel_file->save()){
                $this->addError('file' , "Faylni saqlashda xatolik bo'ldi");
                return false;
            }

            $plan_schedule_file =  new PlanScheduleFile();
            $plan_schedule_file->company_id = $company_id;
            $plan_schedule_file->file_id = $excel_file->id;
            if (!$plan_schedule_file->save()){
                $this->addError('plan_schedule_file' , "Plan grafik faylni saqlashda xatolik bo'ldi");
                return false;
            }

            $spreadsheet = IOFactory::load($full_path);
            $worksheet = $spreadsheet->getActiveSheet();
            $highestRow = $worksheet->getHighestRow();
            $highestColumn = $worksheet->getHighestColumn();

            // Get header row
            $headerRow = [];
            $headerData = $worksheet->rangeToArray('A1:' . $highestColumn . '1')[0];
            foreach ($headerData as $header) {
                $headerValue = $header !== null ? trim($header) : '';
                if (!empty($headerValue)) {
                    $headerRow[] = $headerValue;
                } else {
                    $headerRow[] = '';
                }
            }

            $data = [];
            for ($row = 3; $row <= $highestRow; $row++) {
                $rowData = $worksheet->rangeToArray('A' . $row . ':' . $highestColumn . $row)[0];

                if (empty(array_filter($rowData))) {
                    continue;
                }

                $cleanRow = [];
                if (isset($rowData[1]) && !empty(trim($rowData[1]))) {
                    $cleanRow['product_name'] = trim($rowData[1]);
                }
                if (isset($rowData[2]) && !empty(trim($rowData[2]))) {
                    $cleanRow['unit'] = trim($rowData[2]);
                }
                if (isset($rowData[3]) && !empty(trim($rowData[3]))) {
                    $cleanRow['classifier_code'] = trim($rowData[3]);
                }

                $quarterIndexes = $this->getQuarterIndexes($headerRow);

                foreach ($quarterIndexes as $quarterNum => $indices) {
                    $countIndex = $indices['count'];
                    $priceIndex = $indices['price'];

                    $count = 0;
                    $price = 0;

                    if (isset($rowData[$countIndex])) {
                        $value = $rowData[$countIndex] !== null ? trim($rowData[$countIndex]) : '';
                        if (!empty($value)) {
                            $count = $this->cleanNumericValue($value);
                        }
                    }

                    if (isset($rowData[$priceIndex])) {
                        $value = $rowData[$priceIndex] !== null ? trim($rowData[$priceIndex]) : '';
                        if (!empty($value)) {
                            $price = $this->cleanNumericValue($value);
                        }
                    }

                    $cleanRow["quarter_{$quarterNum}"] = [
                        'count' => $count,
                        'price' => $price,
                        'quarter' => $quarterNum,
                    ];
                }

                if (!empty($cleanRow)) {
                    $data[] = $cleanRow;
                }
            }

            $errors = [];
            $company_id = Yii::$app->user->identity->company_id;
            $current_year = (int)date('Y');
            if (!empty($data)) {
                array_shift($data);
                foreach ($data as $rowIndex => $row) {
                    if (empty(array_filter($row))) {
                        continue;
                    }

                    try {
                        if ($year == $current_year && !$this->isForTheNewYear()) {
                            foreach ($quarterIndexes as $quarterNum => $quarterInfo) {
                                $quarter_data = $row["quarter_{$quarterNum}"];

                                if ($quarter_data['count'] > 0 || $quarter_data['price'] > 0) {
                                    $result = $this->processQuarterData(
                                        $quarter_data,
                                        $company_id,
                                        $year,
                                        $row['classifier_code'],
                                        $row['product_name']
                                    );

                                    if ($result['error']) {
                                        $errors[] = [
                                            'error' => $result['error'],
                                            'data' => $row
                                        ];
                                    }
                                }
                            }
                        }else if ($current_year == $year && $this->isForTheNewYear()){
                            foreach ($quarterIndexes as $quarterNum => $quarterInfo) {
                                $quarter_data = $row["quarter_{$quarterNum}"];

                                if ($quarter_data['count'] > 0 || $quarter_data['price'] > 0) {
                                    $result = $this->processQuarterData(
                                        $quarter_data,
                                        $company_id,
                                        $current_year + 1,
                                        $row['classifier_code'],
                                        $row['product_name']
                                    );

                                    if ($result['error']) {
                                        $errors[] = [
                                            'error' => $result['error'],
                                            'data' => $row
                                        ];
                                    }
                                }
                            }
                        }
                    } catch (\Exception $e) {
                        $errors[] = [
                            'error' => $e->getMessage(),
                            'data' => $row
                        ];
                    }
                }
            }

            $transaction->commit();

            $message = "Success";
            if (!empty($errors)) {
                $message .= " " . count($errors) . " ta xatolik bo'ldi.";
            }

            return [
                'success' => true,
                'message' => $message
            ];

        } catch (\Exception $e) {
            $transaction->rollBack();

            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    private function processQuarterData($quarter_data, $company_id, $year, $classifier_code, $product_name)
    {
        try {
            $quarter = $quarter_data['quarter'];
            if (!$this->canSaveQuarterData($quarter, $year)) {
                return [
                    'error' => null,
                    'message' => "Kvartal vaqti o'tib ketgan. {$quarter}-kvartal {$year}-yil uchun ma'lumot saqlanmaydi."
                ];
            }

            $plan_schedule = PlanScheduleResource::findOne([
                'quarter' => $quarter,
                'company_id' => $company_id,
                'year' => $year
            ]);

            if (!$plan_schedule) {
                $plan_schedule = new PlanScheduleResource([
                    'company_id' => $company_id,
                    'status' => StatusEnum::STATUS_ACTIVE,
                    'quarter' => $quarter,
                    'year' => $year
                ]);

                if (!$plan_schedule->save()) {
                    return ['error' => 'Plan schedule saqlanmadi: ' . json_encode($plan_schedule->errors)];
                }
            }

            $model = new PlanScheduleClassifierResource();
            $model->plan_schedule_id = $plan_schedule->id;
            $model->year = $year;
            $model->tovarname = $product_name;
            $model->status = StatusEnum::STATUS_ACTIVE;
            $model->enabled = 1;
            $model->count_used = 0;
            $model->count = $quarter_data['count'];
            $model->tovarprice = $quarter_data['price'];
            $model->summa = $model->count * $model->tovarprice;
            $model->classifier_id = $this->getClassifierId($classifier_code);

            if (!$model->save()) {
                return ['error' => 'Classifier saqlanmadi: ' . json_encode($model->errors)];
            }

            return [
                'error' => null,
                'plan_schedule_id' => $plan_schedule->id,
                'message' => "Ma'lumot muvaffaqiyatli saqlandi"
            ];

        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }
    private function getClassifierId(string $code): int
    {
        $model = ClassifierResource::find()
            ->notDeleted()
            ->andWhere(['code' => $code])
            ->one();

        if (!$model)
            throw new \Exception("{attribute} kodli tovar topilmadi");

        return $model->id;
    }

    private function saveUnit(int $unit_id,int $plan_classifier_id): bool
    {
        $unit = ClassifierPropertiesResource::findOne($unit_id);
        if (!$unit){
            throw new \Exception("$unit_id IDli o'lchov birligi topilmadi");
        }

        $model = new PlanScheduleClassifierUnitResource();
        $model->classifier_property_id = $unit->id;
        $model->plan_schedule_classifier_id = $plan_classifier_id;
        $model->status = StatusEnum::STATUS_ACTIVE;
        if (!$model->save()){
            throw new \Exception("O'lchov birligi saqlashda xatolik");
        }

        return true;
    }

    private function cleanNumericValue($value) {
        $cleaned = str_replace([' ', ','], ['', '.'], $value);
        return is_numeric($cleaned) ? (float)$cleaned : $value;
    }

    /**
     * Hozirgi kvartalni aniqlash
     */
    private function getCurrentQuarter()
    {
        $current_month = (int)date('n'); // 1-12

        if ($current_month >= 1 && $current_month <= 3) {
            return 1;
        } elseif ($current_month >= 4 && $current_month <= 6) {
            return 2;
        } elseif ($current_month >= 7 && $current_month <= 9) {
            return 3;
        } else {
            return 4;
        }
    }

    /**
     * Kvartal vaqti o'tib ketganmi yoki yo'qmi tekshirish
     */
    private function canSaveQuarterData($quarter, $year)
    {
        $current_year = (int)date('Y');
        $current_quarter = $this->getCurrentQuarter();

        if ($year != $current_year) {
            return false;
        }

        return $quarter >= $current_quarter;
    }

    private function isForTheNewYear(): bool {
        return time() > strtotime(date('Y') . '-12-24 23:59:59');
    }

    /**
     * Dynamically detect quarter columns based on headers
     */
    private function getQuarterIndexes($headerRow)
    {
        $quarters = [];
        $quarterPatterns = [
            'I chorak' => 1,
            'II chorak' => 2,
            'III chorak' => 3,
            'IV chorak' => 4,
            'I quarter' => 1,
            'II quarter' => 2,
            'III quarter' => 3,
            'IV quarter' => 4
        ];

        for ($i = 0; $i < count($headerRow) - 1; $i++) {
            $header1 = $headerRow[$i] !== null ? trim($headerRow[$i]) : '';
            $header2 = $headerRow[$i + 1] !== null ? trim($headerRow[$i + 1]) : '';

            foreach ($quarterPatterns as $pattern => $quarterNum) {
                if (stripos($header1, $pattern) !== false) {
                    if (stripos($header2, 'sum') !== false || stripos($header2, 'price') !== false || stripos($header2, 'summa') !== false) {
                        $quarters[$quarterNum] = [
                            'count' => $i,
                            'price' => $i + 1
                        ];
                        break;
                    }
                }
            }
        }

        if (empty($quarters)) {
            $quarters = $this->getDefaultQuarterIndexes($headerRow);
        }

        Yii::info('Detected quarter indexes: ' . json_encode($quarters), 'excel_import');
        return $quarters;
    }

    /**
     * Get default quarter indexes as fallback
     */
    private function getDefaultQuarterIndexes($headerRow)
    {
        $quarters = [];
        $defaultIndexes = [
            1 => [4, 5], 
            2 => [6, 7], 
            3 => [8, 9],
            4 => [10, 11] 
        ];

        foreach ($defaultIndexes as $quarterNum => $indexes) {
            if (isset($headerRow[$indexes[0]]) && isset($headerRow[$indexes[1]])) {
                $quarters[$quarterNum] = [
                    'count' => $indexes[0],
                    'price' => $indexes[1]
                ];
            }
        }

        return $quarters;
    }

}
