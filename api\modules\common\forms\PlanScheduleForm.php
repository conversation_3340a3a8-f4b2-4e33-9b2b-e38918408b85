<?php

namespace api\modules\common\forms;

use api\components\BaseRequest;
use api\modules\common\resources\PlanScheduleClassifierCreateResource;
use api\modules\common\resources\PlanScheduleClassifierResource;
use api\modules\common\resources\PlanScheduleResource;
use common\enums\PlanScheduleEnum;
use common\enums\StatusEnum;
use common\enums\TenderEnum;
use common\models\PlanScheduleClassifierUnit;
use Yii;

class PlanScheduleForm extends BaseRequest
{

    public $quarter_id;
    public $classifiers = [];

    public PlanScheduleResource $model;

    public function __construct($params = [])
    {
        parent::__construct($params);
    }

    /**
     * {@inheritdoc}
     * #return, year qiymatini aniqlashtirish kerak
     */
    public function rules()
    {
        return [
            [['classifiers', 'quarter_id'], 'required', 'message' => t('{attribute} yuborish kerak')],
            ['quarter_id', 'integer'],
        ];
    }

    public function attributeLabels()
    {
        return [
            'classifiers' => Yii::t('main', 'Mahsulot'),
        ];
    }


    public function getResult()
    {
        $transaction = Yii::$app->db->beginTransaction();

        $model = PlanScheduleResource::findOne(['id'=>$this->quarter_id]);

//        $model = new PlanScheduleResource([
//            'title' => $this->title,
//            'company_id' => Yii::$app->user->identity->company_id,
//            'status' => StatusEnum::STATUS_ACTIVE,
//            'year' => $this->year,
//            'quarter' => $this->quarter,
//            'total_product_count' => 0,
//        ]);
//
        if (!$model) {
            $this->addError('quarter_id' , "Plan grafik topilmadi");
            $transaction->rollBack();
            return false;
        }

        foreach ($this->classifiers as $classifier) {
//            $description = "";
//            if(isset($classifier['description']) && !empty($classifier['description']) && $classifier['description'] != null) {
//                $description = $classifier['description'];
//            }
//            if (PlanScheduleClassifierResource::find()->notDeleted()
//                ->andWhere(['plan_schedule_id' => $model->id, 'classifier_id' => $classifier['classifier_id'], 'status' => TenderEnum::STATUS_ACTIVE])->exists()
//            ) {
//                $this->addError("classifiers", t("Bu maxsulot avval qo'shilgan"));
//                $transaction->rollBack();
//                return false;
//            }

            $product = new PlanScheduleClassifierCreateResource([
                'plan_schedule_id' => $model->id,
                'classifier_id' => $classifier['classifier_id'],
//                'description' => $description,
                // 'year' => $model->year,
//                'month' => $classifier['month'],
                'count' => $classifier['count'],
                'tovarprice' => $classifier['tovarprice'],
                'summa' => $classifier['tovarprice'] * $classifier['count'],
                'count_live' => $classifier['count'],
                'count_used' => 0,
                'status' => TenderEnum::STATUS_ACTIVE,
                'enabled' => 1,
                'source_of_funding' => $classifier['source_of_funding'],
            ]);

            $model->total_product_count += $product->count;

            if (!$product->save()) {
                $this->addError('classifiers', $product->errors);
                $transaction->rollBack();
                return false;
            }
            foreach ($classifier['unit'] as $unitId) {
                $pscp = new PlanScheduleClassifierUnit();
                $pscp->plan_schedule_classifier_id = $product->id;
                $pscp->classifier_property_id = $unitId;
                $pscp->status = StatusEnum::STATUS_ACTIVE;
                if (!$pscp->save()) {
                    $this->addError('unit', $pscp->errors);
                    $transaction->rollBack();
                    return false;
                }
            }

        }

        if (!$model->save()) {
            $this->addError('classifiers', $model->errors);
            $transaction->rollBack();
            return false;
        }

        $transaction->commit();
        return true;
    }
}
