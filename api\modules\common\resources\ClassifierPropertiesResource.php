<?php


namespace api\modules\common\resources;


use common\models\ClassifierProperties;

class ClassifierPropertiesResource extends ClassifierProperties
{
    public function fields()
    {
        return [
            'id',
            'title',
            'option',
            'lang',
            'required',
        ];
    }

    public static function getRequiredPropertiesIDs(int $classifierID): array
    {
        return  ClassifierPropertiesResource::find()->select('id')->where(['classifier_id' => $classifierID, 'required' => 1])->column();
    }
}