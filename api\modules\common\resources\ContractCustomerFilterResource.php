<?php

namespace api\modules\common\resources;

use common\enums\ContractEnum;
use common\models\Contract;

class ContractCustomerFilterResource extends Contract
{
    public function fields(): array
    {
        return [
            'id',
            'number',
            'created_at' => function (ContractCustomerFilterResource $model) {
                return $model->created_at ? date('d/m/Y', strtotime($model->created_at)) : null;
            },
            'title',
            'count',
            'unit',
            'producer' => function (ContractCustomerFilterResource $model) {
                return $model->producer->title;
            },
            'price' => function (ContractCustomerFilterResource $model) {
                return $model->price / 100;
            },
            'status'
        ];
    }

    public function getTitle()
    {   
        if ($this->procedure_type == ContractEnum::PROCEDURE_TYPE_AUCTION) {
            if ($this->auction) {
                /** TODO check */
                return 'classifier';
            }
            return null;
        } else if ($this->procedure_type == ContractEnum::PROCEDURE_TYPE_TENDER || $this->procedure_type == ContractEnum::PROCEDURE_TYPE_SELECTION) {
            /** TODO check */
            if (!$this->tender) {
                return 'classifier';
            }
            return null;
        } else if ($this->procedure_type == ContractEnum::PROCEDURE_TYPE_E_SHOP || $this->procedure_type == ContractEnum::PROCEDURE_TYPE_N_SHOP) {
//            $order =
            return 'classifier';
        }
        return 'Средства для перевязки и дополнительные материалы';
    }

    public function getCount(): int
    {
        return 6000;
    }

    public function getUnit(): string
    {
        return 'шт';
    }
}