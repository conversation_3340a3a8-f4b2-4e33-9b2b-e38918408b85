<?php


namespace api\modules\common\resources;


use common\models\Classifier;
use common\models\PlanSchedule;
use common\models\PlanScheduleClassifier;

class PlanScheduleClassifierCreateResource extends PlanScheduleClassifier
{
    public function rules(): array
    {
        return [
            [['plan_schedule_id', 'classifier_id', 'status', 'count', 'count_live', 'count_used'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [['plan_schedule_id', 'classifier_id', 'status', 'enabled', 'created_by', 'updated_by', 'count', 'count_live', 'count_used'], 'integer'],
            [['created_at', 'updated_at', 'deleted_at'], 'safe'],
            [['description'], 'string', 'max' => 255],
            [['classifier_id'], 'exist', 'skipOnError' => true, 'targetClass' => Classifier::class, 'targetAttribute' => ['classifier_id' => 'id']],
            [['plan_schedule_id'], 'exist', 'skipOnError' => true, 'targetClass' => PlanSchedule::class, 'targetAttribute' => ['plan_schedule_id' => 'id']],
        ];
    }
}
