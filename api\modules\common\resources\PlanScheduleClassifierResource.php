<?php


namespace api\modules\common\resources;


use common\models\CompanyBankAccount;
use common\models\PlanScheduleClassifier;
use common\models\PlanScheduleClassifierUnit;
use common\models\query\NotDeletedFromCompanyQuery;
use function foo\func;

class PlanScheduleClassifierResource extends PlanScheduleClassifier
{
//    public function fields()
//    {
//        return [
//            'id',
//            'description',
//            'year',
//            'month',
//            'count',
//            'count_live',
//            'count_used',
//            'unit',
//            'kls',
//            'expense',
//            'summa' => function ($model) {
//                return $model->summa / 100;
//            },
//            'srok',
//            'address',
//            'company_bank_account_id',
//            'plan_schedule_id'
//        ];
//    }
    public function fields()
    {
        return [
            'id',
            'title' => function($model){
                if ($model->classifier)
                    return $model->classifier->title_uz;
            },
            'count',
            'count_live',
            'source_of_funding',
            'tovarprice',
            'summa' => function ($model) {
                return $model->summa / 100;
            },
            'year',
            'month',
        ];
    }


    public function extraFields()
    {
        return [
            'unit',
            'classifier',
            'planScheduleProperties',
            'unit'
        ];
    }

    public function getClassifier()
    {
        return $this->hasOne(ClassifierResource::class, ['id' => 'classifier_id']);
    }

    public function getPlanScheduleProperties()
    {
        return PlanSchedulePropertiesResource::find()->where('deleted_at is null')->andWhere(['plan_schedule_classifier_id' => $this->id])->all();
        //return $this->hasMany(PlanScheduleClassifierResource::class, ['plan_schedule_id' => 'id']);
    }

    public function getUnit()
    {
        return $this->hasMany(PlanScheduleClassifierUnitResource::class, ['plan_schedule_classifier_id' => 'id']);
    }

    public static function find()
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }
}