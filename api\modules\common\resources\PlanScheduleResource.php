<?php

namespace api\modules\common\resources;

use common\enums\StatusEnum;
use common\models\PlanSchedule;
use common\models\PlanScheduleProperties;
use common\models\query\NotDeletedFromCompanyQuery;
use common\traits\SoftDelete;

class PlanScheduleResource extends PlanSchedule
{

    use SoftDelete;

    public function fields()
    {
        return [
            'id', 'title', 'status', 'year', 'quarter', 'total_product_count','planScheduleClassifiersCount'
        ];
    }

    public function extraFields()
    {
        return [
            'file',
            'planScheduleClassifiers',
            'isUsed'
        ];
    }

    public function getPlanScheduleClassifiers()
    {
//        return PlanScheduleClassifierResource::find()->where('deleted_at is null')->andWhere(['plan_schedule_id' => $this->id])->all();
        return $this->hasMany(PlanScheduleClassifierResource::class, ['plan_schedule_id' => 'id'])->where('deleted_at is null')->andWhere(['plan_schedule_id' => $this->id]);
    }

    public function getPlanScheduleClassifiersCount()
    {
        return $this->hasMany(PlanScheduleClassifierResource::class, ['plan_schedule_id' => 'id'])->where('deleted_at is null')->andWhere(['plan_schedule_id' => $this->id])->count();
    }

    public function getIsUsed()
    {
        return PlanScheduleClassifierResource::find()->where('status=300 and count != count_live')->andWhere(['plan_schedule_id' => $this->id])->exists();
    }


    // public function getCompany()
    // {
    //     return $this->hasOne(Company::class, ['id' => 'company_id']);
    // }

    public function getFile()
    {
        return $this->hasOne(FileResource::class, ['id' => 'file_id']);
    }

    public static function find()
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }

}
