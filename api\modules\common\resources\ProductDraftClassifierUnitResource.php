<?php

namespace api\modules\common\resources;

use common\models\shop\ProductDraftClassifierUnit;

class ProductDraftClassifierUnitResource extends ProductDraftClassifierUnit
{
    public function fields(): array
    {
        return [
            'id',
            'product_id',
            'classifier_id',
            'classifierProperties'
        ];
    }

    public static function getClassifierPropertiesIDs($productID, $classifierID = null): array
    {
        return ProductDraftClassifierUnitResource::find()->select('classifier_properties_id')->where(['product_id' => $productID,'classifier_id' => $classifierID])->column();
    }
}