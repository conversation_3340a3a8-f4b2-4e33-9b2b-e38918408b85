<?php

namespace api\modules\common\resources;

use api\modules\client\resources\ClassifierResource;
use common\models\shop\Product;

class ProductResource extends Product
{
    public function getLotNumber(): ?string
    {
        if ($order = $this->order)
        {
            return $order->lot_number;
        }
        return null;
    }

    public function getClassifier()
    {
        return $this->hasOne(ClassifierResource::class, ['id' => 'classifier_id']);
    }
}