<?php

namespace api\modules\common\resources;

use common\enums\StatusEnum;
use common\models\Sms;

class SmsResource extends Sms
{
    public function checkPhoneBlocked(): bool
    {
        if ($this->failed_attempt > 2 && $this->last_failed_attempt > strtotime('-5 minutes'))
        {
            return true;
        }
        return false;
    }

    public static function getLastActiveSmsCode($phone)
    {
        return SmsResource::find()->where(['phone' => $phone, 'status' => StatusEnum::STATUS_NEW])->orderBy(['id' => SORT_DESC])->limit(1)->one();
    }
}