<?php

namespace api\modules\shop\controllers;

use Yii;
use api\components\ApiController;
use api\modules\shop\filters\CartListFilter;
use api\modules\shop\forms\CartDeleteForm;
use api\modules\shop\forms\CartForm;
use api\modules\shop\resources\CartResource;
use api\modules\shop\resources\ProductResource;
use yii\filters\AccessControl;
use yii\web\NotFoundHttpException;

/**
 * Default controller for the `shop` module
 */
class CartController extends ApiController
{
    public function behaviors(): array
    {
        $parent = parent::behaviors();
        $parent['access'] = [
            'class' => AccessControl::class,
            'rules' => [
                [
                    'allow' => true,
                    'actions' => ['index','create','delete',],
                    'roles' => ['user']
                ],
            ],
        ];
        return $parent;
    }

    public function actionIndex()
    {
        return $this->sendResponse(
            new CartListFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionCreate()
    {
        return $this->sendResponse(
            new CartForm(new CartResource()),
            Yii::$app->request->bodyParams
        );
    }
    public function actionDelete($id)
    {
        return $this->sendResponse(
            new CartDeleteForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    /**
     * @throws NotFoundHttpException
     */
    private function findOne($id)
    {
        return ProductResource::findOrFail($id);
    }

}
