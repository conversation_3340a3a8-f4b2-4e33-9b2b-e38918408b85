<?php

namespace api\modules\shop\controllers;

use Yii;
use api\components\ApiController;
use api\modules\shop\filters\ClassifierCategoryFilter;
use api\modules\shop\filters\ClassifierFilter;
use yii\filters\AccessControl;

class ClassifierController extends ApiController
{
    public function behaviors(): array
    {
        $parent = parent::behaviors();
        $parent['access'] = [
            'class' => AccessControl::class,
            'rules' => [
                [
                    'allow' => true,
                    'actions' => ['category','classifier',],
                    'roles' => ['user']
                ],
            ],
        ];
        return $parent;
    }

    public function actionCategory()
    {
        return $this->sendResponse(
            new ClassifierCategoryFilter(),
            Yii::$app->request->queryParams
        );
    }
    public function actionClassifier()
    {
        return $this->sendResponse(
            new ClassifierFilter(),
            Yii::$app->request->queryParams
        );
    }
}
