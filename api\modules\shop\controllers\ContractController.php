<?php

namespace api\modules\shop\controllers;

use api\components\ApiController;
use api\modules\auction\filters\ContractFilter;
use api\modules\common\resources\FileResource;
use api\modules\shop\filters\ContractInDeliveryFilter;
use api\modules\shop\filters\ContractInPaymentListFilter;
use api\modules\shop\filters\ContractListFilter;
use api\modules\shop\filters\ContractRequestFilter;
use api\modules\shop\filters\ExternalAuctionContractFilter;
use api\modules\shop\filters\ExternalShopContractFilter;
use api\modules\shop\filters\ExternalTenderContractFilter;
use api\modules\shop\forms\AcceptContractForm;
use api\modules\shop\forms\CancelContractAuctionForm;
use api\modules\shop\forms\CancelContractAuctionProducerForm;
use api\modules\shop\forms\CancelContractForm;
use api\modules\shop\forms\CancelContractProducerForm;
use api\modules\shop\forms\CancelForm;
use api\modules\shop\forms\ContractCancelRequestForm;
use api\modules\shop\forms\ContractDeliveredAuctionForm;
use api\modules\shop\forms\ContractDeliveredForm;
use api\modules\shop\forms\ContractSetPaymentListForm;
use api\modules\shop\resources\ContractRequestResource;
use api\modules\shop\resources\ContractResource;
use common\enums\ContractEnum;
use common\enums\PkcsEnum;
use common\enums\StatusEnum;
use kartik\mpdf\Pdf;
use Yii;
use yii\filters\AccessControl;
use yii\filters\auth\HttpBearerAuth;
use yii\web\NotFoundHttpException;

/**
 * Default controller for the `shop` module
 */
class ContractController extends ApiController
{
    public function behaviors(): array
    {
        $parent = parent::behaviors();
        $parent['access'] = [
            'class'  => AccessControl::class,
            'except' => ['external-auction', 'external-shop', 'external-tender',],
            'rules' => [
                [
                    'allow' => true,
                    'actions' => [
                        'index', 'index-producer',  'view',
                        'auction', 'auction-producer','set-payment',
                        'set-payment-auction','in-payment','in-delivery',
                        'delivered', 'delivered-auction', 'cancel-process-contract',
                        'cancel-request', 'cancel-contract', 'cancel-contract-auction',
                        'cancel-contract-auction-producer', 'cancel-contract-producer', 'contract-request-producer',
                        'accept-contract-reserve', 'auction-contract-file', 'shop-contract-file', 'shop-contract-file-2'
                    ],
                    'roles' => ['user']
                ],
            ],
        ];
        $parent['bearerAuth'] = [
            'class' => HttpBearerAuth::class,
            'except' => [
                'external-auction',
                'external-shop',
                'external-tender'
            ]
        ];
        return $parent;
    }

    public function actionIndex()
    {
        return $this->sendResponse(
            new ContractListFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionIndexProducer()
    {
        return $this->sendResponse(
            new ContractListFilter(),
            Yii::$app->request->queryParams
        );
    }

//    public function actionContract($id){
//        $contract = Contract::findOne($id);
//        $path_to_email_template = '@api/modules/shop/files/auction_contract.php';
//        $content = Yii::$app->view->renderFile($path_to_email_template, ['model' => $contract]);
//
//        $pdf = new Pdf(['mode' => Pdf::MODE_UTF8,
//            'format' => Pdf::FORMAT_A4,
//            'orientation' => Pdf::ORIENT_PORTRAIT,
//            'destination' => Pdf::DEST_BROWSER,
//            'content' => $content,
//            'cssFile' => '@api/modules/shop/files/css/pdf.css',
//        ]);
//
//        $fayl_filename = '/source/' . str_replace('.pdf', '', 'contract_file') . '_' . (int)microtime(true) . '.pdf';
//        $pdf->filename = 'lot-' . $contract->id . '-' . $contract->created_at . '.pdf';
//        //$pdf->output($content, \Yii::getAlias('@storage') . '/web' . $fayl_filename, 'F');
//        return $pdf->render();
//    }

    public function actionAuctionProducer()
    {
        return $this->sendResponse(
            new ContractFilter(ContractEnum::TYPE_USER_PRODUCER),
            Yii::$app->request->queryParams
        );
    }

    public function actionSetPayment()
    {
        $body = Yii::$app->request->bodyParams;
        $decodedPkcs = $this->verifyPkcs7($body);

        return $this->sendResponsePost(
            new ContractSetPaymentListForm(),
            $decodedPkcs,
            $body['pkcs7'],
            PkcsEnum::PKCS7_TYPE_SHOP_CONTRACT_SET_PAYMENT
        );
    }

    public function actionSetPaymentAuction()
    {
        $body = Yii::$app->request->bodyParams;
        $decodedPkcs = $this->verifyPkcs7($body);

        return $this->sendResponsePost(
            new ContractSetPaymentListForm(),
            $decodedPkcs,
            $body['pkcs7'],
            PkcsEnum::PKCS7_TYPE_SHOP_CONTRACT_SET_PAYMENT_AUCTION
        );
    }

    public function actionInPayment()
    {
        return $this->sendResponse(
            new ContractInPaymentListFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionExternalShop()
    {
        return $this->sendResponse(
            new ExternalShopContractFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionExternalAuction()
    {
        return $this->sendResponse(
            new ExternalAuctionContractFilter(),
            Yii::$app->request->queryParams
        );
    }
    public function actionExternalTender()
    {
        return $this->sendResponse(
            new ExternalTenderContractFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionInDelivery()
    {
        return $this->sendResponse(
            new ContractInDeliveryFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionDelivered()
    {
        $body = Yii::$app->request->bodyParams;
        $decodedPkcs = $this->verifyPkcs7($body);

        return $this->sendResponsePost(
            new ContractDeliveredForm(),
            $decodedPkcs,
            $body['pkcs7'],
            PkcsEnum::PKCS7_TYPE_SHOP_DELIVERED
        );
    }

    public function actionDeliveredAuction()
    {
        $body = Yii::$app->request->bodyParams;
        $decodedPkcs = $this->verifyPkcs7($body);

        return $this->sendResponsePost(
            new ContractDeliveredAuctionForm(),
            $decodedPkcs,
            $body['pkcs7'],
            PkcsEnum::PKCS7_TYPE_SHOP_DELIVERED_AUCTION
        );
    }


    /**
     * 1-marta qaysidir tomon yuborishi
     * */

    public function actionCancelProcessContract()
    {
        $body = Yii::$app->request->bodyParams;
        $decodedPkcs = $this->verifyPkcs7($body);

        return $this->sendResponsePost(
            new ContractCancelRequestForm(),
            $decodedPkcs,
            $body['pkcs7'],
            PkcsEnum::PKCS7_TYPE_SHOP_CANCEL_PROCESS_CONTRACT
        );
    }

    /**
     * Ikkinchi tomon bekor qilish uchun yuborilgan so'rovni rad qilishi
     */

    public function actionCancelRequest()
    {
        $body = Yii::$app->request->bodyParams;
        $decodePkcs = $this->verifyPkcs7($body);

        return $this->sendResponsePost(
            new CancelForm(),
            $decodePkcs,
            $body['pkcs7'],
            PkcsEnum::PKCS7_TYPE_SHOP_CANCEL_REQUEST
        );
    }

    /**
     * Ikkinchi tomon tasdiqlash
     */
    public function actionCancelContract()
    {
        $body = Yii::$app->request->bodyParams;
        $decodePkcs = $this->verifyPkcs7($body);

        return $this->sendResponsePost(
            new CancelContractForm(),
            $decodePkcs,
            $body['pkcs7'],
            PkcsEnum::PKCS7_TYPE_SHOP_CANCEL_CONTRACT
        );
    }

    public function actionCancelContractAuction()
    {
        $body = Yii::$app->request->bodyParams;
        $decodedPkcs = $this->verifyPkcs7($body);

        return $this->sendResponsePost(
            new CancelContractAuctionForm(),
            $decodedPkcs,
            $body['pkcs7'],
            PkcsEnum::PKCS7_TYPE_SHOP_CANCEL_CONTRACT_AUCTION
        );
    }

    public function actionCancelContractAuctionProducer()
    {
        $body = Yii::$app->request->bodyParams;
        $decodedPkcs = $this->verifyPkcs7($body);
        //shop/contract/cancel-contract-auction-producer
        return $this->sendResponsePost(
            new CancelContractAuctionProducerForm(),
            $decodedPkcs,
            $body['pkcs7'],
            PkcsEnum::PKCS7_TYPE_SHOP_CANCEL_CONTRACT_AUCTION_PRODUCER
        );
    }

    /**
     * Shop, yetkazib beruvchi bekor qilishi shartnomani
     */
    public function actionCancelContractProducer($id)
    {
        $body = Yii::$app->request->bodyParams;
        $decodedPkcs = $this->verifyPkcs7($body);

        //shop/contract/cancel-contract-producer
        return $this->sendResponsePost(
            new CancelContractProducerForm(),
            $decodedPkcs,
            $body['pkcs7'],
            PkcsEnum::PKCS7_TYPE_SHOP_CANCEL_CONTRACT_PRODUCER
        );
    }

    /**
     * zaxira g'olibga shartnoma tuzish bo'yicha so'rov borishi
     */
    public function actionContractRequestProducer()
    {
        //shop/contract/contract-request-producer
        return $this->sendResponse(
            new ContractRequestFilter(),
            Yii::$app->request->bodyParams
        );
    }

    /**
     * Zaxira g'olibga borgan shartnoma so'rovini qabul qilish yoki rad qilish
     * */
    public function actionAcceptContractReserve()
    {
        $body = Yii::$app->request->bodyParams;
        $decodedPkcs = $this->verifyPkcs7($body);

        //shop/contract/accept-contract-reserve
        return $this->sendResponsePost(
            new AcceptContractForm(),
            $decodedPkcs,
            $body['pkcs7'],
            PkcsEnum::PKCS7_TYPE_SHOP_ACCEPT_CONTRACT_RESERVE
        );
    }

    /**
     * @throws NotFoundHttpException
     */
    public function actionView($id)
    {
        return $this->sendModel($this->findOne($id));
    }

    /**
     * @throws NotFoundHttpException
     */
    private function findOne($id)
    {
        return ContractResource::findOrFail($id);
    }

    /**
     * @throws NotFoundHttpException
     */
    private function findContractRequestOne($id)
    {
        $model = ContractRequestResource::findOrFail($id);

        return $model;
    }

    public function actionAuctionContractFile($id)
    {

        $contract = $this->findOne($id);
        $path_to_email_template = '@api/modules/shop/files/auction_contract.php';
        $content = Yii::$app->view->renderFile($path_to_email_template, ['model' => $contract]);

        $pdf = new Pdf([
            'mode' => Pdf::MODE_UTF8,
            'format' => Pdf::FORMAT_A4,
            'orientation' => Pdf::ORIENT_PORTRAIT,
            'destination' => Pdf::DEST_BROWSER,
            'content' => $content,
            'cssFile' => '@api/modules/shop/files/css/pdf.css',

        ]);

        $fayl_filename = '/source/' . str_replace('.pdf', '', 'contract_file') . '_' . (int)microtime(true) . '.pdf';

        $pdf->output($content, \Yii::getAlias('@storage') . '/web' . $fayl_filename, 'F');
        $file = new FileResource();

        $file->path = $fayl_filename;
        $file->title = 'contract_file';
        $file->size = 1234;
        $file->type = 'pdf';
        $file->status = StatusEnum::STATUS_ACTIVE;

        if (!$file->save()) {
            unlink($fayl_filename);
        }
        $contract->updateAttributes(['file_id' => $file->id]);
        return $file->getSrc();
    }

    public function actionShopContractFile($id)
    {
        $contract = $this->findOne($id);

        if ($contract) {

            if ($contract->file_id) {
                $file = FileResource::findOne($contract->file_id);
                if ($file) {
                    $path = \Yii::getAlias('@storage') . '/web/source/' . $file->day . '/' . $file->path;
                    if (file_exists($path)) {
                        return Yii::$app->response->sendFile($path, $path);
                    }
                } else {
                    return null;
                }

            } else {

                $path_to_email_template = '@api/modules/shop/files/new_shop_contract2.php';
                $content = Yii::$app->view->renderFile($path_to_email_template, ['model' => $contract]);

                $pdf = new Pdf(['mode' => Pdf::MODE_UTF8,
                    'format' => Pdf::FORMAT_A4,
                    'orientation' => Pdf::ORIENT_PORTRAIT,
                    'destination' => Pdf::DEST_BROWSER,
                    'content' => $content,
                    'cssFile' => '@api/modules/shop/files/css/pdf.css',
                ]);

                $currentDate = date('Y-m-d');

                $folderPath = \Yii::getAlias('@storage') . '/web/source/' . $currentDate;
                if (!is_dir($folderPath)) {
                    mkdir($folderPath, 0777, true);
                }

                $fayl_filename = '/' . str_replace('.pdf', '', 'contract_file') . '_' . (int)microtime(true) . '.pdf';

                $pdf->output($content, $folderPath . $fayl_filename, 'F');

                $file = new FileResource();

                $file->path = $fayl_filename;
                $file->title = 'shop_contract_file_' . $contract->number;
                $file->size = 1234;
                $file->type = 'pdf';
                $file->day = $currentDate;
                $file->status = StatusEnum::STATUS_ACTIVE;

                if (!$file->save()) {
                    unlink($folderPath . $fayl_filename);
                    die;
                }
                $contract->updateAttributes(['file_id' => $file->id]);

                $path = \Yii::getAlias('@storage') . '/web/source/' . $file->day . '/' . $file->path;
                if (file_exists($path)) {
                    return Yii::$app->response->sendFile($path, $path);
                } else {
                    return "fayl saqlamayapti";
                }
            }

        }
        return "shartnoma topilmadi";
    }

    public function actionShopContractFile2($id)
    {
        $contract = $this->findOne($id);

        if ($contract) {

            $path_to_email_template = '@api/modules/shop/files/new_shop_contract2.php';
            $content = Yii::$app->view->renderFile($path_to_email_template, ['model' => $contract]);

            $pdf = new Pdf(['mode' => Pdf::MODE_UTF8,
                'format' => Pdf::FORMAT_A4,
                'orientation' => Pdf::ORIENT_PORTRAIT,
                'destination' => Pdf::DEST_BROWSER,
                'content' => $content,
                'cssFile' => '@api/modules/shop/files/css/pdf.css',
            ]);

            $currentDate = date('Y-m-d');

            $folderPath = \Yii::getAlias('@storage') . '/web/source/' . $currentDate;
            if (!is_dir($folderPath)) {
                mkdir($folderPath, 0777, true);
            }

            $fayl_filename = '/' . str_replace('.pdf', '', 'contract_file') . '_' . (int)microtime(true) . '.pdf';

            $pdf->output($content, $folderPath . $fayl_filename, 'F');

            $file = new FileResource();

            $file->path = $fayl_filename;
            $file->title = 'shop_contract_file_' . $contract->number;
            $file->size = 1234;
            $file->type = 'pdf';
            $file->day = $currentDate;
            $file->status = StatusEnum::STATUS_ACTIVE;

            if (!$file->save()) {
                unlink($folderPath . $fayl_filename);
                die;
            }
            $contract->updateAttributes(['file_id' => $file->id]);

            $path = \Yii::getAlias('@storage') . '/web/source/' . $file->day . '/' . $file->path;
            if (file_exists($path)) {
                return Yii::$app->response->sendFile($path, $path);
            } else {
                return "fayl saqlamayapti";
            }

        }
        return "shartnoma topilmadi";
    }

}
