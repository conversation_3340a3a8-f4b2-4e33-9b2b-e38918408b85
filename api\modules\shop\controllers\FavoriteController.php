<?php

namespace api\modules\shop\controllers;

use api\components\ApiController;
use api\modules\shop\filters\FavoriteListFilter;
use api\modules\shop\forms\FavoriteDeleteForm;
use api\modules\shop\forms\FavoriteForm;
use api\modules\shop\resources\FavoriteResource;
use Yii;
use yii\filters\AccessControl;

/**
 * Default controller for the `shop` module
 */
class FavoriteController extends ApiController
{
    public function behaviors(): array
    {
        $parent = parent::behaviors();
        $parent['access'] = [
            'class' => AccessControl::class,
            'rules' => [
                [
                    'allow' => true,
                    'actions' => ['index', 'create', 'delete',],
                    'roles' => ['user']
                ],
            ],
        ];
        return $parent;
    }
    public function actionIndex()
    {
        return $this->sendResponse(
            new FavoriteListFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionCreate()
    {
        return $this->sendResponse(
            new FavoriteForm(new FavoriteResource()),
            Yii::$app->request->bodyParams
        );
    }

    public function actionDelete($id)
    {
        return $this->sendResponse(
            new FavoriteDeleteForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    /**
     * @throws \Exception
     */
    private function findOne($id): FavoriteResource
    {
        $model = FavoriteResource::findOne($id);
        if (!$model) throw new \Exception("Product favourite not found");
        return $model;
    }
}
