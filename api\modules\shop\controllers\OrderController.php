<?php

namespace api\modules\shop\controllers;

use Yii;
use api\components\ApiController;
use api\modules\shop\filters\CancelOrderListFilter;
use api\modules\shop\filters\NoOfferListFilter;
use api\modules\shop\filters\OrderFinishedFilter;
use api\modules\shop\filters\OrderListFilter;
use api\modules\shop\filters\OrderRejectFilter;
use api\modules\shop\filters\OrderWaitingFilter;
use api\modules\shop\forms\OrderForm;
use api\modules\shop\forms\OrderUpdateForm;
use api\modules\shop\resources\OrderResource;
use common\enums\PkcsEnum;
use yii\filters\AccessControl;

/**
 * Default controller for the `shop` module
 */
class OrderController extends ApiController
{
    public function behaviors(): array
    {
        $parent = parent::behaviors();
        $parent['access'] = [
            'class' => AccessControl::class,
            'rules' => [
                [
                    'allow' => true,
                    'actions' => [
                        'index',
                        'create',
                        'update',
                        'finished',
                        'waiting',
                        'reject',
                        'no-offer',
                        'cancel-order'
                    ],
                    'roles' => ['user']
                ],
            ],
        ];
        return $parent;
    }

    public function actionIndex()
    {
        return $this->sendResponse(
            new OrderListFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionCreate()
    {
        $body = Yii::$app->request->bodyParams;
        $decodedPks7 = $this->verifyPkcs7($body);

        return $this->sendResponsePost(
            new OrderForm(new OrderResource()),
            $decodedPks7,
            $body['pkcs7'],
            PkcsEnum::PKCS7_TYPE_SHOP_ORDER_CREATE
        );
    }
    public function actionUpdate($id)
    {
        return $this->sendResponse(
            new OrderUpdateForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }
    public function actionFinished()
    {
        return $this->sendResponse(
            new OrderFinishedFilter(),
            Yii::$app->request->bodyParams
        );
    }
    public function actionWaiting()
    {
        return $this->sendResponse(
            new OrderWaitingFilter(),
            Yii::$app->request->queryParams
        );
    }
    public function actionReject()
    {
        return $this->sendResponse(
            new OrderRejectFilter(),
            Yii::$app->request->queryParams
        );
    }
    public function actionNoOffer()
    {
        return $this->sendResponse(
            new NoOfferListFilter(),
            Yii::$app->request->queryParams
        );
    }
    public function actionCancelOrder()
    {
        return $this->sendResponse(
            new CancelOrderListFilter(),
            Yii::$app->request->queryParams
        );
    }

    /**
     * @throws \Exception
     */
    private function findOne($id): OrderResource
    {
        $model = OrderResource::findOne($id);
        if (!$model) throw new \Exception("OrderResource not found");
        return $model;
    }

}
