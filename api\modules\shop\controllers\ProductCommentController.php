<?php

namespace api\modules\shop\controllers;

use Yii;
use api\components\ApiController;
use api\modules\shop\forms\ProductCommentForm;
use api\modules\shop\resources\ProductCommentResource;
use yii\filters\AccessControl;

/**
 * Default controller for the `shop` module
 */
class ProductCommentController extends ApiController
{
    public function behaviors()
    {
        $parent = parent::behaviors();
        $parent['access'] = [
            'class' => AccessControl::class,
            'rules' => [
                [
                    'allow' => true,
                    'actions' => ['create',],
                    'roles' => ['user']
                ],
            ],
        ];
        return $parent;
    }
    public function actionCreate()
    {

        return $this->sendResponse(
            new ProductCommentForm(new ProductCommentResource()),
            Yii::$app->request->bodyParams
        );
    }
}
