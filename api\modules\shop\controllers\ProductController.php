<?php

namespace api\modules\shop\controllers;

use api\components\ApiController;
use api\modules\shop\filters\AnnounceListFilter;
use api\modules\shop\filters\ProductCancelListFilter;
use api\modules\shop\forms\DeliveryProductForm;
use api\modules\shop\filters\ProductDetailFilter;
use api\modules\shop\filters\ProductListFilter;
use api\modules\shop\forms\AnnounceForm;
use api\modules\shop\forms\ModeratorAcceptForm;
use api\modules\shop\forms\ModeratorRejectForm;
use api\modules\shop\forms\OrderPlaceForm;
use api\modules\shop\forms\ProductAutoRenewalForm;
use api\modules\shop\forms\ProductCancelForm;
use api\modules\shop\forms\ProductDeleteForm;
use api\modules\shop\forms\ProductFilterForm;
use api\modules\shop\forms\ProductForm;
use api\modules\shop\forms\ProductSendModeratorForm;
use api\modules\shop\forms\ProductUpdateForm;
use api\modules\shop\resources\ProductResource;
use common\enums\PkcsEnum;
use Yii;
use yii\filters\AccessControl;

class ProductController extends ApiController
{
    public function behaviors(): array
    {
        $parent = parent::behaviors();
        $parent['access'] = [
            'class' => AccessControl::class,
            'rules' => [
                [
                    'allow' => true,
                    'actions' => [
                        'index', 'product-detail', 'product-view', 'filter',
                        'create', 'update', 'delete', 'moderator-accept',
                        'cancel', 'send-moderator', 'cancel-list',
                        'moderator-accept', 'moderator-reject', 'announce',
                        'announce-list', 'order-place', 'deliver-product',
                        'auto-renewal'
                    ],
                    'roles' => ['user']
                ],
            ],
        ];
        return $parent;
    }

    public function actionIndex()
    {
        return $this->sendResponse(
            new ProductListFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionFilter()
    {
        return $this->sendResponse(
            new ProductFilterForm(),
            Yii::$app->request->queryParams
        );
    }

    public function actionProductDetail($id)
    {
        return $this->sendResponse(
            new ProductDetailFilter($this->findOne($id)),
            Yii::$app->request->queryParams
        );
    }

    public function actionProductView($id)
    {
        return $this->sendModel($this->findOne($id));
    }

    private function findOne($id)
    {
        $model = ProductResource::findOrFail($id);

        return $model;
    }

    public function actionCreate()
    {
        $body = Yii::$app->request->bodyParams;
        $decodedPks7 = $this->verifyPkcs7($body);

        return $this->sendResponsePost(
            new ProductForm(new ProductResource()),
            $decodedPks7,
            $body['pkcs7'],
            PkcsEnum::PKCS7_TYPE_SHOP_PRODUCT_CREATE
        );
    }

    public function actionUpdate($id)
    {
        return $this->sendResponse(
            new ProductUpdateForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    public function actionDelete($id)
    {
        return $this->sendResponse(
            new ProductDeleteForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    public function actionCancel($id)
    {
        return $this->sendResponse(
            new ProductCancelForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    public function actionSendModerator($id)
    {
        return $this->sendResponse(
            new ProductSendModeratorForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    public function actionCancelList()
    {
        return $this->sendResponse(
            new ProductCancelListFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionModeratorAccept($id)
    {
        return $this->sendResponse(
            new ModeratorAcceptForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    public function actionModeratorReject($id)
    {
        return $this->sendResponse(
            new ModeratorRejectForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    public function actionAnnounce($id)
    {
        return $this->sendResponse(
            new AnnounceForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    public function actionAnnounceList()
    {
        //shop/product/announce-list
        return $this->sendResponse(
            new AnnounceListFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionOrderPlace($id)
    {
        return $this->sendResponse(
            new OrderPlaceForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    public function actionDeliveryProduct()
    {
        return $this->sendResponse(
            new DeliveryProductForm(),
            Yii::$app->request->queryParams
        );
    }

    public function actionAutoRenewal($id)
    {
        return $this->sendResponse(
            new ProductAutoRenewalForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }
}
