<?php


namespace api\modules\shop\controllers;


use api\components\ApiController;
use api\modules\shop\forms\ProductDraftForm;
use api\modules\shop\forms\ProductDraftUpdateForm;
use api\modules\shop\forms\SendToModerationForm;
use api\modules\shop\resources\ProductDraftResource;
use api\modules\shop\resources\ProductResource;
use common\enums\PkcsEnum;
use common\enums\StatusEnum;
use Yii;
use yii\web\NotFoundHttpException;

class ProductDraftController extends ApiController
{
    public function actionCreate(): array
    {
        return $this->sendResponse(
            new ProductDraftForm(new ProductDraftResource()),
            Yii::$app->request->bodyParams
        );
    }

    /**
     * @throws NotFoundHttpException
     */
    public function actionView($id): array
    {
        return $this->sendModel($this->findOne($id));
    }

    /**
     * @throws NotFoundHttpException
     */
    public function actionUpdate($id): array
    {
        return $this->sendResponse(
            new ProductDraftUpdateForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    /**
     * @throws NotFoundHttpException
     */
    public function actionSendToModeration(): array
    {
        $body = Yii::$app->request->bodyParams;
        $decodedPks7 = $this->verifyPkcs7($body);

        return $this->sendResponsePost(
            new SendToModerationForm(new ProductResource()),
            $decodedPks7,
            $body['pkcs7'],
            PkcsEnum::PKCS7_TYPE_SHOP_PRODUCT_DRAFT_SEND_MODERATOR
        );
    }

    /**
     * @throws NotFoundHttpException
     */
    private function findOne($id): ProductDraftResource
    {
        $model = ProductDraftResource::findOne(['id' => $id, 'status' => StatusEnum::STATUS_ACTIVE]);
        if (!$model) throw new NotFoundHttpException("Product draft not found");
        return $model;
    }

}