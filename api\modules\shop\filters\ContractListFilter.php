<?php

namespace api\modules\shop\filters;

use common\enums\ContractEnum;
use Yii;
use api\components\BaseRequest;
use api\modules\shop\resources\ContractResource;
use common\models\shop\Product;
use yii\web\UnauthorizedHttpException;

class ContractListFilter extends BaseRequest
{
    public ?string $procedure_type = null;
    public ?string $search = null;
    public ?string $date = null;
    public ?int $status = null;

    public function rules(): array
    {
        return [
            ['procedure_type', 'required'],
            ['procedure_type', 'in', 'range' => [ContractEnum::PROCEDURE_TYPE_E_SHOP, ContractEnum::PROCEDURE_TYPE_N_SHOP]],
            [['search', 'date', 'status'], 'safe'],
            ['date', 'date', 'format' => 'php:d/m/Y'],
            ['status', 'integer',],
        ];
    }

    /**
     * @throws UnauthorizedHttpException
     */
    public function getResult(): array
    {
        $identity = Yii::$app->user->identity;
        if (!$identity) throw new UnauthorizedHttpException("Unauthorized");

        $query = ContractResource::find()
            ->with(['producer', 'order'])
            ->where('order_id is not null')
            ->andWhere(['procedure_type' => $this->procedure_type])
            ->andWhere(['producer_id' => $identity->company_id]);

//        $query->leftJoin('order', 'order.id=contract.order_id');
//        $query->leftJoin(Product::tableName(),Product::tableName().'.id'.'='.OrderResource::tableName().'.product_id');

        if ($this->search){
            $query->andWhere(['like',Product::tableName().'.title',$this->search]);
        }
        return paginate($query->orderBy(['id' => SORT_DESC]));
    }
}
