<?php

namespace api\modules\shop\filters;

use api\components\BaseRequest;
use api\modules\shop\resources\ProductDraftCustomResource;
use common\enums\ProductEnum;
use yii\helpers\ArrayHelper;

class ProductDraftFilter extends BaseRequest
{
    public $platform_display;
    public $search;

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['platform_display'], 'string', 'max' => 25],
            [['platform_display'], 'in', 'range' => [
                ProductEnum::PLATFORM_DISPLAY_E_SHOP,
                ProductEnum::PLATFORM_DISPLAY_NATIONAL
            ]],
            [['search'], 'string', 'max' => 255],
            [['search'], 'trim'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'platform_display' => t('Platform turi'),
            'search' => t('Qidiruv'),
        ];
    }

    /**
     * Product Draft larni status bo'yicha guruhlab qaytarish (paginatsiya bilan)
     * @return array
     */
    public function getResult()
    {
        // Barcha status qiymatlari
        $statusList = [
            ProductEnum::SHOP_STATE_NEW,
            ProductEnum::SHOP_STATE_RETURN_MODERATOR,
            ProductEnum::SHOP_STATE_NO_MONEY,
            ProductEnum::SHOP_STATE_ACTIVE,
            ProductEnum::SHOP_STATE_IN_ACTIVE,
            ProductEnum::SHOP_STATE_DELETED,
            ProductEnum::SHOP_STATE_CANCEL,
        ];

        $result = [];

        foreach ($statusList as $status) {
            $query = ProductDraftCustomResource::find()
                ->select([
                    'id', 'title', 'brand_title', 'description_uz', 'description_ru',
                    'price', 'quantity', 'min_order', 'max_order', 'state', 'status',
                    'platform_display', 'type', 'created_at', 'active_date'
                ])
                ->andWhere(['state' => $status]);

            // Platform display filter
            if (!empty($this->platform_display)) {
                $query->andWhere(['platform_display' => $this->platform_display]);
            }

            // Search filter
            if (!empty($this->search)) {
                $query->andWhere([
                    'or',
                    ['ilike', 'title', $this->search],
                    ['ilike', 'brand_title', $this->search],
                    ['ilike', 'description_uz', $this->search],
                    ['ilike', 'description_ru', $this->search],
                ]);
            }

            // Faqat o'chirilmagan product draftlar
            $query->andWhere(['deleted_at' => null]);

            // Oxirgi qo'shilganlar birinchi
            $query->orderBy(['id' => SORT_DESC]);

            // Paginatsiya qo'llash
            $paginatedResult = paginate($query);

            $result[$status] = [
                'status' => $status,
                'status_name' => $this->getStatusName($status),
                'count' => $paginatedResult['meta']['totalCount'],
                'data' => $paginatedResult['data'],
                'meta' => $paginatedResult['meta'],
            ];
        }

        return $result;
    }

    /**
     * Status nomini olish
     * @param int $status
     * @return string
     */
    private function getStatusName($status)
    {
        $statusNames = [
            ProductEnum::SHOP_STATE_NEW => t('Moderator tekshiruvida'),
            ProductEnum::SHOP_STATE_RETURN_MODERATOR => t('Moderatordan qaytarilgan'),
            ProductEnum::SHOP_STATE_NO_MONEY => t('Mablag\' yetarli emas'),
            ProductEnum::SHOP_STATE_ACTIVE => t('Sotuvda'),
            ProductEnum::SHOP_STATE_IN_ACTIVE => t('Aktiv holatda emas'),
            ProductEnum::SHOP_STATE_DELETED => t('O\'chirilgan'),
            ProductEnum::SHOP_STATE_CANCEL => t('Rad qilingan'),
        ];

        return ArrayHelper::getValue($statusNames, $status, t('Noma\'lum status'));
    }
}