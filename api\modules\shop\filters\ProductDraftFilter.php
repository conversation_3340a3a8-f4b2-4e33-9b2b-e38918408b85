<?php


namespace api\modules\shop\filters;


use api\components\BaseRequest;
use api\modules\shop\resources\ProductDraftCustomResource;
use api\modules\shop\resources\ProductDraftResource;
use common\enums\ProductEnum;

class ProductDraftFilter extends BaseRequest
{
    public $platform_display;
    public $search;

    public function rules()
    {
        return [
            [['platform_display'], 'string', 'max' => 25],
            [['platform_display'], 'in', 'range' => [
                ProductEnum::PLATFORM_DISPLAY_E_SHOP,
                ProductEnum::PLATFORM_DISPLAY_NATIONAL
            ]],
            [['search'], 'string', 'max' => 255],
            [['search'], 'trim'],
        ];
    }

    public function getResult()
    {
        $query = ProductDraftCustomResource::find()->where(['deleted_at' => null]);

        if (!empty($this->platform_display)){
            $query->andWhere(['platform_display' => $this->platform_display]);
        }

        if (!empty($this->search)){
            $query->andWhere([
               'or',
               ['ilike', 'title', $this->search],
               ['ilike', 'brand_title', $this->search],
               ['ilike', 'description_uz', $this->search],
               ['ilike', 'description_ru', $this->search],
            ]);
        }

        return paginate($query);
    }
}