<?php


namespace api\modules\shop\forms;


use api\components\BaseRequest;
use api\modules\common\resources\ClassifierResource;
use api\modules\common\resources\PlanScheduleClassifierResource;
use api\modules\common\resources\VirtualTransactionResource;
use api\modules\shop\resources\OrderResource;
use common\enums\CompanyEnum;
use common\enums\OperationTypeEnum;
use common\enums\ProductEnum;
use common\enums\ShopEnum;
use common\enums\StatusEnum;
use common\enums\UserEnum;
use common\models\Bmh;
use common\models\Classifier;
use common\models\CompanyBankAccount;
use common\models\PlanSchedule;
use common\models\shop\OrderList;
use common\models\shop\OrderRegion;
use common\models\shop\OrderRequest;
use common\models\shop\Product;
use common\models\shop\ProductRegion;
use common\models\WorkdayCalendar;
use Yii;
use yii\base\Exception;
use yii\helpers\ArrayHelper;

class OrderForm extends BaseRequest
{

    public OrderResource $model;

    public $product_id;
    public $classifier_id;
    public $type;
    public $plan_schedule_id;
    public $customer_account_treasury;
    public $expense_item;
    public $receiver_fio;
    public $receiver_phone;
    public $delivery_type;
    public $payment_type;
    public $shipping_sum;
    public $payment_status;
    public $payment_date;
    public $cancel_reason;
    public $cancel_date;
    public $shop_end;
    public $count;
    public $account_number_id;

    public $regions = [];
    public $pkcs7;

    public function __construct(OrderResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function rules()
    {
        $parent = parent::rules();
        $child = [
            [['product_id', 'classifier_id', 'plan_schedule_id',
                'count'
            ], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [['regions', 'lot_number'], 'safe'],
            [['product_id'], 'exist', 'skipOnError' => true, 'targetClass' => Product::class, 'targetAttribute' => ['product_id' => 'id']],
            [['classifier_id'], 'exist', 'skipOnError' => true, 'targetClass' => ClassifierResource::class, 'targetAttribute' => ['classifier_id' => 'id']],
            [['plan_schedule_id'], 'exist', 'skipOnError' => true, 'targetClass' => PlanSchedule::class, 'targetAttribute' => ['plan_schedule_id' => 'id']],
            [['account_number_id'], 'exist', 'skipOnError' => true, 'targetClass' => CompanyBankAccount::class, 'targetAttribute' => ['account_number_id' => 'id']],

            [['account_number_id', 'expense_item'], 'required', 'when' => function ($model) {
                return \Yii::$app->user->identity->isBudget;
            }],
            [['account_number_id'], 'required', 'when' => function ($model) {
                return !\Yii::$app->user->identity->isBudget;
            }],
        ];
        return array_merge($parent, $child);
    }

    /**
     * @throws Exception
     * @throws \yii\db\Exception
     * @throws \DateMalformedStringException
     */
    public function getResult()
    {
        $transaction = \Yii::$app->db->beginTransaction();

        // $companyId = \Yii::$app->user->identity->company_id;
        $user = Yii::$app->user->identity;
        if($user->user_type != UserEnum::USER_TYPE_CUSTOMER){
            $this->addError("error", t("Buyurtmachi taklif beroladi"));
            return false;
        }
        $company = $user->company;
        $product = Product::findOne($this->product_id);
        //TODO check count

        if ($this->count < $product->min_order || $this->count > $product->max_order) {
            throw new Exception(t("Mahsulot soni noto'g'ri"));
        }

        if (count($this->regions) == 0) {
            throw new Exception(t("Hudud tanlash majburiy"));
        }
        $productRegionIds = ArrayHelper::getColumn(ProductRegion::find()->andWhere(['product_id' => $product->id])->all(), 'region_id');
        if ($product->platform_display == ProductEnum::PLATFORM_DISPLAY_E_SHOP && !in_array($this->regions[0], $productRegionIds)) {
            throw new Exception(t("Siz tanlagan hududga yetkazib berilmaydi"));
        }
        if ($product->platform_display == ProductEnum::PLATFORM_DISPLAY_NATIONAL && $company->region_id != $product->company->region_id) {
            throw new Exception(t("Yetkazib beruvchi siz bilan bir hududda emas!!!"));
        }
        if ($this->count > $product->quantity) {
            throw new Exception(t("Mahsulot sonidan ortiqcha buyurtma qilyapsiz"));
        }

        //TODO plan grafikni tekshirish


        $plan_schedule = PlanScheduleClassifierResource::find()->andWhere(['plan_schedule_id' => $this->plan_schedule_id])->andWhere(['classifier_id' => $this->classifier_id])->one();
        if (!$plan_schedule) {
            throw new Exception(t("Reja grafik topilmadi"));
        }
        if ($plan_schedule->count_live < $this->count) {
            throw new Exception(t("Reja grafikdagi joriy miqdordan ko'p kiritdingiz"));
        }
        $plan_schedule->updateAttributes([
            'count_used' => $this->count,
            'count_live' => $plan_schedule->count - $this->count,
        ]);
        if ($plan_schedule->count_live == 0) {
            $plan_schedule->updateAttributes([
                'status' => StatusEnum::STATUS_NOT_ACTIVE, // not active
            ]);
        }

        //TODO check balance

        $zalog = $this->count * $product->unit_price * env('SHOP_ZALOG_PERSENT', 0.03);
        $commission = $this->count * $product->unit_price * env('COMMISSION_PERCENT', 0.0015);
        $commissionProducer = $commission;

        if ($commission >= env('SHOP_CUSTOMER_MAX_COMMISSION_SUM', 1000000)) {
            $commission = env('SHOP_CUSTOMER_MAX_COMMISSION_SUM', 1000000);
        }

        $total_block_sum = $zalog + $commission;
        if (!hasMoney($company, $total_block_sum)) {
            throw new Exception(t("Balansda yetarli mablag' mavjud emas"));
        }

        $bmh = Bmh::getAmount();
        $price = $product->unit_price * $this->count;
        if ($product->type == ProductEnum::PRODUCT_TYPE_PRODUCT) {
            if ($company->organization_type == CompanyEnum::NO_BYUDJET) {
                $bmh = $bmh * 25000;
                if ($price > $bmh) {
                    $transaction->rollBack();
                    $this->addError("error", t("Tovarlarning qiymati bir shartnoma bo‘yicha bazaviy hisoblash miqdorining yigirma besh ming baravarigacha bo‘lgan miqdorni tashkil etadi"));
                    return false;
                }

            } else {
                $bmh = $bmh * 2500;
                if ($price > $bmh) {
                    $transaction->rollBack();
                    $this->addError("error", t("Tovarlarning qiymati bir shartnoma bo‘yicha bazaviy hisoblash miqdorining ikki yarim ming baravarigacha bo‘lgan miqdorni tashkil etadi"));
                    return false;
                }
            }
        }

        if ($product->type == ProductEnum::PRODUCT_TYPE_SERVICE) {
            if ($company->organization_type == CompanyEnum::NO_BYUDJET) {
                $bmh = $bmh * 100;
                if ($price > $bmh) {
                    $transaction->rollBack();
                    $this->addError("error", t("Tovarlarning qiymati bir shartnoma bo‘yicha bazaviy hisoblash miqdorining yuz baravarigacha bo‘lgan miqdorni tashkil etadi"));
                    return false;
                }

            } else {
                $bmh = $bmh * 50;
                if ($price > $bmh) {
                    $transaction->rollBack();
                    $this->addError("error", t("Tovarlarning qiymati bir shartnoma bo‘yicha bazaviy hisoblash miqdorining ellik baravarigacha bo‘lgan miqdorni tashkil etadi"));
                    return false;
                }
            }
        }
        $holidays = ArrayHelper::map(WorkdayCalendar::findAll(['type' => WorkdayCalendar::HOLIDAY]), 'local_date', 'local_date');
        $workDays = ArrayHelper::map(WorkdayCalendar::findAll(['type' => WorkdayCalendar::WORKDAY]), 'local_date', 'local_date');

        $this->model->total_sum = $product->unit_price * $this->count;
        $this->model->company_id = $product->company_id;
        $this->model->user_id = \Yii::$app->user->id;
        $this->model->status = $user->isBudget ? ShopEnum::ORDER_STATUS_WAITING : ShopEnum::ORDER_STATUS_ACTIVE;
        $this->model->created_at = date("Y-m-d H:i:s");
        $this->model->begin_date = startWorkDay(date("Y-m-d H:i:s"), $workDays,$holidays);
        $endDate = addDaysExcludingWeekends($this->model->begin_date, 2, $workDays,$holidays);
        $this->model->request_end = $endDate;
        $att = $this->attributes;
        $this->model->setAttributes($att, false);

        if ($this->model->attributes && $this->model->validate() && $this->model->save()) {
            $orderId = $this->model->id;
            //TODO zalog blocklash  buyurtmacidan
//            $company_transaction_zalog = new CompanyTransaction([
//                'company_id' => $companyId,
//                'contract_id' => null,
//                'order_id' => $this->model->id,
//                'amount' => $zalog,
//                'type' => CompanyTransactionEnum::TYPE_ZALOG,
//                'description' => Yii::t("main", "Mahsulotga narx so'rovi uchun garov bandlandi"),
//                'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                'transaction_date' => date("Y-m-d H:i:s"),
//            ]);
            //TODO kommissiya blocklash buyurtmacidan
//            $company_transaction_commission = new CompanyTransaction([
//                'company_id' => $companyId,
//                'contract_id' => null,
//                'order_id' => $this->model->id,
//                'amount' => $commission,
//                'type' => CompanyTransactionEnum::TYPE_BLOCK_COMMISION,
//                'description' => Yii::t("main", "Mahsulotga narx so'rovi uchun kommissiya bandlandi"),
//                'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                'transaction_date' => date("Y-m-d H:i:s"),
//            ]);
//            if (!($company_transaction_commission->save() && $company_transaction_zalog->save())) {
//                $this->addError("commission", $company_transaction_commission->errors);
//                $transaction->rollBack();
//
//                return false;
//            }


            //TODO zalog blocklash  yetkazib beruvchidan
//            $company_transaction_zalog = new CompanyTransaction([
//                'company_id' => $product->company_id,
//                'order_id' => $this->model->id,
//                'amount' => $zalog,
//                'type' => CompanyTransactionEnum::TYPE_ZALOG,
//                'description' => Yii::t("main", "Maxsulotga narx so'rovi berilgani uchun garov bandlandi"),
//                'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                'transaction_date' => date("Y-m-d H:i:s"),
//            ]);
            //TODO kommissiya blocklash  yetkazib beruvchidan
//            $company_transaction_commission = new CompanyTransaction([
//                'company_id' => $product->company_id,
//                'order_id' => $this->model->id,
//                'amount' => $commissionProducer,
//                'type' => CompanyTransactionEnum::TYPE_BLOCK_COMMISION,
//                'description' => Yii::t("main", "Maxsulotga narx so'rovi berilgani uchun kommissiya bandlandi"),
//                'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                'transaction_date' => date("Y-m-d H:i:s"),
//            ]);

//            if (!($company_transaction_commission->save() && $company_transaction_zalog->save())) {
//                $this->addError("commission", $company_transaction_commission->errors);
//                $transaction->rollBack();
//
//                return false;
//            }
            try {
                VirtualTransactionResource::saveTransaction(
                    $company,
                    $company,
                    OperationTypeEnum::P_K_30101,
                    OperationTypeEnum::P_K_30201,
                    $zalog,
                    Yii::t("main", "Mahsulotga narx so'rovi uchun zalog bandlandi"),
                    OperationTypeEnum::PRODUCT_NAME_ORDER,
                    $orderId,
                    null,
                    OperationTypeEnum::BLOCK_SALE_DEPOSIT
                );
                VirtualTransactionResource::saveTransaction(
                    $company,
                    $company,
                    OperationTypeEnum::P_K_30101,
                    OperationTypeEnum::P_K_30202,
                    $zalog,
                    Yii::t("main", "Mahsulotga narx so'rovi uchun kommissiya bandlandi"),
                    OperationTypeEnum::PRODUCT_NAME_ORDER,
                    $orderId,
                    null,
                    OperationTypeEnum::BLOCK_SALE_COMMISSION
                );
                VirtualTransactionResource::saveTransaction(
                    $product->company,
                    $product->company,
                    OperationTypeEnum::P_K_30101,
                    OperationTypeEnum::P_K_30201,
                    $zalog,
                    Yii::t("main", "Maxsulotga narx so'rovi berilgani uchun garov bandlandi"),
                    OperationTypeEnum::PRODUCT_NAME_ORDER,
                    $orderId,
                    null,
                    OperationTypeEnum::BLOCK_SALE_DEPOSIT
                );
                VirtualTransactionResource::saveTransaction(
                    $product->company,
                    $product->company,
                    OperationTypeEnum::P_K_30101,
                    OperationTypeEnum::P_K_30202,
                    $zalog,
                    Yii::t("main", "Maxsulotga narx so'rovi berilgani uchun kommissiya bandlandi"),
                    OperationTypeEnum::PRODUCT_NAME_ORDER,
                    $orderId,
                    null,
                    OperationTypeEnum::BLOCK_SALE_COMMISSION
                );
            } catch (Exception $ex) {
                $transaction->rollBack();
                throw $ex;
            }
            foreach ($this->regions as $region) {
                $productRegion = new OrderRegion();

                $productRegion->region_id = $region;
                $productRegion->order_id = $orderId;

                if (!($productRegion->validate() && $productRegion->save())) {
                    $transaction->rollBack();
                    $this->addError('regions', $productRegion->errors);

                    return false;
                }
            }

            $cache = [
                'product' => array_merge(
                    $product->attributes,
                    [
                        'category_name' => $product->classifierCategory->title_uz,
                        'description' => $product->description_uz
                    ]
                ),
            ];

            $order_list = new OrderList([
                'order_id' => $orderId,
                'product_id' => $product->id,
                'quantity' => $this->count,
                'price' => $this->model->total_sum,
                'cache' => json_encode($cache)
            ]);

            if (!($order_list->validate() && $order_list->save())) {
                $transaction->rollBack();
                $this->addError('order_list', $order_list->errors);
                return false;
            }
            //TODO Yet/Berchini ozining taklifini yaratish
            $order_request = new OrderRequest([
                'company_id' => $product->company_id,
                'price' => $this->count * $product->unit_price,
                'order_id' => $orderId,
                'created_at' => date('Y-m-d H:i:s'),
                'is_winner' => 0,
                'status' => ShopEnum::ORDER_REQUEST_STATUS_DONE,
                'type' => ShopEnum::ORDER_REQUEST_TYPE_PRODUCER,
            ]);

            if (!($order_request->validate() && $order_request->save())) {
                $transaction->rollBack();
                $this->addError('order_request', $order_request->errors);
                return false;
            }
            //TODO productdan kamaytrish
            $product->updateAttributes(['quantity' => $product->quantity - $this->count]);


            //TODO  generate lot number

            $this->model->lot_number = OrderResource::generateLotNumber($this->model->id);
            if (!$this->model->save()) {
                $transaction->rollBack();
                $this->addError('lot_number', $this->model->errors);
                return false;
            }

            $classifier = Classifier::findOne($product->classifier_id);
            $classifier->updateAttributes(['saled' => $classifier->saled + 1]);
            $transaction->commit();
            return $this->model->id;
        }

        $transaction->rollBack();
        $this->addErrors($this->model->errors);
        return false;
    }
}