<?php


namespace api\modules\shop\forms;


use api\components\BaseRequest;
use api\modules\shop\resources\OrderRequestResource;
use common\enums\OrderEnum;
use common\models\WorkdayCalendar;
use yii\helpers\ArrayHelper;

class OrderRequestDeleteForm extends BaseRequest
{
    public OrderRequestResource $model;

    public function __construct(OrderRequestResource $model , $paramas = [])
    {
        $this->model = $model;

        parent::__construct($paramas);
    }

    public function getResult()
    {
        $company_id = \Yii::$app->user->identity->company_id;

        $holidays = ArrayHelper::map(WorkdayCalendar::findAll(['type' => WorkdayCalendar::HOLIDAY]), 'local_date', 'local_date');
        $workDays = ArrayHelper::map(WorkdayCalendar::findAll(['type' => WorkdayCalendar::WORKDAY]), 'local_date', 'local_date');

        $expired_date = addDaysExcludingWeekends($this->model->created_at, 2, $workDays, $holidays);

        if (!empty($this->model->deleted_at) || $this->model->status == OrderEnum::STATUS_DELETED_ORDER_REQUEST){
            $this->addError('model' , "Bu so'rov allaqachon o'chirilgan");
        }

        if ($company_id != $this->model->company_id){
            $this->addError('company_id' , "Siz faqat o‘zingizga tegishli so‘rovni o‘chira olasiz.");
            return false;
        }

        if (strtotime($expired_date) < time()){
            $this->addError('created_at' , "O'chirish vaqti o'tib ketgan");
            return false;
        }

        $this->model->status = OrderEnum::STATUS_DELETED_ORDER_REQUEST;
        $this->model->deleted_at = date('Y-m-d H:i:s');
        if (!$this->model->save()){
            $this->addErrors($this->model->errors);
            return false;
        }

        return true;
    }
}