<?php


namespace api\modules\shop\forms;


use api\components\BaseRequest;
use api\modules\shop\resources\OrderRequestResource;
use common\models\WorkdayCalendar;
use yii\helpers\ArrayHelper;

class OrderRequestUpdateForm extends BaseRequest
{
    public $price;
    public OrderRequestResource $model;

    public function rules()
    {
        return [
            ['price' , 'required' , 'message' => t('{attribute} yuborish majburiy')],
            [['price'], 'number'],
        ];
    }

    public function __construct(OrderRequestResource $model , $paramas = [])
    {
        $this->model = $model;

        parent::__construct($paramas);
    }

    public function getResult()
    {
        $holidays = ArrayHelper::map(WorkdayCalendar::findAll(['type' => WorkdayCalendar::HOLIDAY]), 'local_date', 'local_date');
        $workDays = ArrayHelper::map(WorkdayCalendar::findAll(['type' => WorkdayCalendar::WORKDAY]), 'local_date', 'local_date');

        $expired_date = addDaysExcludingWeekends($this->model->created_at, 2, $workDays, $holidays);

        if (!empty($this->model->deleted_at)){
            $this->addError('error' ,"Bu o'chirilgan so'rov");
        }

        if (strtotime($expired_date) < time()){
            $this->addError('created_at' , "O'zgartirish vaqti o'tib ketgan");
            return false;
        }

        $this->model->price = $this->price;
        $this->model->updated_at = date('Y-m-d H:i:s');

        if (!$this->model->save()){
            $this->addError('error' , "Narx so'rovini o'zgartirib bo'lmadi");
            return false;
        }

        return true;
    }
}