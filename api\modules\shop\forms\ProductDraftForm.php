<?php

namespace api\modules\shop\forms;

use Yii;
use api\components\BaseRequest;
use api\modules\shop\resources\ProductDraftResource;
use common\enums\ProductEnum;
use common\enums\StatusEnum;
use yii\db\Exception;

class ProductDraftForm extends BaseRequest
{
    public ?string $platform_display = null;

    public function __construct(
        public ?ProductDraftResource $model = null,
        $params = []
    )
    {
        parent::__construct($params);
    }

    public function rules(): array
    {
        return [
            ['platform_display', 'required'],
            ['platform_display', 'in', 'range' => [ProductEnum::PLATFORM_DISPLAY_NATIONAL, ProductEnum::PLATFORM_DISPLAY_E_SHOP]],
        ];
    }

    /**
     * @throws Exception
     * @throws \yii\base\Exception
     */
    public function getResult(): false|int
    {
        $user = Yii::$app->user->identity;
        $company = $user->company;
        $this->model->company_id = $company->id;
        $this->model->status = StatusEnum::STATUS_ACTIVE;
        if ($this->model->save(false)) {
            return $this->model->id;
        }
        $this->addErrors($this->model->errors);
        return false;
    }
}