<?php

namespace api\modules\shop\forms;

use api\components\BaseRequest;
use api\modules\common\resources\ClassifierCategoryResource;
use api\modules\common\resources\ClassifierResource;
use api\modules\common\resources\ProductDraftClassifierUnitResource;
use api\modules\common\resources\RegionResource;
use api\modules\shop\resources\ProductDraftResource;
use common\enums\ProductEnum;
use common\enums\StatusEnum;
use common\models\Classifier;
use common\models\File;
use common\models\Region;
use common\models\shop\ProductDraftClassifierUnit;
use common\models\shop\ProductDraftFile;
use common\models\shop\ProductDraftRegion;
use Yii;
use yii\db\Exception;

class ProductDraftUpdateForm extends BaseRequest
{
    public ?string $platform_display = null;
    public ?int $classifier_id = null;
    public ?int $classifier_category_id = null;
    public ?string $brand_title = null;
    public ?string $made_by = null; // kim tomonidan ishlab chiqarilgan
    public ?int $country_id = null; // qaysi mamlakatda ishlab chiqarilgan
    public ?int $quantity = null;
    public ?int $unit_id = null;
    public ?int $min_order = null;
    public ?int $max_order = null;
    public ?int $unit_price = null;
    public ?int $year = null;
    public ?string $expire_date = null;
    public ?int $warranty_period = null;
    public ?int $delivery_period = null;
    public ?string $description_uz = null;
    public ?string $description_ru = null;
    public array $regions = [];
    public array $product_files = [];
    public array $product_images = [];
    public bool $auto_renewal = false;
    public array $classifier_units = [];
    public ?int $main_image = null;

    public function __construct(
        public ?ProductDraftResource $model = null,
                                     $params = []
    )
    {
        parent::__construct($params);
    }

    public function rules(): array
    {
        return [
            [
                [
                    'platform_display', 'classifier_id',
                    'classifier_category_id', 'brand_title',
                    'made_by', 'country_id', 'quantity', 'unit_id',
                    'min_order', 'max_order', 'unit_price',
                    'year', 'expire_date', 'warranty_period', 'delivery_period', 'auto_renewal'
                ], 'safe',
            ],
            ['platform_display', 'in', 'range' => [ProductEnum::PLATFORM_DISPLAY_NATIONAL, ProductEnum::PLATFORM_DISPLAY_E_SHOP]],
            [
                [
                    'classifier_id', 'classifier_category_id',
                    'quantity', 'min_order', 'max_order',
                    'unit_price', 'year', 'warranty_period',
                    'delivery_period', 'country_id'
                ], 'integer'
            ],
            [['brand_title', 'made_by', ], 'string', 'max' => 255],
            ['expire_date', 'date', 'format' => 'php:d/m/Y'],
            [['description_uz','description_ru',], 'string'],
            [['description_uz','description_ru', 'regions', 'product_files', 'product_images', 'classifier_units','main_image'], 'safe'],
            [['classifier_id'], 'exist', 'skipOnError' => true, 'targetClass' => ClassifierResource::class, 'targetAttribute' => ['classifier_id' => 'id']],
            [['classifier_category_id',], 'exist', 'skipOnError' => true, 'targetClass' => ClassifierCategoryResource::class, 'targetAttribute' => ['classifier_category_id' => 'id']],
            ['country_id', 'exist', 'skipOnError' => true, 'targetClass' => RegionResource::class, 'targetAttribute' => ['country_id' => 'id'], 'filter' => ['type' => Region::TYPE_COUNTRY]],
        ];
    }

    /**
     * @throws Exception
     * @throws \yii\base\Exception
     */
    public function getResult()
    {
        $user = Yii::$app->user->identity;
        $company = $user->company;
        $transaction = Yii::$app->db->beginTransaction();

        $this->unit_price = $this->unit_price * 100;
        $classifier = null;
        if ($this->classifier_id)
        {
            $classifier = Classifier::findOne($this->classifier_id);
            if (!$classifier) throw new Exception(t('Klassifikator topilmadi !!!'));
        }

        $this->model->country_id = $this->country_id;
        $this->model->company_id = $company->id;
        $this->model->auto_renewal = $this->auto_renewal;
        if ($classifier)
        {
            $this->model->classifier_category_id = $classifier->classifier_category_id;
            $this->model->type = $classifier->type;
        }
        $att = $this->attributes;
        $this->model->setAttributes($att, false);
        $this->model->price = $this->quantity * $this->unit_price;
        $this->model->status = StatusEnum::STATUS_ACTIVE;
//        $bhm = Bmh::getAmount();
//        if ($user->isBudget) {
//            if ($this->model->type === ProductEnum::PRODUCT_TYPE_PRODUCT) {
//                $limit = $bhm * 25000;
//                if ($this->model->price >= $limit)
//                {
//                    $this->addError('unit_price', t("BHMni 25000 barobaridan oshmasligi kerak"));
//                }
//            } else if ($this->model->type === ProductEnum::PRODUCT_TYPE_SERVICE || $this->model->type === ProductEnum::PRODUCT_TYPE_WORK) {
//                $limit = $bhm * 50;
//                if ($this->model->price >= $limit)
//                {
//                    $this->addError('unit_price', t("BHMni 50 barobaridan oshmasligi kerak"));
//                }
//            }
//        } else {
//            if ($this->model->type === ProductEnum::PRODUCT_TYPE_PRODUCT) {
//                $limit = $bhm * 25000;
//                if ($this->model->price >= $limit)
//                {
//                    $this->addError('unit_price', t("BHMni 25000 barobaridan oshmasligi kerak"));
//                }
//            } else if ($this->model->type === ProductEnum::PRODUCT_TYPE_SERVICE || $this->model->type === ProductEnum::PRODUCT_TYPE_WORK) {
//                $limit = $bhm * 100;
//                if ($this->model->price >= $limit)
//                {
//                    $this->addError('unit_price', t("BHMni 100 barobaridan oshmasligi kerak"));
//                }
//            }
//        }

        if ($this->model->save(false)) {
            $productId = $this->model->id;
            foreach ($this->regions ?? [] as $region) {
                if ($this->platform_display == ProductEnum::PLATFORM_DISPLAY_NATIONAL) {
                    $r = Region::findOne($region);
                    if (!$r) {
                        $transaction->rollBack();
                        $this->addError('error', "Viloyat/Tuman topilmadi");
                        return false;
                    }
                    if ($company->region_id != $r->parent_id) {
                        $transaction->rollBack();
                        $this->addError('error', "Milliy do'konda yetkazib beruvchi yuridik manzili bir xil bo'lishi kerak");
                        return false;
                    }
                }
                $condition = [
                    'region_id' => $region,
                    'product_id' => $productId
                ];
                $productRegion = ProductDraftRegion::findOne($condition) ?: new ProductDraftRegion($condition);
                if (!($productRegion->validate() && $productRegion->save())) {
                    $transaction->rollBack();
                    $this->addError('regions', $productRegion->errors);
                    return false;
                }
            }

            foreach ($this->product_files ?? [] as $fileID) {
                $condition = [
                    'file_id' => $fileID,
                    'product_id' => $productId,
                    'type' => ProductDraftFile::TYPE_FILE
                ];
                $productFile = ProductDraftFile::findOne($condition) ?: new ProductDraftFile($condition);
                if (!($productFile->validate() && $productFile->save())) {
                    $transaction->rollBack();
                    $this->addError('product_files', $productFile->errors);
                    return false;
                }
            }

            foreach ($this->product_images ?? [] as $imageID) {
                $condition = [
                    'file_id' => $imageID,
                    'product_id' => $productId,
                    'type' => ProductDraftFile::TYPE_IMAGE
                ];
                $productImage = ProductDraftFile::findOne($condition) ?: new ProductDraftFile($condition);
                if (!($productImage->validate() && $productImage->save())) {
                    $transaction->rollBack();
                    $this->addError('product_images', $productImage->errors);
                    return false;
                }
            }

            if ($this->main_image) {
                $file = File::findOne($this->main_image);
                $file->updateAttributes(['is_main' => true]);
            }

            foreach ($this->classifier_units ?? [] as $classifierUnitID) {
                $condition = [
                    'classifier_id' => $classifier->id,
                    'product_id' => $productId,
                    'classifier_properties_id' => $classifierUnitID
                ];
                $productClassifier = ProductDraftClassifierUnitResource::findOne($condition) ?: new ProductDraftClassifierUnit($condition);
                if (!($productClassifier->validate() && $productClassifier->save())) {
                    $transaction->rollBack();
                    $this->addError('classifier_units', $productClassifier->errors);
                }
            }

            $transaction->commit();
            return $this->model;
        }

        $transaction->rollBack();
        $this->addErrors($this->model->errors);
        return false;
    }
}