<?php

namespace api\modules\shop\forms;

use api\components\BaseRequest;
use common\enums\ProductEnum;
use common\models\shop\Product;
use yii\helpers\ArrayHelper;

class ProductFilterForm extends BaseRequest
{
    public $platform_display;
    public $search;

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['platform_display'], 'string', 'max' => 15],
            [['platform_display'], 'in', 'range' => [
                ProductEnum::PLATFORM_DISPLAY_E_SHOP,
                ProductEnum::PLATFORM_DISPLAY_NATIONAL
            ]],
            [['search'], 'string', 'max' => 255],
            [['search'], 'trim'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'platform_display' => t('Platform turi'),
            'search' => t('Qidiruv'),
        ];
    }

    /**
     * Mahsulotlarni status bo'yicha guruhlab qaytarish
     * @return array
     */
    public function getResult()
    {
        // Barcha status qiymatlari
        $statusList = [
            ProductEnum::SHOP_STATE_NEW,
            ProductEnum::SHOP_STATE_RETURN_MODERATOR,
            ProductEnum::SHOP_STATE_NO_MONEY,
            ProductEnum::SHOP_STATE_ACTIVE,
            ProductEnum::SHOP_STATE_IN_ACTIVE,
            ProductEnum::SHOP_STATE_DELETED,
            ProductEnum::SHOP_STATE_CANCEL,
        ];

        $result = [];

        foreach ($statusList as $status) {
            $query = Product::find()
                ->select([
                    'id', 'title', 'brand_title', 'description_uz', 'description_ru',
                    'price', 'quantity', 'min_order', 'max_order', 'state', 'status',
                    'platform_display', 'type', 'created_at', 'active_date'
                ])
                ->andWhere(['state' => $status]);

            // Platform display filter
            if (!empty($this->platform_display)) {
                $query->andWhere(['platform_display' => $this->platform_display]);
            }

            // Search filter
            if (!empty($this->search)) {
                $query->andWhere([
                    'or',
                    ['like', 'title', $this->search],
                    ['like', 'brand_title', $this->search],
                    ['like', 'description_uz', $this->search],
                    ['like', 'description_ru', $this->search],
                ]);
            }

            // Faqat o'chirilmagan mahsulotlar
            $query->andWhere(['deleted_at' => null]);

            // Oxirgi qo'shilganlar birinchi
            $query->orderBy(['id' => SORT_DESC]);

            $products = $query->all();
            $count = $query->count();

            $result[$status] = [
                'status' => $status,
                'status_name' => $this->getStatusName($status),
                'count' => $count,
                'products' => $this->formatProducts($products),
            ];
        }

        return $result;
    }

    /**
     * Status nomini olish
     * @param int $status
     * @return string
     */
    private function getStatusName($status)
    {
        $statusNames = [
            ProductEnum::SHOP_STATE_NEW => t('Moderator tekshiruvida'),
            ProductEnum::SHOP_STATE_RETURN_MODERATOR => t('Moderatordan qaytarilgan'),
            ProductEnum::SHOP_STATE_NO_MONEY => t('Mablag\' yetarli emas'),
            ProductEnum::SHOP_STATE_ACTIVE => t('Sotuvda'),
            ProductEnum::SHOP_STATE_IN_ACTIVE => t('Aktiv holatda emas'),
            ProductEnum::SHOP_STATE_DELETED => t('O\'chirilgan'),
            ProductEnum::SHOP_STATE_CANCEL => t('Rad qilingan'),
        ];

        return ArrayHelper::getValue($statusNames, $status, t('Noma\'lum status'));
    }

    /**
     * Mahsulotlarni formatlash
     * @param array $products
     * @return array
     */
    private function formatProducts($products)
    {
        $result = [];
        
        foreach ($products as $product) {
            $result[] = [
                'id' => $product->id,
                'title' => $product->title,
                'brand_title' => $product->brand_title,
                'description_uz' => $product->description_uz,
                'description_ru' => $product->description_ru,
                'price' => $product->price,
                'quantity' => $product->quantity,
                'min_order' => $product->min_order,
                'max_order' => $product->max_order,
                'state' => $product->state,
                'status' => $product->status,
                'platform_display' => $product->platform_display,
                'type' => $product->type,
                'created_at' => $product->created_at,
                'active_date' => $product->active_date,
            ];
        }

        return $result;
    }
}
