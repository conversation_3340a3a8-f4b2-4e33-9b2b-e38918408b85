<?php

namespace api\modules\shop\forms;

use api\components\BaseRequest;
use api\modules\shop\resources\ProductResource;
use common\enums\ProductEnum;
use common\models\shop\Product;
use yii\helpers\ArrayHelper;

class ProductFilterForm extends BaseRequest
{
    public $platform_display;
    public $search;

    public function rules()
    {
        return [
            [['platform_display'], 'string', 'max' => 25],
            [['platform_display'], 'in', 'range' => [
                ProductEnum::PLATFORM_DISPLAY_E_SHOP,
                ProductEnum::PLATFORM_DISPLAY_NATIONAL
            ]],
            [['search'], 'string', 'max' => 255],
            [['search'], 'trim'],
        ];
    }

    public function getResult()
    {
        // Barcha status qiymatlari
        $statusList = [
            ProductEnum::SHOP_STATE_NEW,
            ProductEnum::SHOP_STATE_RETURN_MODERATOR,
            ProductEnum::SHOP_STATE_NO_MONEY,
            ProductEnum::SHOP_STATE_ACTIVE,
            ProductEnum::SHOP_STATE_IN_ACTIVE,
            ProductEnum::SHOP_STATE_DELETED,
            ProductEnum::SHOP_STATE_CANCEL,
        ];

        $result = [];

        foreach ($statusList as $status) {
            $query = ProductResource::find()
                ->select([
                    'id', 'title', 'brand_title', 'description_uz', 'description_ru',
                    'price', 'quantity', 'min_order', 'max_order', 'state', 'status',
                    'platform_display', 'type', 'created_at', 'active_date'
                ])
                ->andWhere(['state' => $status]);

            if (!empty($this->platform_display)) {
                $query->andWhere(['platform_display' => $this->platform_display]);
            }

            if (!empty($this->search)) {
                $query->andWhere([
                    'or',
                    ['ilike', 'title', $this->search],
                    ['ilike', 'brand_title', $this->search],
                    ['ilike', 'description_uz', $this->search],
                    ['ilike', 'description_ru', $this->search],
                ]);
            }

            $query->andWhere(['deleted_at' => null]);

            $query->orderBy(['id' => SORT_DESC]);

            $paginatedResult = paginate($query);

            $result[] = [
                'status' => $status,
                'status_name' => $this->getStatusName($status),
                'count' => $paginatedResult['meta']['totalCount'],
                'data' => $paginatedResult['data'],
                'meta' => $paginatedResult['meta'],
            ];
        }

        return $result;
    }

    private function getStatusName($status)
    {
        $statusNames = [
            ProductEnum::SHOP_STATE_NEW => t('Moderator tekshiruvida'),
            ProductEnum::SHOP_STATE_RETURN_MODERATOR => t('Moderatordan qaytarilgan'),
            ProductEnum::SHOP_STATE_NO_MONEY => t('Mablag\' yetarli emas'),
            ProductEnum::SHOP_STATE_ACTIVE => t('Sotuvda'),
            ProductEnum::SHOP_STATE_IN_ACTIVE => t('Aktiv holatda emas'),
            ProductEnum::SHOP_STATE_DELETED => t('O\'chirilgan'),
            ProductEnum::SHOP_STATE_CANCEL => t('Rad qilingan'),
        ];

        return ArrayHelper::getValue($statusNames, $status, t('Noma\'lum status'));
    }


}
