<?php

namespace api\modules\shop\forms;

use api\modules\auction\resources\draft\AuctionDraftResource;
use api\modules\shop\resources\ProductClassifierUnitResource;
use common\models\Bmh;
use common\models\shop\ProductFile;
use common\models\shop\ProductRegion;
use Yii;
use api\components\BaseRequest;
use api\modules\common\resources\ClassifierCategoryResource;
use api\modules\common\resources\ClassifierPropertiesResource;
use api\modules\common\resources\ClassifierResource;
use api\modules\shop\resources\ProductResource;
use common\enums\ProductEnum;
use common\enums\StatusEnum;
use common\models\Classifier;
use common\models\File;
use common\models\Region;
use yii\db\Exception;

class SendToModerationForm extends BaseRequest
{
    public ?int $id = null;
    public ?string $platform_display = null;
    public ?int $classifier_id = null;
    public ?int $classifier_category_id = null;
    public ?string $brand_title = null;
    public ?string $made_by = null; // kim tomonidan ishlab chiqarilgan
    public ?string $made_in = null; // qaysi mamlakatda ishlab chiqarilgan
    public ?int $quantity = null;
    public ?int $unit_id = null;
    public ?int $min_order = null;
    public ?int $max_order = null;
    public ?int $unit_price = null;
    public ?int $year = null;
    public ?string $expire_date = null;
    public ?int $warranty_period = null;
    public ?int $delivery_period = null;
    public ?string $description_uz = null;
    public ?string $description_ru = null;
    public array $regions = [];
    public array $product_files = [];
    public array $product_images = [];
    public bool $auto_renewal = false;
    public array $classifier_units = [];
    public ?int $main_image = null;
    public function __construct(
        public ?ProductResource $model = null,
        $params = []
    )
    {
        parent::__construct($params);
    }
    public function rules(): array
    {
        return [
            [
                [
                    'id','platform_display', 'classifier_id',
                    'classifier_category_id', 'brand_title',
                    'made_by', 'made_in', 'quantity', 'unit_id',
                    'min_order', 'max_order', 'unit_price',
                    'year', 'expire_date', 'warranty_period', 'delivery_period',
                ], 'required',
            ],
            ['platform_display', 'in', 'range' => [ProductEnum::PLATFORM_DISPLAY_NATIONAL, ProductEnum::PLATFORM_DISPLAY_E_SHOP]],
            [
                [
                    'classifier_id', 'classifier_category_id',
                    'quantity', 'min_order', 'max_order',
                    'unit_price', 'year', 'warranty_period',
                    'delivery_period',
                ], 'integer'
            ],
            [['brand_title', 'made_by', 'made_in',], 'string', 'max' => 255],
            ['expire_date', 'date', 'format' => 'php:d/m/Y'],
            [['description_uz','description_ru',], 'string'],
            [['description_uz','description_ru', 'regions', 'product_files', 'product_images', 'classifier_units','main_image'], 'safe'],
            [['classifier_id'], 'exist', 'skipOnError' => true, 'targetClass' => ClassifierResource::class, 'targetAttribute' => ['classifier_id' => 'id']],
            [['classifier_category_id',], 'exist', 'skipOnError' => true, 'targetClass' => ClassifierCategoryResource::class, 'targetAttribute' => ['classifier_category_id' => 'id']],
        ];
    }

    /**
     * @throws Exception
     * @throws \yii\base\Exception
     */
    public function getResult()
    {
        $user = Yii::$app->user->identity;
        $company = $user->company;
        $transaction = Yii::$app->db->beginTransaction();

        $this->unit_price = $this->unit_price * 100;
        if (count($this->regions) == 0) {
            throw new Exception(t("Hudud tanlash majburiy"));
        }
        if (count($this->product_images) == 0) {
            throw new Exception(t("File yoki image tanlanmagan"));
        }

        if ($this->platform_display == ProductEnum::PLATFORM_DISPLAY_NATIONAL && count($this->product_files) == 0)
        {
            throw new Exception(t("File yuborish kerak"));
        }

        $this->main_image = $this->product_images[0];

        $classifier = Classifier::findOne($this->classifier_id);
        if (!$classifier) throw new Exception(t('Klassifikator topilmadi !!!'));

        $checkClassifierPropertiesIDs = ClassifierPropertiesResource::getRequiredPropertiesIDs($classifier->id);
        if (!empty(array_diff($checkClassifierPropertiesIDs, $this->classifier_units))) {
            throw new Exception(t("Majburiy klasifikator birligi tanlanmagan"));
        }
        $draftModel = AuctionDraftResource::findOne(['id' => $this->id, 'deleted_at' => null]);
        if (!$draftModel) throw new Exception("Draft model not found");
        $this->model->company_id = $company->id;
        $this->model->auto_renewal = $this->auto_renewal;
        $this->model->classifier_category_id = $classifier->classifier_category_id;
        $this->model->type = $classifier->type;
        $att = $this->attributes;
        $this->model->setAttributes($att, false);
        $this->model->price = $this->quantity * $this->unit_price;
        $this->model->status = StatusEnum::STATUS_NEW;
        $this->model->state = ProductEnum::SHOP_STATE_NEW;
        $bhm = Bmh::getAmount();
        if ($user->isBudget) {
            if ($this->model->type === ProductEnum::PRODUCT_TYPE_PRODUCT) {
                $limit = $bhm * 25000;
                if ($this->model->price >= $limit)
                {
                    $this->addError('unit_price', t("BHMni 25000 barobaridan oshmasligi kerak"));
                }
            } else if ($this->model->type === ProductEnum::PRODUCT_TYPE_SERVICE || $this->model->type === ProductEnum::PRODUCT_TYPE_WORK) {
                $limit = $bhm * 50;
                if ($this->model->price >= $limit)
                {
                    $this->addError('unit_price', t("BHMni 50 barobaridan oshmasligi kerak"));
                }
            }
        } else {
            if ($this->model->type === ProductEnum::PRODUCT_TYPE_PRODUCT) {
                $limit = $bhm * 25000;
                if ($this->model->price >= $limit)
                {
                    $this->addError('unit_price', t("BHMni 25000 barobaridan oshmasligi kerak"));
                }
            } else if ($this->model->type === ProductEnum::PRODUCT_TYPE_SERVICE || $this->model->type === ProductEnum::PRODUCT_TYPE_WORK) {
                $limit = $bhm * 100;
                if ($this->model->price >= $limit)
                {
                    $this->addError('unit_price', t("BHMni 100 barobaridan oshmasligi kerak"));
                }
            }
        }

        if ($this->model->save()) {
            $productId = $this->model->id;
            foreach ($this->regions ?? [] as $region) {
                if ($this->platform_display == ProductEnum::PLATFORM_DISPLAY_NATIONAL) {
                    $r = Region::findOne($region);
                    if (!$r) {
                        $transaction->rollBack();
                        $this->addError('error', "Viloyat/Tuman topilmadi");
                        return false;
                    }
                    if ($company->region_id != $r->parent_id) {
                        $transaction->rollBack();
                        $this->addError('error', "Milliy do'konda yetkazib beruvchi yuridik manzili bir xil bo'lishi kerak");
                        return false;
                    }
                }
                $condition = [
                    'region_id' => $region,
                    'product_id' => $productId
                ];
                $productRegion = ProductRegion::findOne($condition) ?: new ProductRegion($condition);
                if (!($productRegion->validate() && $productRegion->save())) {
                    $transaction->rollBack();
                    $this->addError('regions', $productRegion->errors);
                    return false;
                }
            }

            foreach ($this->product_files ?? [] as $fileID) {
                $condition = [
                    'file_id' => $fileID,
                    'product_id' => $productId,
                    'type' => ProductFile::TYPE_FILE
                ];
                $productFile = ProductFile::findOne($condition) ?: new ProductFile($condition);
                if (!($productFile->validate() && $productFile->save())) {
                    $transaction->rollBack();
                    $this->addError('product_files', $productFile->errors);
                    return false;
                }
            }

            foreach ($this->product_images ?? [] as $imageID) {
                $condition = [
                    'file_id' => $imageID,
                    'product_id' => $productId,
                    'type' => ProductFile::TYPE_IMAGE
                ];
                $productImage = ProductFile::findOne($condition) ?: new ProductFile($condition);
                if (!($productImage->validate() && $productImage->save())) {
                    $transaction->rollBack();
                    $this->addError('product_images', $productImage->errors);
                    return false;
                }
            }

            if ($this->main_image) {
                $file = File::findOne($this->main_image);
                $file->updateAttributes(['is_main' => true]);
            }

            foreach ($this->classifier_units ?? [] as $classifierUnitID) {
                $condition = [
                    'classifier_id' => $classifier->id,
                    'product_id' => $productId,
                    'classifier_properties_id' => $classifierUnitID
                ];
                $productClassifier = ProductClassifierUnitResource::findOne($condition) ?: new ProductClassifierUnitResource($condition);
                if (!($productClassifier->validate() && $productClassifier->save())) {
                    $transaction->rollBack();
                    $this->addError('classifier_units', $productClassifier->errors);
                }
            }
            $draftModel->delete();
            $transaction->commit();
            return $this->model->id;
        }

        $transaction->rollBack();
        $this->addErrors($this->model->errors);
        return false;
    }
}