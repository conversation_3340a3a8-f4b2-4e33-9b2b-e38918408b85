<?php

namespace api\modules\shop\resources;

use api\modules\common\resources\CompanyShortResource;
use api\modules\common\resources\FileResource;
use common\enums\ContractEnum;
use common\models\Contract;
use common\models\ContractRequest;

class ContractResource extends Contract
{
    public function fields()
    {
        return [
            'lot_number' => function (ContractResource $model) {
                return $model->order?->lot_number;
            },
            'order',
            'price' => function ($model) {
                return $model->price / 100;
            },
            'status',
            'created_at',
        ];
    }

    public function extraFields()
    {
        return [
            'order',
            'auction',
            'producer',
            'customer',
            'contractCancelRequest',
            'factureFile',
            'file',
            'checkReserve'
        ];
    }

    public function getContractCancelRequest()
    {
        return $this->hasMany(ContractCancelRequestResource::class, ['contract_id' => 'id'])->andWhere(['status' => ContractEnum::STATUS_REQUEST_NEW])->orderBy(['id' => SORT_DESC]);
    }


    public function getAuction()
    {
        return $this->hasOne(AuctionMyLotsResource::class, ['id' => 'auction_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getOrder()
    {
        return $this->hasOne(OrderResource::class, ['id' => 'order_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFile()
    {
        return $this->hasOne(FileResource::class, ['id' => 'file_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getCustomer()
    {
        return $this->hasOne(CompanyShortResource::class, ['id' => 'customer_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getProducer()
    {
        return $this->hasOne(CompanyShortResource::class, ['id' => 'producer_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFactureFile()
    {
        return $this->hasOne(FileResource::class, ['id' => 'facture_file_id']);
    }

    public function getCheckReserve()
    {
        $request = ContractRequest::findOne(['contract_id' => $this->id]);
        return $request?->status;
    }
}
