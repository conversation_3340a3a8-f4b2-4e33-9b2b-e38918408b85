<?php


namespace api\modules\shop\resources;


use api\modules\client\resources\ClassifierResource;
use api\modules\common\resources\ClassifierCategoryResource;
use api\modules\common\resources\ProductDraftClassifierUnitResource;
use api\modules\common\resources\RegionResource;
use common\models\shop\ProductDraft;
use common\models\shop\ProductDraftRegion;

class ProductDraftResource extends ProductDraft
{
    public function fields(): array
    {
        return [
            'id',
            'classifier_category_id' => function($model){
                return ClassifierCategoryResource::findOne($model->classifier_category_id);
            },
            'classifier_id'=> function($model){
                return ClassifierResource::findOne($model->classifier_id);
            },
            'brand_title',
            'company_id',
            'made_by',
            'made_in',
            'quantity',
            'min_order',
            'max_order',
            'unit_price' => 'unitPrice',
            'price' => 'sumPrice',
            'expire_date',
            'year',
            'warranty_period',
            'delivery_period',
            'description_uz',
            'description_ru',
            'region' => 'regions',
            'images' => 'images',
            'files' => 'files',
            'is_have_license',
            'platform_display',
            'unit_id' => 'unit',
            'auto_renewal'
        ];
    }

    public function extraFields()
    {
        return [
            'classifierUnit',
            'unit',
            'regions',
            'files',
            'images',
        ];
    }

    public function getRegions(): array
    {
        $regionsID = ProductDraftRegion::find()->select('region_id')->where(['product_id' => $this->id])->column();
        if (!empty($regionsID))
        {
            return RegionResource::findAll(['id' => $regionsID]);
        }
        return [];
    }
    public function getSumPrice()
    {
        return $this->price / 100;
    }

    public function getUnitPrice()
    {
        return $this->unit_price / 100;
    }

    public function getClassifierUnit()
    {
        return $this->hasMany(ProductDraftClassifierUnitResource::class, ['product_id' => 'id'])->with('classifierProperties');
    }
}