<?php


namespace api\modules\tender\controllers;


use api\components\ApiController;
use api\modules\tender\filters\TenderDmbatFilter;
use api\modules\tender\filters\TenderProtestFilter;
use api\modules\tender\filters\TenderActiveFilter;
use api\modules\tender\filters\TenderActiveLotsFilter;
use api\modules\tender\filters\TenderAgreementFilter;
use api\modules\tender\filters\TenderCommissionRateStatFilter;
use api\modules\tender\filters\TenderCommissionStatFilter;
use api\modules\tender\filters\TenderCommissionVoteFilter;
use api\modules\tender\filters\TenderContractListFilter;
use api\modules\tender\filters\TenderDeletedFilter;
use api\modules\tender\filters\TenderFilter;
use api\modules\tender\filters\TenderForChairmanToPublishFilter;
use api\modules\tender\filters\TenderForCommissionFilter;
use api\modules\tender\filters\TenderForSecretaryFilter;
use api\modules\tender\filters\TenderModeratorFilter;
use api\modules\tender\filters\TenderWithOfferFilter;
use api\modules\tender\filters\TenderRealizedFilter;
use api\modules\tender\forms\TenderAcceptContractForm;
use api\modules\tender\forms\TenderAcceptContractSecondForm;
use api\modules\tender\forms\TenderCommissionMemberUpdateForm;
use api\modules\tender\forms\TenderCommissionTenderRatingVoteForm;
use api\modules\tender\forms\TenderCommissionVoteForm;
use api\modules\tender\forms\TenderDeleteForm;
use api\modules\tender\forms\TenderForm;
use api\modules\tender\forms\TenderForSecretaryQualificationRateForm;
use api\modules\tender\forms\TenderForSecretaryRateForm;
use api\modules\tender\forms\TenderPublishForm;
use api\modules\tender\forms\TenderReceiveProductForm;
use api\modules\tender\forms\TenderReleaseForm;
use api\modules\tender\forms\TenderRequestCheckRatingEndForm;
use api\modules\tender\forms\TenderRequestConflictInterestForm;
use api\modules\tender\forms\TenderRequestDisclassificationForm;
use api\modules\tender\forms\TenderSendContractForm;
use api\modules\tender\forms\TenderUpdateBySecretaryForm;
use api\modules\tender\forms\TenderUpdateChairmanForm;
use api\modules\tender\forms\TenderUpdateChairmanMakeDiscussionProtocolForm;
use api\modules\tender\forms\TenderUpdateChairmanWithProtocolForm;
use api\modules\tender\forms\TenderUpdateForm;
use api\modules\tender\resources\ContractResource;
use api\modules\tender\resources\TenderActiveLotsDetailResource;
use api\modules\tender\resources\TenderResourceForSecretary;
use api\modules\tender\resources\TenderResource;
use api\modules\tender\resources\TenderResourceForCommission;
use app\modules\tender\forms\TenderForSecretaryRateLocalProducerForm;
use common\enums\PkcsEnum;
use common\enums\TenderEnum;
use kartik\mpdf\Pdf;
use Yii;
use yii\base\Exception;
use yii\web\NotFoundHttpException;

class TenderController extends ApiController
{

    /**
     * tender page uchun start
     * */

    public function actionActiveLots()
    {
        return $this->sendResponse(
            new TenderActiveLotsFilter(TenderEnum::TYPE_TENDER),
            Yii::$app->request->queryParams
        );
    }

    public function actionActiveLotsSelection()
    {
        return $this->sendResponse(
            new TenderActiveLotsFilter(TenderEnum::TYPE_INVITE),
            Yii::$app->request->queryParams
        );
    }

    //prodda aktiv lotni ko'rish, landing
    public function actionActiveLotsView($id)
    {
        //active-lots-view
        $model = TenderActiveLotsDetailResource::find()
            ->where(['state' => TenderEnum::STATE_READY, 'id' => $id])->one();

        if (!Yii::$app->user->isGuest) {
            $model->views = $model->views + 1;
            if (!$model->save(false)) {
                return $model->errors;
            }
        }

        return $this->sendModel($model);
    }


    public function actionIndex()
    {
        return $this->sendResponse(
            new TenderModeratorFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionIndexDmbat()
    {
        //tender/index-dmbat
        return $this->sendResponse(
            new TenderDmbatFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionIndexAgreement() //kelishishdagi
    {
        //tender/index-agreement
        return $this->sendResponse(
            new TenderAgreementFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionIndexDeleted() //o'chirilgan
    {
        return $this->sendResponse(
            new TenderDeletedFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionIndexUnRealized() //amalga oshmagan
    {
        return $this->sendResponse(
            new TenderFilter(TenderEnum::STATE_NOT_REALIZED),
            Yii::$app->request->queryParams
        );
    }

    public function actionIndexActive()
    {
        return $this->sendResponse(
            new TenderActiveFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionIndexRatePending()
    {
        return $this->sendResponse(
            new TenderFilter(TenderEnum::STATE_READY_TO_RATING),
            Yii::$app->request->queryParams
        );
    }

    public function actionIndexVoting()
    {
        return $this->sendResponse(
            new TenderFilter(TenderEnum::STATE_READY_TO_VOTE, [TenderEnum::STATE_READY_TO_MAKE_PROTOCOL]),
            Yii::$app->request->queryParams
        );
    }

    public function actionIndexRealized()
    {
        return $this->sendResponse(
            new TenderRealizedFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionCreate()
    {
        $body = Yii::$app->request->bodyParams;
        $decodedPks7 = $this->verifyPkcs7($body);

        return $this->sendResponsePost(
            new TenderForm(new TenderResource()),
            $decodedPks7,
            $body['pkcs7'],
            PkcsENum::PKCS7_TYPE_TENDER_CREATE
        );
    }

    public function actionGetProtocol($id)
    {

        $model = TenderResource::find()
//            ->where(['>=', 'state', TenderEnum::STATE_MADE_PROTOCOL])
            ->andWhere(['id' => $id])
            ->one();

        if (!$model) throw new NotFoundHttpException("Protokol shakllantirilmagan.");

        $pdf = new Pdf([
            'mode' => Pdf::MODE_UTF8,
//            'format' => Pdf::FORMAT_A4,
//            'orientation' => Pdf::ORIENT_PORTRAIT,
            'destination' => Pdf::DEST_BROWSER,
            'content' => $this->renderPartial('_protocol_new4', [
                'model' => $model
            ]),
            'methods' => [
                'SetTitle' => $model->title ?? 'Document',
            ],
            'cssFile' => '@api/modules/tender/views/tender/pdf.css',
            'defaultFontSize' => '13pt',
            'defaultFont' => 'Trebuchet MS',
            'marginLeft' => 5,
            'marginTop' => 20,
        ]);
        $pdf->filename = 'lot-' . $model->lot . '-' . '.pdf';
        return $pdf->render();
    }

    public function actionGetProtocolNotRealized($id)
    {

        $model = TenderResource::find()
            ->where(['>=', 'state', TenderEnum::STATE_MADE_PROTOCOL])
            ->andWhere(['id' => $id])
            ->one();

        if (!$model) throw new NotFoundHttpException("Protokol shakllantirilmagan.");

        $pdf = new Pdf([
            'mode' => Pdf::MODE_UTF8,
            'format' => Pdf::FORMAT_A4,
            'orientation' => Pdf::ORIENT_PORTRAIT,
            'destination' => Pdf::DEST_BROWSER,
            'content' => $this->renderPartial('_protocol', [
                'model' => $model
            ]),
            'cssFile' => '@api/modules/tender/views/tender/pdf.css',
        ]);

        $pdf->filename = 'lot-' . $model->lot . '-' . '.pdf';
        return $pdf->render();
    }

    public function actionGetDiscussionProtocol($id)
    {

        $model = TenderResource::find()
            ->where(['>=', 'state', TenderEnum::STATE_MADE_DISCUSSION_PROTOCOL])
            ->andWhere(['id' => $id])
            ->one();
        if (!$model) throw new NotFoundHttpException("Muxokama protokoli shakllantirilmagan.");
        $pdf = new Pdf([
            'mode' => Pdf::MODE_UTF8,
            'format' => Pdf::FORMAT_A4,
            'orientation' => Pdf::ORIENT_PORTRAIT,
            'destination' => Pdf::DEST_BROWSER,
            'content' => $this->renderPartial('_protocol_discussion', [
                'model' => $model
            ])
        ]);
        $pdf->filename = 'lot-' . $model->id . '-' . '.pdf';
        return $pdf->render();
    }

    public function actionView($id)
    {
        return $this->sendModel($this->findOne($id));
    }


    public function actionUpdate($id)
    {
        return $this->sendResponse(
            new TenderUpdateForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    /**
     * E'lon qilingan tenderni tahrirlash, kelishish, so'rovlar orqali
    */
    public function actionUpdateBySecretary($id)
    {
        return $this->sendResponse(
            new TenderUpdateBySecretaryForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }

//    public function actionReLoad($id)
//    {
//        return $this->sendResponse(
//            new TenderReloadForm($this->findAnyOne($id)),
//            Yii::$app->request->bodyParams
//        );
//    }

    public function actionRelease($id)
    {
        return $this->sendResponse(
            new TenderReleaseForm($this->findAnyOne($id, TenderEnum::STATE_NOT_REALIZED)),
            Yii::$app->request->bodyParams
        );
    }

    public function actionDelete($id)
    {
        $body = Yii::$app->request->bodyParams;
        $decodedPks7 = $this->verifyPkcs7($body);
        return $this->sendResponsePost(
            new TenderDeleteForm(),
            $decodedPks7,
            $body['pkcs7'],
            PkcsENum::PKCS7_TYPE_TENDER_DELETE
        );
    }

    public function actionCommissionVotes()
    {
        return $this->sendResponse(
            new TenderCommissionVoteFilter(),
            Yii::$app->request->queryParams
        );
    }


    //elon qilish
    public function actionPublish()
    {
        $body = Yii::$app->request->bodyParams;
        $decodedPks7 = $this->verifyPkcs7($body);
        return $this->sendResponsePost(
            new TenderPublishForm(),
            $decodedPks7,
            $body['pkcs7'],
            PkcsEnum::PKCS7_TYPE_TENDER_PUBLISH
        );
    }


    private function findOne($id)
    {
        $model = TenderResource::find()->notDeletedAndFromCompany()->andWhere(['id' => $id])->one();

        if (!$model) throw new NotFoundHttpException("Tender is not found");

        return $model;
    }

    /**
     * @throws NotFoundHttpException
     */
    private function findAnyOne($id, $state = null)
    {
        $model = TenderResource::find()->where(['company_id' => Yii::$app->user->identity->company_id])->andWhere(['id' => $id]);

        if ($state != null && in_array($state, TenderEnum::STATE_LIST)) {
            $model->andWhere(['state' => $state]);
        }
        $model = $model->one();

        if (!$model) throw new NotFoundHttpException("Tender is not found");

        return $model;
    }

    /*komissiyani almashtirish*/
    public function actionUpdateTenderCommissionMember($id)
    {
        return $this->sendResponse(
            new TenderCommissionMemberUpdateForm($this->findAnyOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    /**
     * @throws NotFoundHttpException
     */
    public function actionSendContractToWinner()
    {
        // buyurtmachi imzo bilan tasdiqlash kk
        $body = Yii::$app->request->bodyParams;
        $decodedPks7 = $this->verifyPkcs7($body);

        return $this->sendResponsePost(
            new TenderSendContractForm(1),
            $decodedPks7,
            $body['pkcs7'],
            PkcsEnum::PKCS7_TYPE_SEND_CONTRACT_TO_WINNER
        );

    }

    /**
     * @throws NotFoundHttpException
     */
    public function actionSendContractToSecondWinner($id)
    {
        // buyurtmachi imzo bilan tasdiqlash kk
        $body = Yii::$app->request->bodyParams;
        $decodedPks7 = $this->verifyPkcs7($body);

        return $this->sendResponsePost(
            new TenderSendContractForm(2),
            $decodedPks7,
            $body['pkcs7'],
            PkcsEnum::PKCS7_TYPE_SEND_CONTRACT_TO_SECOND_WINNER
        );
    }

    public function actionAcceptContract()
    {
        $body = Yii::$app->request->bodyParams;
        $decodedPks7 = $this->verifyPkcs7($body);

        return $this->sendResponsePost(
            new TenderAcceptContractForm(),
            $decodedPks7,
            $body['pkcs7'],
            PkcsEnum::PKCS7_TYPE_TENDER_ACCEPT_CONTRACT
        );
    }

    public function actionReceiveProduct($id)
    {
        return $this->sendResponse(
            new TenderReceiveProductForm($this->findAnyOne($id, TenderEnum::STATE_ACCEPT_CONTRACT)),
            Yii::$app->request->bodyParams
        );
    }

    public function actionAcceptContractSecond()
    {
        $body = Yii::$app->request->bodyParams;
        $decodedPks7 = $this->verifyPkcs7($body);

        return $this->sendResponsePost(
            new TenderAcceptContractSecondForm(),
            $decodedPks7,
            $body['pkcs7'],
            PkcsEnum::PKCS7_TYPE_TENDER_ACCEPT_CONTRACT_SECOND
        );

    }


    /**
     * @throws NotFoundHttpException
     */
    private function findContract($id, $valid = false)
    {
        $model = ContractResource::find()->where(['id' => $id]);
        if ($valid) {
            $comId = Yii::$app->user->identity->company_id;
            $model->andWhere([
                'or',
                ['customer_id' => $comId],
                ['producer_id' => $comId]
            ]);
        }

        $model = $model->one();;

        if (!$model) throw new NotFoundHttpException("Contract is not found");

        return $model;
    }


    // komissiya uchun


    /**
     * Elon qilish uchun
     * */
    public function actionCommissionTenderAgreementLots()
    { //    const ROLE_COMMISSION = 'commission';
        //tender/commission-tender-agreement-lots
        return $this->sendResponse(
            new TenderForCommissionFilter([TenderEnum::STATE_ACCEPT_MODERATOR, TenderEnum::STATE_READY_FOR_CHAIRMAN, TenderEnum::STATE_READY_TO_PRESENT]),
            Yii::$app->request->queryParams
        );
    }

    /**
     * o'zi qatnashgan aktiv lotlar
     * */
    public function actionCommissionTenderActiveLots()
    {
        return $this->sendResponse(
            new TenderForCommissionFilter(TenderEnum::STATE_READY),
            Yii::$app->request->queryParams
        );
    }

    public function actionCommissionTenderStat($id)
    {
        return $this->sendResponse(
            new TenderCommissionStatFilter($this->findOneForCommission($id)),
            Yii::$app->request->queryParams
        );
    }

    public function actionCommissionTenderRateStat($id)
    {
        return $this->sendResponse(
            new TenderCommissionRateStatFilter($this->findOneForCommission($id)),
            Yii::$app->request->queryParams
        );
    }

    /**
     * @throws NotFoundHttpException
     */
    private function findOneForCommission($id)
    {
        $model = TenderResourceForCommission::find()->notDeletedAndFromCompany()->andWhere(['id' => $id])->one();

        if (!$model) throw new NotFoundHttpException("Tender is not found");

        return $model;
    }

    /*sekretar baholagan lotlar*/
    public function actionCommissionTenderRatedLots()
    { //    const ROLE_COMMISSION = 'commission';
        return $this->sendResponse(
            new TenderForCommissionFilter([TenderEnum::STATE_READY_TO_RATING, TenderEnum::STATE_READY_TO_VOTE, TenderEnum::STATE_READY_TO_MAKE_PROTOCOL]),
            Yii::$app->request->queryParams
        );
    }

    public function actionCommissionTenderRatedView($id)
    {
        $model = TenderResourceForSecretary::find()
            ->notDeletedAndFromCompany()
            ->andWhere(['id' => $id])->andWhere(['state' => [TenderEnum::STATE_READY_TO_VOTE, TenderEnum::STATE_READY_TO_MAKE_PROTOCOL]])->one();
        if (!$model) throw new NotFoundHttpException("Tender is not found");
        return $this->sendModel($model);
    }

    public function actionCommissionTenderVote($id)
    {
        ///tender/commission-tender-vote
        return $this->sendResponse(
            new TenderCommissionVoteForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    //sekretar baholariga munosabat
    public function actionCommissionTenderRatingVote($id)
    {
        return $this->sendResponse(
            new TenderCommissionTenderRatingVoteForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    // rais

    /**
     * Elon qilish uchun
     * */
    public function actionChairmanTenderAgreementLots()
    {
        // TODO rais imzo bilan tasdiqlash kk
        return $this->sendResponse(
            new TenderForChairmanToPublishFilter([TenderEnum::STATE_ACCEPT_MODERATOR, TenderEnum::STATE_READY_FOR_CHAIRMAN, TenderEnum::STATE_READY_TO_PRESENT]),
            Yii::$app->request->queryParams
        );
    }

    public function actionTenderFinishedLots()
    {
        // TODO rais imzo bilan tasdiqlash kk
        return $this->sendResponse(
            new TenderForChairmanToPublishFilter([TenderEnum::STATE_MADE_PROTOCOL]),
            Yii::$app->request->queryParams
        );
    }


    //Elon qilish uchun Rais uchun tasdiqlashi
    public function actionChairmanTenderUpdate($id): array
    {
        // TODO rais imzo bilan tasdiqlash kk
        return $this->sendResponse(
            new TenderUpdateChairmanForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    /**
     * Tender g'olib aniqlash protses shu ichida
    */
    public function actionChairmanTenderUpdateWithProtocol($id): array
    {
        // TODO rais imzo bilan tasdiqlash kk
        return $this->sendResponse(
            new TenderUpdateChairmanWithProtocolForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    public function actionChairmanMadeDiscussionProtocol($id): array
    {
        // TODO rais imzo bilan tasdiqlash kk
        return $this->sendResponse(
            new TenderUpdateChairmanMakeDiscussionProtocolForm($this->findAnyOne($id, TenderEnum::STATE_DISCUSSION_END_MADE_PROTOCOL)),
            Yii::$app->request->bodyParams
        );
    }

    public function actionChairmanTenderProtest($id): array
    {
        // TODO rais imzo bilan tasdiqlash kk
        return $this->sendResponse(
            new TenderProtestFilter($this->findAnyOne($id)),
            \Yii::$app->request->queryParams
        );
    }


    //secretar

    public function actionSecretaryTenderToRateIndex(): array
    {
        return $this->sendResponse(
            new TenderForSecretaryFilter(),
            Yii::$app->request->queryParams
        );
    }

    //sekretar, malaka tanlovini baholash
    public function actionSecretaryTenderQualificationRate($id): array
    {
        return $this->sendResponse(
            new TenderForSecretaryQualificationRateForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    /**
     * @throws Exception
     */
    public function actionSecretaryTenderLocalProducerRate($id): array
    {
        return $this->sendResponse(
            new TenderForSecretaryRateLocalProducerForm($id),
            Yii::$app->request->bodyParams
        );
    }

    public function actionSecretaryTenderRate($id): array
    {
        return $this->sendResponse(
            new TenderForSecretaryRateForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    public function actionSecretaryConflictInterest($id): array
    {
        return $this->sendResponse(
            new TenderRequestConflictInterestForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    public function actionSecretaryDisclassification($id)
    {
        return $this->sendResponse(
            new TenderRequestDisclassificationForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    public function actionSecretaryRatingEnd($id)
    {
        //tender/secretary-rating-end
        return $this->sendResponse(
            new TenderRequestCheckRatingEndForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    private function findOneSecretary($id)
    {
        $model = TenderResourceForSecretary::find()->notDeletedAndFromCompany()->andWhere($id)->one();
        if (!$model) throw new NotFoundHttpException("Tender is not found");
        return $model;
    }

    public function actionSecretaryTenderToRateView($id)
    {
        //tender/secretary-tender-to-rate-view
        $tender = $this->findOneSecretary(['id' => $id, 'state' => TenderEnum::STATE_READY_TO_RATING]);
        $tender->systemRateAnswers();
        return $this->sendModel($tender);
    }


    /**
     * Klent kabineti uchun
     * */

    public function actionListOffer()
    {
        return $this->sendResponse(
            new TenderWithOfferFilter(),
            \Yii::$app->request->queryParams
        );
    }
}