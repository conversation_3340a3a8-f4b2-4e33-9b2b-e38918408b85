<?php


namespace api\modules\tender\filters;


use api\components\BaseRequest;
use api\modules\tender\resources\ContractListResource;
use api\modules\tender\resources\TenderResource;

class TenderContractListFilter extends BaseRequest
{

    public $lot;

    public function rules()
    {
        return [
            [['lot'], 'safe'],
        ];
    }

    public function getResult()
    {

        $models = ContractListResource::find()
            ->where(['producer_id' => \Yii::$app->user->identity->company_id])
            ->andWhere('tender_id is not null');
        if ($this->lot) {
            $tender = TenderResource::findOne(['lot' => $this->lot]);
            // tender topilmasa hechnima qaytmasligi kk
            if ($tender) {
                $models->andWhere(['tender_id' => $tender->id]);
            } else {
                $models->andWhere(['tender_id' => -1]);
            }
        }
        $models->orderBy('created_at DESC');
        return paginate($models);
    }
}