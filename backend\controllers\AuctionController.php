<?php

namespace backend\controllers;

use common\enums\AuctionEnum;
use common\enums\CompanyTransactionEnum;
use common\enums\OperationTypeEnum;
use common\models\auction\AuctionHistory;
use common\models\Classifier;
use common\models\Company;
use common\models\CompanyTransaction;
use common\models\Contract;
use common\models\TenderModeratorLog;
use common\models\VirtualTransaction;
use Yii;
use common\models\auction\Auction;
use common\models\auction\AuctionSearch;
use yii\helpers\ArrayHelper;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\web\Response;

/**
 * AuctionController implements the CRUD actions for Auction model.
 */
class AuctionController extends BackendController
{

    public function actionChart()
    {
        $year = Yii::$app->request->get('year') ? Yii::$app->request->get('year') : date("Y");
        $month = Yii::$app->request->get('month') ? Yii::$app->request->get('month') : date("m");

        $contract = Contract::find()->select([
            'DATE(created_at) as created_at',
            'SUM(price) as price'
        ])->andWhere(['is not', 'auction_id', null]);
        if ($year) {
            $contract->andWhere(['extract(year from created_at)' => $year]);
        }
        if ($month) {
            $contract->andWhere(['extract(month from created_at)' => $month]);
        }
        $data = $contract->groupBy(['DATE(created_at)'])->all();

        $daysInMonth = cal_days_in_month(CAL_GREGORIAN, $month, $year);

        $startDate = $year . '-' . $month . '-' . '01';
        $endDate = $year . '-' . $month . '-' . $daysInMonth;
        $allDates = [];
        $currentDate = strtotime($startDate);
        $endDateTimestamp = strtotime($endDate);

        while ($currentDate <= $endDateTimestamp) {
            $allDates[date('Y-m-d', $currentDate)] = 0; // 0 qiymat bilan boshlaymiz
            $currentDate = strtotime('+1 day', $currentDate);
        }

        foreach ($data as $d) {
            $allDates[$d->created_at] = $d->price;
        }

        return $this->render('chart', [
            'year' => $year,
            'month' => $month,
            'data' => $allDates,
        ]);
    }

    /**
     * Lists all Auction models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new AuctionSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Lists all Product models.
     * @return mixed
     */
    public function actionModerator($status)
    {
        $searchModel = new AuctionSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams, $status);
        $classifier = ArrayHelper::map(Classifier::find()->all(), 'id', 'title_uz');
        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'classifier' => $classifier,
        ]);
    }

    /**
     * Lists all Product models.
     * @return mixed
     */
    public function actionModeratorRejected($status)
    {
        $searchModel = new AuctionSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams, $status);
        $classifier = ArrayHelper::map(Classifier::find()->all(), 'id', 'title_uz');
        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'classifier' => $classifier,
        ]);
    }

    /**
     * Lists all Product models.
     * @return mixed
     */
    public function actionModeratorAccept()
    {

        $model = $this->findModel(Yii::$app->request->post()['id']);

        if ($model->status != AuctionEnum::STATUS_MODERATING) {
            \Yii::$app->session->setFlash("danger", "Auksion moderatsiya xolatida emas");
            return $this->render('view', [
                'model' => $model,
            ]);
        }
        $company = $model->company;
        $isBudget = $company->organization_type == Company::BUDJET;

        $transaction = \Yii::$app->db->beginTransaction();

        $zalog_sum = 0;
        if (!$isBudget) {
            $zalog_sum = $model->total_sum * env('ZALOG_PERCENT', 0.03);
        }
        $commission_sum = $model->total_sum * env('COMMISSION_PERCENT', 0.0015);
        $commission_sum = $commission_sum > 1000000 ? 1000000 : $commission_sum;
        $total_block_sum = $zalog_sum + $commission_sum;
        /**
         * @var $company Company
         */

        if (!hasMoney($company , $total_block_sum)) {
            \Yii::$app->session->setFlash("danger", "Недостаточно средств на балансе покупателя.");
            return $this->render('view', [
                'model' => $model,
            ]);
        }

        if ($zalog_sum > 0) {
            $virtual_transaction_zalog_id = VirtualTransaction::saveTransaction(
                $company,
                $company,
                OperationTypeEnum::P_K_30101,
                OperationTypeEnum::P_K_30201,
                $zalog_sum,
                Yii::t("main", "Auksion uchun zalog"),
                OperationTypeEnum::PRODUCT_NAME_AUCTION,
                $model->id,
                null,
                OperationTypeEnum::BLOCK_SALE_DEPOSIT,

            );

            if (!$virtual_transaction_zalog_id) {
                $transaction->rollBack();
                \Yii::$app->session->setFlash("danger", "Не удалось сохранить транзакцию для залога: " . json_encode($virtual_transaction_zalog_id->errors));
                return $this->render('view', [
                    'model' => $model,
                ]);
            }

//            $company_transaction_zalog = new CompanyTransaction([
//                'company_id' => $company->id,
//                'auction_id' => $model->id,
//                'amount' => $zalog_sum,
//                'type' => CompanyTransactionEnum::TYPE_ZALOG,
//                'description' => Yii::t("main", "Auksion uchun zalog"),
//                'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                'transaction_date' => date("Y-m-d H:i:s"),
//            ]);

//            if (!$company_transaction_zalog->save()) {
//                $transaction->rollBack();
//                \Yii::$app->session->setFlash("danger", "Не удалось сохранить транзакцию для залога: " . json_encode($company_transaction_zalog->errors));
//                return $this->render('view', [
//                    'model' => $model,
//                ]);
//            }

        }

        $virtual_transaction_comission_id = VirtualTransaction::saveTransaction(
            $company,
            $company,
            OperationTypeEnum::P_K_30101,
            OperationTypeEnum::P_K_30202,
            $commission_sum,
            Yii::t("main", "Auksion uchun kommissiya"),
            OperationTypeEnum::PRODUCT_NAME_AUCTION,
            $model->id,
            null,
            OperationTypeEnum::BLOCK_SALE_COMMISSION,

        );

        if (!$virtual_transaction_comission_id) {
            $transaction->rollBack();
            \Yii::$app->session->setFlash("danger", "Не удалось сохранить транзакцию для комиссии: " . json_encode($virtual_transaction_comission_id->errors));
            return $this->render('view', [
                'model' => $model,
            ]);
        }

//        $company_transaction_commission = new CompanyTransaction([
//            'company_id' => $company->id,
//            'auction_id' => $model->id,
//            'amount' => $commission_sum,
//            'type' => CompanyTransactionEnum::TYPE_BLOCK_COMMISION,
//            'description' => Yii::t("main", "Auksion uchun kommissiya"),
//            'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//            'transaction_date' => date("Y-m-d H:i:s"),
//        ]);

//        if (!$company_transaction_commission->save()) {
//            $transaction->rollBack();
//            \Yii::$app->session->setFlash("danger", "Не удалось сохранить транзакцию для комиссии: " . json_encode($company_transaction_commission->errors));
//            return $this->render('view', [
//                'model' => $model,
//            ]);
//        }

        if ($isBudget) {
            $model->status = AuctionEnum::STATUS_DMBAT;//todo
        } else {
            $model->status = AuctionEnum::STATUS_ACTIVE;
            $model->begin_date = date("Y-m-d H:i:s");
//            $model->age = $model->age + 1;
            $model->auction_end = date("Y-m-d H:i:s", strtotime("+15 minutes"));
        }


        if ($model->save()) {
            $moderatorLog = new TenderModeratorLog();
            $moderatorLog->auction_id = $model->id;
            $moderatorLog->description = "Moderator tasdiqladi. DMBAT ga yuborildi";
            $moderatorLog->moderator_pinfl = Yii::$app->user->identity->username;

            if (!($moderatorLog->validate() && $moderatorLog->save())) {
                $transaction->rollBack();
                \Yii::$app->session->setFlash("danger", "Не удалось сохранить лог : " . json_encode($moderatorLog->errors));
                return $this->render('view', [
                    'model' => $model,
                ]);
            }

            $history = new AuctionHistory();
            $history->auction_id = $model->id;
            $history->user_id = Yii::$app->user->id;
            $history->status = $model->status;
            $history->comment = "Moderatsiyadan muvaffaqiyatli o'tdi";
            $history->created_at = date("Y-m-d H:i:s");
            if (!$history->save()) {
                $transaction->rollBack();
                \Yii::$app->session->setFlash("danger", "Не удалось сохранить история : " . json_encode($history->errors));
                return $this->render('view', [
                    'model' => $model,
                ]);
            }

            $transaction->commit();
            \Yii::$app->session->setFlash("success", "Успешно сохранить");
            return $this->redirect(['index']);
        }

        \Yii::$app->session->setFlash("danger", "Не удалось сохранить" . json_encode($model->errors));

        $transaction->rollBack();
        return $this->render('view', [
            'model' => $model,
        ]);

    }

    public function actionModeratorReject()
    {

        $model = $this->findModel(Yii::$app->request->post()['id']);

        if ($model->status != AuctionEnum::STATUS_MODERATING) {
            \Yii::$app->session->setFlash("success", "Moderatsiya holatida emas");
            return $this->render('view', [
                'model' => $model,
            ]);
        }

        $transaction = \Yii::$app->db->beginTransaction();

        $model->status = AuctionEnum::STATUS_REJECTED;
        $model->cancel_date = date("Y-m-d H:i:s");
        $model->cancel_reason = Yii::$app->request->post()['select'] . "<br>" . Yii::$app->request->post()['description'];

        if ($model->save()) {

            $history = new AuctionHistory();
            $history->auction_id = $model->id;
            $history->user_id = Yii::$app->user->id;
            $history->status = $model->status;
            $history->comment = Yii::$app->request->post()['description'];
            $history->created_at = date("Y-m-d H:i:s");
            if (!$history->save()) {
                \Yii::$app->session->setFlash("danger", "Не удалось сохранить история : " . json_encode($history->errors));
                $transaction->rollBack();
                return $this->render('view', [
                    'model' => $model,
                ]);
            }

            $moderatorLog = new TenderModeratorLog();
            $moderatorLog->auction_id = $model->id;
            $moderatorLog->description = Yii::$app->request->post()['select'] . "<br>" . Yii::$app->request->post()['description'];
            $moderatorLog->moderator_pinfl = Yii::$app->user->identity->username;

            if (!($moderatorLog->validate() && $moderatorLog->save())) {
                $transaction->rollBack();
                \Yii::$app->session->setFlash("danger", "Не удалось сохранить лог : " . json_encode($moderatorLog->errors));
                return $this->render('view', [
                    'model' => $model,
                ]);
            }

            $classifiers = $model->auctionClassifiers;
            foreach ($classifiers as $classifier) {
                $planScheduleClassifier = $classifier->planScheduleClassifier;
                $planScheduleClassifier->count_used = $planScheduleClassifier->count_used - $classifier->quantity;
                $planScheduleClassifier->count_live = $planScheduleClassifier->count - $planScheduleClassifier->count_used;
                if (!$planScheduleClassifier->save()) {
                    $this->addErrors($planScheduleClassifier->errors);
                    $transaction->rollBack();
                    return false;
                }
            }


            $transaction->commit();
            \Yii::$app->session->setFlash("success", "Успешно no moderating");
            return $this->redirect(['index']);
        }

        \Yii::$app->session->setFlash("danger", "Не удалось сохранить" . json_encode($model->errors));

        $transaction->rollBack();
        return $this->render('view', [
            'model' => $model,
        ]);


    }

    public function actionDmbatReject()
    {

        $model = $this->findModel(Yii::$app->request->post()['id']);

        if ($model->status != AuctionEnum::STATUS_DMBAT) {
            \Yii::$app->session->setFlash("success", "DMBAT holatida emas");
            return $this->render('view', [
                'model' => $model,
            ]);
        }

        $transaction = \Yii::$app->db->beginTransaction();

        $model->status = AuctionEnum::STATUS_DMBAT_REJECT;
        $model->cancel_date = date("Y-m-d H:i:s");
        $model->cancel_reason = Yii::$app->request->post()['description'];

        if ($model->save()) {

            $history = new AuctionHistory();
            $history->auction_id = $model->id;
            $history->user_id = Yii::$app->user->id;
            $history->status = $model->status;
            $history->comment = Yii::$app->request->post()['description'];
            $history->created_at = date("Y-m-d H:i:s");
            if (!$history->save()) {
                \Yii::$app->session->setFlash("danger", "Не удалось сохранить история : " . json_encode($history->errors));
                $transaction->rollBack();
                return $this->render('view', [
                    'model' => $model,
                ]);
            }

            $moderatorLog = new  TenderModeratorLog();
            $moderatorLog->auction_id = $model->id;
            $moderatorLog->description = Yii::$app->request->post()['description'];
            $moderatorLog->moderator_pinfl = Yii::$app->user->identity->username;

            if (!($moderatorLog->validate() && $moderatorLog->save())) {
                $transaction->rollBack();
                \Yii::$app->session->setFlash("danger", "Не удалось сохранить лог : " . json_encode($moderatorLog->errors));
                return $this->render('view', [
                    'model' => $model,
                ]);
            }

            $classifiers = $model->auctionClassifiers;
            foreach ($classifiers as $classifier) {
                $planScheduleClassifier = $classifier->planScheduleClassifier;
                $planScheduleClassifier->count_used = $planScheduleClassifier->count_used - $classifier->quantity;
                $planScheduleClassifier->count_live = $planScheduleClassifier->count - $planScheduleClassifier->count_used;
                if (!$planScheduleClassifier->save()) {
                    $this->addErrors($planScheduleClassifier->errors);
                    $transaction->rollBack();
                    return false;
                }
            }


            $transaction->commit();
            \Yii::$app->session->setFlash("success", "Успешно no moderating");
            return $this->redirect(['index']);
        }

        \Yii::$app->session->setFlash("danger", "Не удалось сохранить" . json_encode($model->errors));

        $transaction->rollBack();
        return $this->render('view', [
            'model' => $model,
        ]);


    }

    public function actionDmbat()
    {

        $model = $this->findModel(Yii::$app->request->get()['id']);

        if ($model->status != AuctionEnum::STATUS_DMBAT) {
            \Yii::$app->session->setFlash("success", "DMBAT holatida emas");
            return $this->render('view', [
                'model' => $model,
            ]);
        }

        $transaction = \Yii::$app->db->beginTransaction();

        $model->status = AuctionEnum::STATUS_ACTIVE;
        $model->age = $model->age + 1;
        $model->auction_end = date("Y-m-d H:i:s", strtotime("+15 minutes"));

        if ($model->save()) {

            $history = new AuctionHistory();
            $history->auction_id = $model->id;
            $history->user_id = Yii::$app->user->id;
            $history->status = $model->status;
            $history->comment = 'DMBAT tasdiqladi';
            $history->created_at = date("Y-m-d H:i:s");
            if (!$history->save()) {
                \Yii::$app->session->setFlash("danger", "Не удалось сохранить история : " . json_encode($history->errors));
                $transaction->rollBack();
                return $this->render('view', [
                    'model' => $model,
                ]);
            }

            $moderatorLog = new TenderModeratorLog();
            $moderatorLog->auction_id = $model->id;
            $moderatorLog->description = 'DMBAT tasdiqladi';
            $moderatorLog->moderator_pinfl = Yii::$app->user->identity->username;

            if (!($moderatorLog->validate() && $moderatorLog->save())) {
                $transaction->rollBack();
                \Yii::$app->session->setFlash("danger", "Не удалось сохранить лог : " . json_encode($moderatorLog->errors));
                return $this->render('view', [
                    'model' => $model,
                ]);
            }

            $transaction->commit();
            \Yii::$app->session->setFlash("success", "Успешно no moderating");
            return $this->redirect(['index']);
        }

        \Yii::$app->session->setFlash("danger", "Не удалось сохранить" . json_encode($model->errors));

        $transaction->rollBack();
        return $this->render('view', [
            'model' => $model,
        ]);


    }

    /**
     * Displays a single Auction model.
     * @param int $id ID
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Finds the Auction model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param int $id ID
     * @return Auction the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Auction::findOne($id)) !== null) {
            return $model;
        }
        throw new NotFoundHttpException('The requested page does not exist.');
    }

    public function actionRender($id)
    {
        \Yii::$app->response->format = Response::FORMAT_JSON;

        return [
            'render' => $this->renderAjax('render', [
                'model' => $this->findModel($id),
            ])
        ];
    }
}
