<?php


namespace backend\modules\admin\forms;


use api\components\BaseModel;
use common\enums\OperationTypeEnum;
use common\models\Company;
use common\models\VirtualTransaction;
use yii\base\Exception;

class SetBalanceForm extends BaseModel
{
    public $company_tin;
    public $transaction_id;

    public function rules()
    {
        return [
//            [['companyTin', 'transaction_id'], 'required'],
            [['transaction_id'], 'integer'],
            [['company_tin'], 'string', 'max' => 14],
        ];
    }

    public function getResult()
    {
        $transaction = VirtualTransaction::findOne($this->transaction_id);
        if (!$transaction) {
            $this->addError('transaction_id', 'Tranzaksiya topilmadi.');
            return false;
        }

        if ($transaction->prefix_account !== (string)OperationTypeEnum::P_K_30901) {
            $this->addError('transaction_id', 'Bu tranzaksiya aniqlanmagan tranzaksiya emas.');
            return false;
        }

        $existingTransaction = VirtualTransaction::find()
            ->where([
                'operation_type' => OperationTypeEnum::CLARIFY_TRANSACTION,
                'debit_id' => $transaction->id
            ])
            ->one();

        if ($existingTransaction) {
            $this->addError('transaction_id', 'Bu tranzaksiya allaqachon boshqa kompaniyaga o\'tkazilgan.');
            return false;
        }

        $company = Company::findOne(['tin' => $this->company_tin]);
        if (!$company) {
            $this->addError('company_tin', 'Kompaniya topilmadi.');
            return false;
        }

        try {
            $creditTransactionId = VirtualTransaction::credit30101(
                $company,
                OperationTypeEnum::P_K_30101,
                $transaction->credit,
                "Aniqlanmagan tranzaksiya kompaniyaga bog'landi",
                $transaction,
                OperationTypeEnum::CLARIFY_TRANSACTION
            );

            $transaction->parent_id = $creditTransactionId;
            if (!$transaction->save()) {
                throw new Exception('Tranzaksiyani yangilashda xatolik: ' . implode(', ', $transaction->getFirstErrors()));
            }
            return [
                'success' => true,
                'message' => 'Tranzaksiya muvaffaqiyatli kompaniyaga o\'tkazildi.',
                'data' => [
                    'original_transaction_id' => $transaction->id,
                    'new_transaction_id' => $creditTransactionId,
                    'company_tin' => $company->tin,
                    'company_title' => $company->title,
                    'amount' => $transaction->credit
                ]
            ];

        } catch (Exception $e) {
            $this->addError('general', 'Tranzaksiyani qayta ishlashda xatolik: ' . $e->getMessage());
            return false;
        }
    }
}