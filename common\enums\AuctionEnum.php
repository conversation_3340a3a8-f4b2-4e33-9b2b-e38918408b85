<?php


namespace common\enums;


interface AuctionEnum
{
  const int STATUS_MODERATING = 1; // moderatorga yuborildi
  const int STATUS_ACTIVE = 2; // moderator tasdiqladi
  const int STATUS_FINISHED = 3; // success tugatildi, shartnoma tuzildi
  const int STATUS_REJECTED = 4; // moderator tomonidan bekor qilindi
  const int STATUS_NOT_HELD = 5; // shartnoma tuzish uchun ovoz yetarli emas,
  const int STATUS_DELETED = 6; // o'chirilgan
  const int STATUS_DMBAT = 7; // budget tashkilot bo'lgani uchun DMBATga yuborildi
  const int STATUS_DMBAT_REJECT = 8; // DMBAT tomonidan reject oldi

  public const array LIST = [
    self::STATUS_MODERATING,
    self::STATUS_ACTIVE,
    self::STATUS_FINISHED,
    self::STATUS_REJECTED,
    self::STATUS_NOT_HELD,
    self::STATUS_DMBAT,
    self::STATUS_DMBAT_REJECT,
  ];
}
