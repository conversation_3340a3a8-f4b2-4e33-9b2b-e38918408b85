<?php


namespace common\enums;


interface ContractEnum
{

    /* tender uchun  */
    const STATUS_NEW = 100;//yangi
    const STATUS_ACCEPT = 200;//qabul qildi
    const STATUS_RECEIVED_PRODUCT = 300; // product qabul qilindi

    const STATUS_PROCESSING = 400; // ishlatilmadi tenderda
    const STATUS_DELIVERED = 500; // ishlatilmadi tenderda
    const STATUS_CANCELLED = 800; // bekor qilindi

    /* tender uchun  */


    const STATUS_SIGNED = 1; // Imzolangan tolov qilishi kerak
    const STATUS_PAYMENT_END = 2;  // <PERSON><PERSON> qilingan
    const STATUS_DONE = 3; // yetkazib berilgan
    const STATUS_CANCEL_PROCESS = 4; //bir tomon qaytargan
    const STATUS_CANCEL = 5;  // ikkala tomon ham qaytargan
    const STATUS_DISCARD = 6;  // yetkazib beruvchi rad qilganda
    const STATUS_RETURN_SIGNED = 7;  // qaytarilib kein imzoga tuwgan
    const STATUS_WAITING_RESERVE = 8;  // auksion, g'olib bosh tortgan, zaxira qabul qilishini kutilyapti
    const STATUS_CANCEL_BY_SYSTEM = 9;  // Tizim tomonidan bekor qilindi


    const STATUS_REQUEST_NEW = 100;  // yangi so'rov yuborgan xolat
    const STATUS_REQUEST_DONE = 200;  // qabul qilgan
    const STATUS_REQUEST_CANCELED = 300;  // so'rovni rad qilgan


    const TYPE_USER_PRODUCER = 1; //producer
    const TYPE_USER_CUSTOMER = 2; //customer


    const string PROCEDURE_TYPE_E_SHOP = 'e_shop';
    const string PROCEDURE_TYPE_N_SHOP = 'n_shop';
    const string PROCEDURE_TYPE_AUCTION = 'auction';
    const string PROCEDURE_TYPE_TENDER = 'tender';
    const string PROCEDURE_TYPE_SELECTION = 'selection'; // отбор
    const array PROCEDURE_TYPE_LIST = [
        self::PROCEDURE_TYPE_E_SHOP,
        self::PROCEDURE_TYPE_N_SHOP,
        self::PROCEDURE_TYPE_AUCTION,
        self::PROCEDURE_TYPE_TENDER,
        self::PROCEDURE_TYPE_SELECTION,
    ];

}