<?php

namespace common\enums;

interface PkcsEnum
{
    const PKCS7_TYPE_AUCTION_CREATE = 'auction_create';
    const PKCS7_TYPE_AUCTION_DRAFT_SEND_MODERATOR = 'auction_draft_send_moderator';
    const PKCS7_TYPE_TENDER_CREATE = 'tender_create';
    const PKCS7_TYPE_TENDER_DELETE = 'tender_delete';
    const PKCS7_TYPE_TENDER_ACCEPT_CONTRACT = 'tender_accept_contract';
    const PKCS7_TYPE_TENDER_ACCEPT_CONTRACT_SECOND = 'tender_accept_contract_second';
    const PKCS7_TYPE_TENDER_PUBLISH = 'tender_publish';
    const PKCS7_TYPE_SEND_CONTRACT_TO_WINNER = 'send_contract_to_winner';
    const PKCS7_TYPE_SEND_CONTRACT_TO_SECOND_WINNER = 'send_contract_to_second_winner';
    const PKCS7_TYPE_SHOP_PRODUCT_CREATE = 'shop_product_create';
    const PKCS7_TYPE_SHOP_PRODUCT_DRAFT_SEND_MODERATOR = 'shop_product_draft_send_moderator';
    const PKCS7_TYPE_SHOP_ORDER_CREATE = 'shop_order_create';
    const PKCS7_TYPE_SHOP_ONE_SIDED_ACCEPT = 'shop_one_sided_accept';
    const PKCS7_TYPE_SHOP_ONE_SIDED_REJECT = 'shop_one_sided_reject';
    const PKCS7_TYPE_SHOP_NO_OFFER_REQUEST = 'shop_no_offer_request';
    const PKCS7_TYPE_SHOP_NO_OFFER_ACCEPT = 'shop_no_offer_accept';
    const PKCS7_TYPE_SHOP_NO_OFFER_REJECT = 'shop_no_offer_reject';
    const PKCS7_TYPE_SHOP_CUSTOMER_ACCEPT = 'shop_customer_accept';
    const PKCS7_TYPE_SHOP_CUSTOMER_REJECT = 'shop_customer_reject';
    const PKCS7_TYPE_SHOP_CONTRACT_SET_PAYMENT = 'shop_contract_set_payment';
    const PKCS7_TYPE_SHOP_CONTRACT_SET_PAYMENT_AUCTION = 'shop_contract_set_payment_auction';
    const PKCS7_TYPE_SHOP_CANCEL_REQUEST = 'shop_cancel_request';
    const PKCS7_TYPE_SHOP_CANCEL_CONTRACT = 'shop_cancel_contract';
    const PKCS7_TYPE_SHOP_CANCEL_PROCESS_CONTRACT = 'shop_cancel_process_contract';
    const PKCS7_TYPE_SHOP_DELIVERED = 'shop_delivered';
    const PKCS7_TYPE_SHOP_DELIVERED_AUCTION = 'shop_delivered_auction';
    const PKCS7_TYPE_SHOP_CANCEL_CONTRACT_AUCTION = 'shop_cancel_contract_auction';
    const PKCS7_TYPE_SHOP_ACCEPT_CONTRACT_RESERVE = 'shop_accept_contract_reserve';
    const PKCS7_TYPE_SHOP_CANCEL_CONTRACT_AUCTION_PRODUCER = 'shop_cancel_contract_auction_producer';
    const PKCS7_TYPE_SHOP_CANCEL_CONTRACT_PRODUCER = 'shop_cancel_contract_producer';
    const PKCS7_TYPE_COMPANY_ADD_BANK_ACCOUNT = 'company_add_bank_account';
}