<?php

namespace common\enums;

interface ProductEnum
{

    public const SHOP_STATE_NEW = 100; // yangi moderatorga ketadi
    public const SHOP_STATE_RETURN_MODERATOR = 110; // moderator qaytarib yuborgan status
    public const SHOP_STATE_NO_MONEY = 200; // moderat<PERSON><PERSON><PERSON> utgan, puli yetmaganlari
    public const SHOP_STATE_ACTIVE = 300; // savdodagi
    public const SHOP_STATE_IN_ACTIVE = 400; // 15 ish kunidan kn o'tib qoladigan status
    public const SHOP_STATE_DELETED = 500; // o'chirilgan
    public const SHOP_STATE_CANCEL = 600; // rad qilingan

    const PERIOD_TYPE_DAY = 1;
    const PERIOD_TYPE_MONTH = 2;
    const PERIOD_TYPE_YEAR = 3;

    const PRODUCT_TYPE_PRODUCT = 1;
    const PRODUCT_TYPE_WORK = 2;
    const PRODUCT_TYPE_SERVICE = 3;



    const STATUS_NEW = 100;
    const STATUS_VIEWED = 200;
    const STATUS_NOT_ACTIVE = 400;
    const STATUS_DELETED = 500;
    const STATUS_CHANGED = 510;
    const  PLATFORM_DISPLAY_E_SHOP = 'e-shop';
    const  PLATFORM_DISPLAY_NATIONAL = 'n-shop';
    public const LIST = [
        self::SHOP_STATE_NEW,
        self::SHOP_STATE_RETURN_MODERATOR,
        self::SHOP_STATE_NO_MONEY,
        self::SHOP_STATE_ACTIVE,
        self::SHOP_STATE_IN_ACTIVE,
        self::SHOP_STATE_DELETED,
    ];
}
