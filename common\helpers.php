<?php

/**
 * Yii2 Shortcuts
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 * -----
 * This file is just an example and a place where you can add your own shortcuts,
 * it doesn't pretend to be a full list of available possibilities
 * -----
 */

use common\enums\OperationTypeEnum;
use common\enums\SystemEnum;
use common\models\Company;
use common\models\CompanyVirtualAccount;
use common\services\RequestService;
use JetBrains\PhpStorm\NoReturn;
use yii\base\Exception;
use yii\data\DataProviderInterface;
use yii\data\Pagination;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\httpclient\Client;
use yii\web\Response;

/**
 * @return int|string
 */
function getMyId()
{
    return Yii::$app->user->getId();
}

/**
 * @param string $view
 * @param array $params
 * @return string
 */
function render($view, $params = [])
{
    return Yii::$app->controller->render($view, $params);
}

/**
 * @param $url
 * @param int $statusCode
 * @return Response
 */
function redirect($url, $statusCode = 302)
{
    return Yii::$app->controller->redirect($url, $statusCode);
}

/**
 * @param string $key
 * @param mixed $default
 * @return mixed
 */
function env($key, $default = null)
{
    // getenv is disabled when using createImmutable with Dotenv class
    if (isset($_ENV[$key])) {
        return $_ENV[$key];
    } elseif (isset($_SERVER[$key])) {
        return $_SERVER[$key];
    }

    return $default;
}

/**
 * Renders any data provider summary text.
 *
 * @param DataProviderInterface $dataProvider
 * @param array $options the HTML attributes for the container tag of the summary text
 * @return string the HTML summary text
 */
function getDataProviderSummary($dataProvider, $options = [])
{
    $count = $dataProvider->getCount();
    if ($count <= 0) {
        return '';
    }
    $tag = ArrayHelper::remove($options, 'tag', 'div');
    if (($pagination = $dataProvider->getPagination()) !== false) {
        $totalCount = $dataProvider->getTotalCount();
        $begin = $pagination->getPage() * $pagination->pageSize + 1;
        $end = $begin + $count - 1;
        if ($begin > $end) {
            $begin = $end;
        }
        $page = $pagination->getPage() + 1;
        $pageCount = $pagination->pageCount;
        return Html::tag($tag, Yii::t('main', 'Showing <b>{begin, number}-{end, number}</b> of <b>{totalCount, number}</b> {totalCount, plural, one{item} other{items}}.', [
            'begin' => $begin,
            'end' => $end,
            'count' => $count,
            'totalCount' => $totalCount,
            'page' => $page,
            'pageCount' => $pageCount,
        ]), $options);
    } else {
        $begin = $page = $pageCount = 1;
        $end = $totalCount = $count;
        return Html::tag($tag, Yii::t('main', 'Total <b>{count, number}</b> {count, plural, one{item} other{items}}.', [
            'begin' => $begin,
            'end' => $end,
            'count' => $count,
            'totalCount' => $totalCount,
            'page' => $page,
            'pageCount' => $pageCount,
        ]), $options);
    }
}

/**
 * @param $query
 * @param array $params
 * @return array
 */
function paginate($query)
{
    $pageSize = Yii::$app->request->get('perPage', 10);
    $currentPage = Yii::$app->request->get('currentPage', 0);
    $countQuery = clone $query;
    $pages = new Pagination(['totalCount' => $countQuery->count(), 'pageSize' => $pageSize, 'page' => $currentPage]);
    $models = $query->offset($pages->offset)
        ->limit($pages->limit)
        ->all();

    return [
        'data' => $models,
        'meta' => [
            'totalCount' => (int)$pages->totalCount,
            'pageCount' => $pages->getPageCount(),
            'currentPage' => $pages->getPage(),
            'perPage' => $pages->getPageSize(),
        ],
    ];
}

function showPrice($price)
{
    return number_format($price, 2, '.', ' ');
}

if (!function_exists('hasMoney'))
{

    /**
     * @throws Exception
     */
    function hasMoney(Company $company, $price): bool
    {
        $prefix = OperationTypeEnum::P_K_30101;
        if ($company->organization_type === Company::BUDJET)
        {
            $prefix = OperationTypeEnum::P_B_31101;
        }
        $account = CompanyVirtualAccount::findOneByPrefixAndCompany($prefix, $company->id);
        if (!$account)
            throw new Exception("Hisob raqam topilmadi.");
        return $account->price >= $price;
    }
}

if (!function_exists('_company'))
{
    /**
     * @throws Exception
     */
    function _company(): Company
    {
        $company = Company::findOne(['tin' => SystemEnum::STOCK_TIN]);
        if (!$company)
            throw new Exception("Birja kompaniya sifatida kiritilmagan.");
        return $company;
    }
}


function debuggerTelegram($debugTitle, $params, $errors): mixed
{
    $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS);
    $traceLog = [];
    foreach ($backtrace as $trace) {
        if (isset($trace['file'], $trace['line'], $trace['function'])) {
            $traceLog[] = "Called in file: {$trace['file']} on line {$trace['line']}";
        }
    }
    $tracer = @array_shift($traceLog);
    $text = $debugTitle."\n\n";
    $text .= "Params: \n";
    $text .= "<pre>". json_encode($params,JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "</pre>";
    $text .= "Response: \n";
    $text .= "<pre>" . json_encode($errors,JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "</pre>";
    $text .= "Tracer: \n";
    $text .= "<pre>" . $tracer . "</pre>";

    return sendTelegramData('sendMessage', [
        'chat_id' => RequestService::TELEGRAM_LOG_GROUP_ID,
        'text' => $text,
        'parse_mode' => 'HTML'
    ]);
}



function sendTelegramData($route = '', $params= [])
{
    $base_url = 'https://api.telegram.org/bot' . RequestService::TELEGRAM_LOG_BOT_TOKEN . '/' . $route;

    $client = new Client(['baseUrl' => $base_url]);

    try {
        $request = $client->createRequest()
            ->setFormat(Client::FORMAT_JSON)
            ->setMethod('POST')
            ->addHeaders([
                'Content-type' => 'application/json',
                'Accept' => 'application/json',
            ])
            ->setData($params);
        $response = $request->send();
        if ($response->isOk)
        {
            return $response->data;
        }
        return ['ok' => false, 'error' => $response->getContent()];
    } catch (\Throwable $ex) {
        return ['ok' => false, 'error' => $ex->getMessage(),];
    }
}

function clear_phone_full(string $phone): string|null
{
    $number = preg_replace('/\D/', '', $phone);
    if ($number && ctype_digit($number) && strlen($number) === 9) {
        $number = '998' . $number;
    }
    if (strlen($number) < 9)
        return null;
    return $number;
}

function _d($var,$caller=null): void
{
    if(!isset($caller)){
        $debug_backtrace = debug_backtrace(1);
        $caller = array_shift($debug_backtrace);
    }
    echo '<code>File: '.$caller['file'].' / Line: '.$caller['line'].'</code>';
    echo '<pre>';
    yii\helpers\VarDumper::dump($var, 10, true);
    echo '</pre>';
}

#[NoReturn] function _dd($var): void
{
    $debug_backtrace = debug_backtrace(1);
    $caller = array_shift($debug_backtrace);
    _d($var,$caller);
    die();
}

