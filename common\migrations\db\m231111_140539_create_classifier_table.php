<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%classifier}}`.
 */
class m231111_140539_create_classifier_table extends Migration
{
  /**
   * {@inheritdoc}
   */
  public function safeUp()
  {
    $this->createTable('{{%classifier}}', [
      'id' => $this->primaryKey(),
      'title_ru' => $this->string(500),
      'title_en' => $this->string(500),
      'title_uz' => $this->string(500),
      'title_uzk' => $this->string(500),
      'code' => $this->string(255),
      'classifier_category_id' => $this->integer(),
      'status' => $this->integer(), //1-aktiv, 99-delete
      'type' => $this->integer(), // ish xizmat tovar
      'saled' => $this->integer(),
      'created_at' => $this->dateTime(),
      'updated_at' => $this->dateTime(),
      'deleted_at' => $this->dateTime(),
      'created_by' => $this->integer(),
      'updated_by' => $this->integer(),
    ]);
    $this->createIndex(
        'idx-classifier-code',
        'classifier',
        'code'
    );
    $this->addForeignKey('FK_classifier_classifier_category',  'classifier', 'classifier_category_id', 'classifier_category', 'id', 'CASCADE', 'CASCADE');
  }

  /**
   * {@inheritdoc}
   */
  public function safeDown(): void
  {
    $this->dropTable('{{%classifier}}');
  }
}
