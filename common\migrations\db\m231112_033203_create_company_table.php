<?php

use common\enums\OperationTypeEnum;
use common\models\CompanyVirtualAccount;
use common\models\Company;
use yii\console\ExitCode;
use yii\db\Migration;

/**
 * Handles the creation of table `{{%company}}`.
 */
class m231112_033203_create_company_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%company}}', [
            'id' => $this->primaryKey(),
            'organization_type' => $this->integer(),
            'title' => $this->string(255),
            'tin' => $this->string(9),
            'pinfl' => $this->string(14),
            'address' => $this->string(300),
            'status' => $this->integer(),

            'resident' => $this->integer(),
            'country_id' => $this->integer(),
            'passport' => $this->string(150),
            'passport_valid_date' => $this->date(),
            'phone' => $this->string(),
            'is_phone_confirmed' => $this->boolean()->defaultValue(false),
            'director' => $this->string(),
            'organization_legal_form_id' => $this->integer(),
            'economic_activities_type_id' => $this->integer(),
            'region_id' => $this->integer(),
            'district_id' => $this->integer(),
            'created_at' => $this->dateTime(),
            'updated_at' => $this->dateTime(),
            'deleted_at' => $this->dateTime(),
            'created_by' => $this->integer(),
            'updated_by' => $this->integer(),
        ]);

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $company = new Company();
            $company->title = "“Toshkent tovar xomashyo birjasi” AJ";
            $company->tin = "*********";
            if (!$company->save())
            {
                echo json_encode($company->getErrors()) . PHP_EOL;
                return ExitCode::UNSPECIFIED_ERROR;
            }
            foreach (OperationTypeEnum::ACTIVE_ACCOUNTS as $account) {
                $isSaved = CompanyVirtualAccount::saveVirtualAccount($company, $account);
                if (!$isSaved)
                {
                    $transaction->rollBack();
                    echo "Virtual account save failed" . PHP_EOL;
                    return ExitCode::UNSPECIFIED_ERROR;
                }
            }
            $transaction->commit();
            return ExitCode::OK;
        } catch (Exception $ex) {
            $transaction->rollBack();
            echo $ex->getMessage() . PHP_EOL;
            return ExitCode::UNSPECIFIED_ERROR;
        }
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown(): void
    {
        $this->dropTable('{{%company}}');
    }
}
