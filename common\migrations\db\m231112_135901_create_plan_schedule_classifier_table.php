<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%plan_schedule_classifier}}`.
 */
class m231112_135901_create_plan_schedule_classifier_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp(): void
    {
        $this->createTable('{{%plan_schedule_classifier}}', [
            'id' => $this->primaryKey(),
            'plan_schedule_id' => $this->integer(),
            'classifier_id' => $this->integer(),
            'status' => $this->integer(),//common/enums/StatusEnum
            'enabled' => $this->integer(),
            'unit_id' => $this->integer(), //TOVAREDIZM
            'description' => $this->string(255),
            'year' => $this->integer(),
            'month' => $this->integer(),
            'kls' => $this->string(27),
            'company_bank_account_id' => $this->integer(),
            'tovar' => $this->string(18), //Код товара ЕНКТ
            'tovarname' => $this->string(80),
            'expense' => $this->string(8), //Статья расходов
            'count' => $this->integer(), //TOVARAMOUNT
            'tovarprice' => $this->bigInteger()->unsigned(),
            'summa' => $this->bigInteger()->unsigned(),
            'conditions' => $this->string(500),
            'address' => $this->string(200),
            'srok' => $this->integer(3), // Сроки выполнения работ
            'count_live' => $this->integer()->comment('tovar qanchasi tenderdan yutilgani'), // tovar qanchasi tenderdan yutilgani
            'count_used' => $this->integer(), //tender qushishda biz foydalandik turibdi
            'source_of_funding' => $this->integer(),
            'created_at' => $this->dateTime(),
            'updated_at' => $this->dateTime(),
            'deleted_at' => $this->dateTime(),
            'created_by' => $this->integer(),
            'updated_by' => $this->integer(),
        ]);

        $this->addForeignKey('FK_plan_schedule_classifier_plan_schedule',  'plan_schedule_classifier', 'plan_schedule_id', 'plan_schedule', 'id', 'CASCADE', 'CASCADE');
        $this->addForeignKey('FK_plan_schedule_classifier_classifier',  'plan_schedule_classifier', 'classifier_id', 'classifier', 'id', 'CASCADE', 'CASCADE');
        $this->addForeignKey("fk-plan_schedule_classifier-unit_id", 'plan_schedule_classifier', 'unit_id', 'unit', 'id', 'cascade', 'cascade');
        $this->createIndex("idx_plan_schedule_classifier_unit_id", 'plan_schedule_classifier', 'unit_id');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown(): void
    {
        $this->dropTable('{{%plan_schedule_classifier}}');
    }
}
