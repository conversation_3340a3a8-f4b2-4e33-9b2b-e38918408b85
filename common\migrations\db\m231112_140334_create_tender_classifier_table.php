<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%tender_classifier}}`.
 */
class m231112_140334_create_tender_classifier_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%tender_classifier}}', [
            'id' => $this->primaryKey(),
            'tender_id' => $this->integer(),
            'classifier_id' => $this->integer(),
            'plan_schedule_classifier_id' => $this->integer(),
            'number_purchased' => $this->integer(),
            'additional_charges_non_residents' => $this->integer(),//Norezidentlar uchun qo'shimcha xarajatlar qo'shiladi
            'additional_charges_amount' => $this->string(255),
            'price' => $this->bigInteger()->unsigned(),
            'unit_id' => $this->integer(),
            'expiry_date_value' => $this->integer(),
            'expiry_date_unit' => $this->integer(),//yil oy kun 
            'delivery_period' => $this->integer(),
            'description' => $this->string(512),
            'status' => $this->integer(),
            'created_at' => $this->dateTime(),
            'updated_at' => $this->dateTime(),
            'deleted_at' => $this->dateTime(),
            'created_by' => $this->integer(),
            'updated_by' => $this->integer(),
        ]);
        $this->addCommentOnColumn('tender_classifier', 'additional_charges_non_residents', 'Norezidentlar uchun qoshimcha xarajatlar qoshiladi');

        $this->addForeignKey('FK_tender_classifier_tender',  'tender_classifier', 'tender_id',   'tender', 'id', 'CASCADE', 'CASCADE');
        $this->addForeignKey('FK_tender_classifier_classifier_id',  'tender_classifier', 'classifier_id',   'classifier', 'id', 'CASCADE', 'CASCADE');
        $this->addForeignKey('FK_tender_classifier_plan_schedule_classifier_id',  'tender_classifier', 'plan_schedule_classifier_id',   'plan_schedule_classifier', 'id', 'CASCADE', 'CASCADE');

    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%tender_classifier}}');
    }
}
