<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%auction}}`.
 */
class m231115_193117_create_auction_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp(): void
    {
        $this->createTable('{{%auction}}', [
            'id' => $this->primaryKey(),
            'title' => $this->string(),
            'company_id' => $this->integer(),
            'plan_schedule_id' => $this->integer(),
            'organ' => $this->string(11),
            'lot' => $this->string(14),
            'account' => $this->string(27),
            'status' => $this->integer(),
            'total_sum' => $this->bigInteger()->unsigned(),
            'cancel_reason' => $this->string(),
            'begin_date' => $this->timestamp(),
            'auction_end' => $this->timestamp(),
            'cancel_date' => $this->timestamp(),
            'payment_status' => $this->integer(),
            'payment_date' => $this->timestamp(),
            'delivery_period' => $this->integer(),
            'payment_period' => $this->integer(),
            'region_id' => $this->integer(),
            'district_id' => $this->integer(),
            'address' => $this->string(),
            'delivery_basis' => $this->string(10),
            'description' => $this->text(),
            'organization_phone' => $this->string(),
            'created_at' => $this->dateTime(),
            'updated_at' => $this->dateTime(),
            'deleted_at' => $this->dateTime(),
            'created_by' => $this->integer(),
            'updated_by' => $this->integer(),
        ]);

        $this->createIndex('idx-auction-title', 'auction', 'title');
        $this->createIndex('idx-plan_schedule_id-auction', 'auction', 'plan_schedule_id');
        $this->addForeignKey(
            'fk-plan_schedule_id-auction',
            'auction',
            'plan_schedule_id',
            'plan_schedule',
            'id',
            'SET NULL',
            'CASCADE'
        );
        $this->createIndex("idx-auction-company_id", 'auction', 'company_id');
        $this->createIndex("idx-auction-region_id", 'auction', 'region_id');
        $this->createIndex("idx-auction-district_id", 'auction', 'district_id');


        $this->addForeignKey(
            'fk-auction-company_id',
            'auction',
            'company_id',
            'company',
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-auction-region_id',
            'auction',
            'region_id',
            'region',
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-auction-district_id',
            'auction',
            'district_id',
            'region',
            'id',
            'CASCADE',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown(): void
    {

        $this->dropForeignKey(
            'fk-auction-company_id',
            'auction'
        );

        $this->dropIndex("idx-auction-company_id", 'auction');

        $this->dropTable('{{%auction}}');
    }
}
