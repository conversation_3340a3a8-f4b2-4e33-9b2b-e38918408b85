<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%auction_classifier}}`.
 */
class m231115_194802_create_auction_classifier_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%auction_classifier}}', [
            'id' => $this->primaryKey(),
            'auction_id' => $this->integer(),
            'plan_schedule_id' => $this->integer(),
            'plan_schedule_classifier_id' => $this->integer(),
            'order' => $this->integer(),
            'classifier_category_id' => $this->integer(),
            'classifier_id' => $this->integer(),
            'quantity' => $this->integer(),
            'unit_id' => $this->integer(),
            'price' => $this->bigInteger()->unsigned(),
            'total_sum' => $this->bigInteger()->unsigned(),
            'description' => $this->text(),

            'created_at' => $this->dateTime(),
            'updated_at' => $this->dateTime(),
            'deleted_at' => $this->dateTime(),
            'created_by' => $this->integer(),
            'updated_by' => $this->integer(),
            'status' => $this->integer(),
        ]);

        $this->createIndex(
            'idx-auction_classifier-auction_id',
            'auction_classifier',
            'auction_id',
        );

        $this->createIndex(
            'idx-auction_classifier-classifier_category_id',
            'auction_classifier',
            'classifier_category_id'
        );

        $this->createIndex(
            'idx-auction_classifier-classifier_id',
            'auction_classifier',
            'classifier_id',
        );

        // Create foreign key for 'unit_id'
        $this->createIndex(
            'idx-auction_classifier-unit_id',
            'auction_classifier',
            'unit_id',
        );

        $this->addForeignKey(
            'fk-auction_classifier-auction_id',
            'auction_classifier',
            'auction_id',
            'auction',
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-auction_classifier-classifier_category_id',
            'auction_classifier',
            'classifier_category_id',
            'classifier_category',
            'id',
            'CASCADE',
            'CASCADE',
        );

        $this->addForeignKey(
            'fk-auction_classifier-classifier_id',
            'auction_classifier',
            'classifier_id',
            'classifier',
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-auction_classifier-unit_id',
            'auction_classifier',
            'unit_id',
            'unit',
            'id',
            'CASCADE',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropIndex(
            'idx-auction_classifier-auction_id',
            'auction_classifier',
        );

        $this->dropIndex(
            'idx-auction_classifier-classifier_id',
            'auction_classifier',
        );

        $this->dropIndex(
            'idx-auction_classifier-unit_id',
            'auction_classifier',
        );

        $this->dropForeignKey(
            'fk-auction_classifier-auction_id',
            'auction_classifier',
        );

        $this->dropForeignKey(
            'fk-auction_classifier-classifier_id',
            'auction_classifier',
        );

        // Create foreign key for 'unit_id'
        $this->dropForeignKey(
            'fk-auction_classifier-unit_id',
            'auction_classifier',
        );

        $this->dropTable('{{%auction_classifier}}');
    }
}
