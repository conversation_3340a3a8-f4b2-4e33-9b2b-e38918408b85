<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%auction_request}}`.
 */
class m231115_202513_create_auction_request_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%auction_request}}', [
            'id' => $this->primaryKey(),
            'auction_id' => $this->integer(),
            'company_id' => $this->integer(),

            'price' => $this->bigInteger()->unsigned(),
            'is_winner' => $this->integer(),
            'create' => $this->integer(),

            'created_at' => $this->dateTime(),
            'updated_at' => $this->dateTime(),
            'deleted_at' => $this->dateTime(),
            'created_by' => $this->integer(),
            'updated_by' => $this->integer(),
        ]);

        $this->createIndex(
            'idx-auction_request-auction_id',
            'auction_request',
            'auction_id',
        );

        $this->createIndex(
            'idx-auction_request-company_id',
            'auction_request',
            'company_id',
        );

        $this->addForeignKey(
            'fk-auction_request-auction_id',
            'auction_request',
            'auction_id',
            'auction',
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-auction_request-company_id',
            'auction_request',
            'company_id',
            'company',
            'id',
            'CASCADE',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey(
            'fk-auction_request-auction_id',
            'auction_request',
        );

        $this->dropForeignKey(
            'fk-auction_request-company_id',
            'auction_request',
        );

        $this->dropIndex(
            'idx-auction_request-auction_id',
            'auction_request',
        );

        $this->dropIndex(
            'idx-auction_request-company_id',
            'auction_request',
        );

        $this->dropTable('{{%auction_request}}');
    }
}
