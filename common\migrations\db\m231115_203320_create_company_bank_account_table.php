<?php

use common\enums\CurrencyEnum;
use yii\db\Migration;

/**
 * Handles the creation of table `{{%company_bank_account}}`.
 */
class m231115_203320_create_company_bank_account_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%company_bank_account}}', columns: [
            'id' => $this->primaryKey(),
            'company_id' => $this->integer(),
            'organ' => $this->string(11)->comment('budjet tashkilotni unikal kodi'),
            'tin' => $this->string(14),
            'account' => $this->string(27)->comment('LICACC'),
            'currency_code' => $this->string(5)->defaultValue(CurrencyEnum::CURRENCY_SUM),
            'is_main' => $this->integer(),
            'type' => $this->integer()->comment('0-korporativ, 1-budjet, 2-xorij'),
            'change' => $this->string(2)->comment('I-добавить, U-обновить'),
            'fin' => $this->string(3),
            'bud' => $this->integer(),
            'territory' => $this->string(5),
            'name' => $this->string(160),
            'state' => $this->string(1)->comment('A – актив или P – пассив'),
            'dateopen' => $this->string(8), //dd mm yyyy
            'dateclose' => $this->string(8), //dd mm yyyy
            'datechange' => $this->string(8), //dd mm yyyy
            'klstype' => $this->string(1)->comment('Y – бюджетный счет, N – внебюджетный счет'),
            'mfo' => $this->string(10),
            'swift' => $this->string(150),
            'created_at' => $this->dateTime(),
            'updated_at' => $this->dateTime(),
            'deleted_at' => $this->dateTime(),
            'created_by' => $this->integer(),
            'updated_by' => $this->integer(),
        ]);

        $this->createIndex(
            'idx-company_bank_account-company_id',
            'company_bank_account',
            'company_id',
        );
//
//        $this->createIndex(
//            'idx-company_bank_account-mfo',
//            'company_bank_account',
//            'mfo',
//        );

        $this->addForeignKey(
            'fk-company_bank_account-company_id',
            'company_bank_account',
            'company_id',
            'company',
            'id',
            'CASCADE',
            'CASCADE'
        );

//        $this->addForeignKey(
//            'fk-company_bank_account-mfo',
//            'company_bank_account',
//            'mfo',
//            'bank',
//            'mfo',
//            'CASCADE',
//            'CASCADE'
//        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey(
            'fk-company_bank_account-company_id',
            'company_bank_account',
        );

//        $this->dropForeignKey(
//            'fk-company_bank_account-mfo',
//            'company_bank_account',
//        );

        $this->dropIndex(
            'idx-company_bank_account-company_id',
            'company_bank_account',
        );

//        $this->dropIndex(
//            'idx-company_bank_account-mfo',
//            'company_bank_account',
//        );

        $this->dropTable('{{%company_bank_account}}');
    }
}

/*{
    "CHANGE": "U",
    "FIN": "000",
    "BUD":0,
    "ORGAN":"***********",
    "INN":"*********",
    "LICACC":"100010860262807092100075002",
    "TERRITORY":"26280",
    "NAME":"Cпециализированная общеобразовательная школа с углубленным изучением точных предметов при МНО Руз",
    "STATE": "A",
    "DATEOPEN":"********",
    "DATECLOSE": "",
    "DATECHANGE":"********",
    "KLSTYPE": "Y"
}
*/