<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%contract}}`.
 */
class m231115_205530_create_contract_table extends Migration
{
  /**
   * {@inheritdoc}
   */
  public function safeUp(): void
  {
    $this->createTable('{{%contract}}', [
      'id' => $this->primaryKey(),
      'procedure_type' => $this->string(10)->comment('procedure type: e-shop, n-shop, tender, ....'), // see contract enum
      'number'  => $this->string(),
      'customer_id' => $this->integer(),
      'organ' => $this->string(11)->comment('byudjet korxona uchun, unikal'),
      'producer_id' => $this->integer(),
      'auction_id' => $this->integer(),
      'tender_id' => $this->integer(),
      'order_id' => $this->integer(),
      'price' => $this->bigInteger()->unsigned(),
      'need_price' => $this->bigInteger()->unsigned(),
      'customer_signed' => $this->integer(1),
      'producer_signed' => $this->integer(1),
      'status' => $this->integer(),
      'customer_sign_date' => $this->dateTime(),
      'producer_sign_date' => $this->dateTime(),
      'customer_pay_date' => $this->dateTime(),
      'customer_mark_delivered_date' => $this->dateTime(),
      'customer_cancel_date' => $this->dateTime(),
      'producer_cancel_date' => $this->dateTime(),
      'facture_file_id' => $this->integer(),
      'reserve' => $this->integer(),
      'payment_last_date' => $this->dateTime(),
      'file_id' => $this->integer(),
      'created_at' => $this->dateTime(),
      'updated_at' => $this->dateTime(),
      'deleted_at' => $this->dateTime(),
      'created_by' => $this->integer(),
      'updated_by' => $this->integer(),
    ]);

    $this->createIndex(
      'idx-contract-procedure_type',
      'contract',
      'procedure_type'
    );

    $this->createIndex(
        'idx-contract-file_id',
        'contract',
        'file_id'
    );
    $this->addForeignKey(
        'fk-contract-file_id',
        'contract',
        'file_id',
        'file',
        'id'
    );
    $this->createIndex(
      'idx-contract-customer_id',
      'contract',
      'customer_id',
    );

    $this->createIndex(
      'idx-contract-producer_id',
      'contract',
      'producer_id',
    );

    $this->createIndex(
      'idx-contract-auction_id',
      'contract',
      'auction_id',
    );

    $this->addForeignKey(
      'fk-contract-customer_id',
      'contract',
      'customer_id',
      'company',
      'id',
      'CASCADE'
    );

    $this->addForeignKey(
      'fk-contract-producer_id',
      'contract',
      'producer_id',
      'company',
      'id',
      'CASCADE'
    );

    $this->addForeignKey(
      'fk-contract-auction_id',
      'contract',
      'auction_id',
      'auction',
      'id',
      'CASCADE'
    );
    $this->addForeignKey(
          'fk-contract-tender_id',
          'contract',
          'tender_id',
          'tender',
          'id',
          'CASCADE'
    );
//    $this->addForeignKey(
//          'fk-contract-order_id',
//          'contract',
//          'order_id',
//          'order',
//          'id',
//          'CASCADE'
//    );
  }

  /**
   * {@inheritdoc}
   */
  public function safeDown(): void
  {
    $this->dropForeignKey(
      'fk-contract-customer_id',
      'contract',
    );

    $this->dropForeignKey(
      'fk-contract-producer_id',
      'contract',
    );

    $this->dropForeignKey(
      'fk-contract-auction_id',
      'contract',
    );

    $this->dropIndex(
      'idx-contract-customer_id',
      'contract',
    );

    $this->dropIndex(
      'idx-contract-producer_id',
      'contract',
    );

    $this->dropIndex(
      'idx-contract-auction_id',
      'contract',
    );

    $this->dropTable('{{%contract}}');
  }
}
