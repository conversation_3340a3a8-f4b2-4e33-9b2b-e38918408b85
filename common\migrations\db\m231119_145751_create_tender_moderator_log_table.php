<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%tender_moderator_log}}`.
 */
class m231119_145751_create_tender_moderator_log_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp(): void
    {
        $this->createTable('{{%tender_moderator_log}}', [
            'id' => $this->primaryKey(),
            'tender_id' => $this->integer(),
            'auction_id' => $this->integer(),
            'product_id' => $this->integer(),
            'state' => $this->integer(),
            'description' => $this->string(255),
            'moderator_pinfl' => $this->string(14),
            'created_at' => $this->dateTime(),
            'updated_at' => $this->dateTime(),
            'deleted_at' => $this->dateTime(),
            'created_by' => $this->integer(),
            'updated_by' => $this->integer(),
        ]);

        $this->addForeignKey("fk-tender_moderator_log_tender", 'tender_moderator_log', 'tender_id', 'tender', 'id', 'cascade', 'cascade');

    }

    /**
     * {@inheritdoc}
     */
    public function safeDown(): void
    {
        $this->dropTable('{{%tender_moderator_log}}');
    }
}
