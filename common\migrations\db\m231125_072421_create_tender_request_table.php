<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%tender_request}}`.
 */
class m231125_072421_create_tender_request_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%tender_request}}', [
            'id' => $this->primaryKey(),
            'tender_id' => $this->integer(),
            'company_id' => $this->integer()->comment('request yuboruvchi comp'),
            'price' => $this->bigInteger()->unsigned()->comment('umumiy summa'),
            'price_qqs' => $this->bigInteger()->unsigned(),
            'preference_local_producer' => $this->integer()->comment('maxalliy ishlab chiqaruvchi'),
            'preference_local_producer_file_id' => $this->integer()->comment('maxalliy ishlab chiqaruvchi fayli'),
            'is_preference_local_producer' => $this->boolean(),
            'description' => $this->string(255)->comment('secretary ot<PERSON><PERSON> q<PERSON>a nimadir yozadi.'),
            'status' => $this->integer(),
            'state' => $this->integer()->comment('10-yangi, 20- malaka tanloviga javob bermadi. 30 o\'tdi'),
            'is_winner' => $this->integer(),//
            'disclassification_system' => $this->integer(),//
            'disclassification' => $this->integer(),//
            'disclassification_text' => $this->string(512),//
            'conflict_interest' => $this->integer(),//
            'conflict_interest_text' => $this->string(512),//
            'created_at' => $this->dateTime(),
            'updated_at' => $this->dateTime(),
            'deleted_at' => $this->dateTime(),
            'created_by' => $this->integer(),
            'updated_by' => $this->integer(),
        ]);
        $this->addForeignKey("fk-tender_request_tender", 'tender_request', 'tender_id', 'tender', 'id', 'cascade', 'cascade');
        $this->addForeignKey("fk-tender_request_company", 'tender_request', 'company_id', 'company', 'id', 'cascade', 'cascade');
        $this->addForeignKey("fk-tender_request_file", 'tender_request', 'preference_local_producer_file_id', 'file', 'id', 'cascade', 'cascade');
        $this->createIndex("idx_tender_request_tender_id", 'tender_request', 'tender_id');

    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%tender_request}}');
    }
}
