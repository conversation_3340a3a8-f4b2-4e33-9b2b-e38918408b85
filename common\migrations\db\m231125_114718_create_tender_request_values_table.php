<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%tender_request_values}}`.
 */
class m231125_114718_create_tender_request_values_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%tender_request_values}}', [
            'id' => $this->primaryKey(),
            'tender_id' => $this->integer(),
            'tender_classifier_id' => $this->integer(),
            'classifier_id' => $this->integer(),
            'tender_request_id' => $this->integer(),
            'country_id' => $this->integer(),
            'price' => $this->bigInteger()->unsigned(),
            'price_qqs' => $this->bigInteger()->unsigned(),
            'purchase_currency' => $this->integer(),//
            'status' => $this->integer(),//
            'created_at' => $this->dateTime(),
            'updated_at' => $this->dateTime(),
            'deleted_at' => $this->dateTime(),
            'created_by' => $this->integer(),
            'updated_by' => $this->integer(),
        ]);

        $this->addForeignKey("fk-tender_request_values_tender_request", 'tender_request_values', 'tender_request_id', 'tender_request', 'id', 'cascade', 'cascade');
        $this->addForeignKey("fk-tender_request_values_tender", 'tender_request_values', 'tender_id', 'tender', 'id', 'cascade', 'cascade');
        $this->addForeignKey("fk-tender_request_values_classifier", 'tender_request_values', 'classifier_id', 'classifier', 'id', 'cascade', 'cascade');
        $this->addForeignKey("fk-tender_request_values_tender_classifier_id", 'tender_request_values', 'tender_classifier_id', 'tender_classifier', 'id', 'cascade', 'cascade');
        $this->addForeignKey("fk-tender_request_values_country", 'tender_request_values', 'country_id', 'region', 'id', 'cascade', 'cascade');
        $this->createIndex("idx_tender_request_values_tender_id", 'tender_request_values', 'tender_id');
        $this->createIndex("idx_tender_request_values_tender_request_id", 'tender_request_values', 'tender_request_id');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%tender_request_values}}');
    }
}
