<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%product}}`.
 */
class m231129_213303_create_product_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp(): void
    {
        $this->createTable('{{%product}}', [
            'id' => $this->primaryKey(),
            'platform_display' => $this->string(),
            'classifier_id' => $this->integer(),
            'classifier_category_id' => $this->integer(),
            'company_id' => $this->integer(),
            'organ' => $this->string(11),
            'title' => $this->string(),
            'brand_title' => $this->string(),
            'description_uz' => $this->text(),
            'description_ru' => $this->text(),
            'year' => $this->integer(),
            'quantity' => $this->integer(),
            'price' => $this->bigInteger()->unsigned(),
            'min_order' => $this->integer(),
            'max_order' => $this->integer(),
            'delivery_period' => $this->integer(),
            'warranty_period' => $this->integer(),
            'is_have_license' => $this->boolean(),
            'type' => $this->string(),
            'unit_price' => $this->string(),
            'state' => $this->integer(),
            'delete_reason' => $this->string(),
            'country_id' => $this->integer(),
            'unit_id' => $this->integer(),
            'auto_renewal' => $this->boolean(),
            'expire_date' => $this->date(),
            'made_by' => $this->string(),
            'active_date' => $this->dateTime(),
            'inactive_date' => $this->dateTime(),
            'status' => $this->integer(),
            'created_at' => $this->dateTime(),
            'updated_at' => $this->dateTime(),
            'deleted_at' => $this->dateTime(),
            'created_by' => $this->integer(),
            'updated_by' => $this->integer(),
        ]);

        $this->createIndex("idx_product_classifier_category_id", "product", "classifier_category_id");
        $this->createIndex("idx_product_classifier_id", "product", "classifier_id");
        $this->createIndex("idx_product_company_id", "product", "company_id");

        $this->addForeignKey("fk_product_classifier_category_id", "product", "classifier_category_id", "classifier_category", "id", "cascade", "cascade");
        $this->addForeignKey("fk_product_classifier_id", "product", "classifier_id", "classifier", "id", "cascade", "cascade");
        $this->addForeignKey("fk_product_company_id", "product", "company_id", "company", "id", "cascade", "cascade");

        $this->createIndex(
            'idx-product-unit_id',
            'product',
            'unit_id'
        );

        $this->addForeignKey(
            'fk-product-unit_id',
            'product',
            'unit_id',
            'unit',
            'id',
            'SET NULL',
            'CASCADE'
        );

        $this->createIndex(
            'idx-product-country_id',
            'product',
            'country_id',
        );
        $this->addForeignKey(
            'fk-product-country_id',
            'product',
            'country_id',
            'region',
            'id',
            'SET NULL',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown(): void
    {
        $this->dropTable('{{%product}}');
    }
}
