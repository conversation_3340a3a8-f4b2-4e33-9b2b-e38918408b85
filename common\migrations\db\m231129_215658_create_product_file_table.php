<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%product_file}}`.
 */
class m231129_215658_create_product_file_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp(): void
    {
        $this->createTable('{{%product_file}}', [
            'id' => $this->primaryKey(),
            'product_id' => $this->integer(),
            'file_id' => $this->integer(),
            'type' => $this->integer(),
            'sort' => $this->integer()->defaultValue(0),
            'created_at' => $this->dateTime(),
            'updated_at' => $this->dateTime(),
            'deleted_at' => $this->dateTime(),
            'created_by' => $this->integer(),
            'updated_by' => $this->integer(),
        ]);
        
        $this->createIndex("idx_product_file_product_id", "product_file", "product_id");
        $this->createIndex("idx_product_file_file_id", "product_file", "file_id");

        $this->addForeignKey("fk_product_file_product_id", "product_file", "product_id", "product", "id", "cascade", "cascade");
        $this->addForeignKey("fk_product_file_file_id", "product_file", "file_id", "file", "id", "cascade", "cascade");
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown(): void
    {
        $this->dropForeignKey("fk_product_file_product_id", "product_file");
        $this->dropForeignKey("fk_product_file_file_id", "product_file");

        $this->dropIndex("idx_product_file_product_id", "product_file");
        $this->dropIndex("idx_product_file_file_id", "product_file");

        $this->dropTable('{{%product_file}}');
    }
}
