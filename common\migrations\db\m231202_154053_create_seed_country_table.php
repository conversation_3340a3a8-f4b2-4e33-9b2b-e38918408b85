<?php

use common\models\Region;
use yii\db\Migration;

/**
 * Handles the creation of table `{{%seed_country}}`.
 */
class m231202_154053_create_seed_country_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp(): void
    {
        // Seed data
        $this->batchInsert('{{%region}}',
            ['title_uz', 'title_ru', 'title_en', 'type'],
            [
                ['Afgʻoniston', 'Афганистан', 'Afghanistan', Region::TYPE_COUNTRY],
                ['Albaniya', 'Албания', 'Albania', Region::TYPE_COUNTRY],
                ['Jazoir', 'Алжир', 'Algeria', Region::TYPE_COUNTRY],
                ['Amerika Samoasi', 'Американское Самоа', 'American Samoa', Region::TYPE_COUNTRY],
                ['Andorra', 'Андорра', 'Andorra', Region::TYPE_COUNTRY],
                ['Angola', 'Ангола', 'Angola', Region::TYPE_COUNTRY],
                ['Anguilla', 'Ангилья', 'Anguilla', Region::TYPE_COUNTRY],
                ['Antarktida', 'Антарктида', 'Antarctica', Region::TYPE_COUNTRY],
                ['Antigua va Barbuda', 'Антигуа и Барбуда', 'Antigua and Barbuda', Region::TYPE_COUNTRY],
                ['Argentina', 'Аргентина', 'Argentina', Region::TYPE_COUNTRY],
                ['Armaniston', 'Армения', 'Armenia', Region::TYPE_COUNTRY],
                ['Aruba', 'Аруба', 'Aruba', Region::TYPE_COUNTRY],
                ['Avstraliya', 'Австралия', 'Australia', Region::TYPE_COUNTRY],
                ['Avstriya', 'Австрия', 'Austria', Region::TYPE_COUNTRY],
                ['Ozarbayjon', 'Азербайджан', 'Azerbaijan', Region::TYPE_COUNTRY],
                ['Bagama orollari', 'Багамские острова', 'Bahamas', Region::TYPE_COUNTRY],
                ['Bahrayn', 'Бахрейн', 'Bahrain', Region::TYPE_COUNTRY],
                ['Bangladesh', 'Бангладеш', 'Bangladesh', Region::TYPE_COUNTRY],
                ['Barbados', 'Барбадос', 'Barbados', Region::TYPE_COUNTRY],
                ['Belorusiya', 'Беларусь', 'Belarus', Region::TYPE_COUNTRY],
                ['Belgiya', 'Бельгия', 'Belgium', Region::TYPE_COUNTRY],
                ['Beliz', 'Белиз', 'Belize', Region::TYPE_COUNTRY],
                ['Benin', 'Бенин', 'Benin', Region::TYPE_COUNTRY],
                ['Bermuda', 'Бермуды', 'Bermuda', Region::TYPE_COUNTRY],
                ['Butan', 'Бутан', 'Bhutan', Region::TYPE_COUNTRY],
                ['Boliviya', 'Боливия', 'Bolivia', Region::TYPE_COUNTRY],
                ['Bosniya va Gertsegovina', 'Босния и Герцеговина', 'Bosnia and Herzegovina', Region::TYPE_COUNTRY],
                ['Botsvana', 'Ботсвана', 'Botswana', Region::TYPE_COUNTRY],
                ['Bouvet Island', 'Остров Буве', 'Bouvet Island', Region::TYPE_COUNTRY],
                ['Braziliya', 'Бразилия', 'Brazil', Region::TYPE_COUNTRY],
                ['Britaniya Hind okeani hududi', 'Британская территория в Индийском океане', 'British Indian Ocean Territory', Region::TYPE_COUNTRY],
                ['Bruney Darussalom', 'Бруней-Даруссалам', 'Brunei Darussalam', Region::TYPE_COUNTRY],
                ['Bolgariya', 'Болгария', 'Bulgaria', Region::TYPE_COUNTRY],
                ['Burkina Faso', 'Буркина-Фасо', 'Burkina Faso', Region::TYPE_COUNTRY],
                ['Burundi', 'Бурунди', 'Burundi', Region::TYPE_COUNTRY],
                ['Kambodja', 'Камбоджа', 'Cambodia', Region::TYPE_COUNTRY],
                ['Kamerun', 'Камерун', 'Cameroon', Region::TYPE_COUNTRY],
                ['Kanada', 'Канада', 'Canada', Region::TYPE_COUNTRY],
                ['Kabo-Verde', 'Кабо-Верде', 'Cape Verde', Region::TYPE_COUNTRY],
                ['Kayman orollari', 'Каймановы острова', 'Cayman Islands', Region::TYPE_COUNTRY],
                ['Markaziy Afrika Respublikasi', 'Центральноафриканская Республика', 'Central African Republic', Region::TYPE_COUNTRY],
                ['Chad', 'Чад', 'Chad', Region::TYPE_COUNTRY],
                ['Chili', 'Чили', 'Chile', Region::TYPE_COUNTRY],
                ['Xitoy', 'Китай', 'China', Region::TYPE_COUNTRY],
                ['Christmas Island', 'Остров Рождества', 'Christmas Island', Region::TYPE_COUNTRY],
                ['Kokos (Keeling) orollari', 'Кокосовые (Килинг) острова', 'Cocos (Keeling) Islands', Region::TYPE_COUNTRY],
                ['Kolumbiya', 'Колумбия', 'Colombia', Region::TYPE_COUNTRY],
                ['Komoros', 'Коморы', 'Comoros', Region::TYPE_COUNTRY],
                ['Kongo', 'Конго', 'Congo', Region::TYPE_COUNTRY],
                ['Kongo Demokratik Respublikasi', 'Демократическая Республика Конго', 'Congo, the Democratic Republic of the', Region::TYPE_COUNTRY],
                ['Cook orollari', 'Острова Кука', 'Cook Islands', Region::TYPE_COUNTRY],
                ['Kosta-Rika', 'Коста-Рика', 'Costa Rica', Region::TYPE_COUNTRY],
                ['Kot-d-Ivuar', 'Кот-д’Ивуар', 'Cote D\'Ivoire', Region::TYPE_COUNTRY],
                ['Xorvatiya', 'Хорватия', 'Croatia', Region::TYPE_COUNTRY],
                ['Kuba', 'Куба', 'Cuba', Region::TYPE_COUNTRY],
                ['Qipro', 'Кипр', 'Cyprus', Region::TYPE_COUNTRY],
                ['Chexiya', 'Чехия', 'Czech Republic', Region::TYPE_COUNTRY],
                ['Daniya', 'Дания', 'Denmark', Region::TYPE_COUNTRY],
                ['Jibuti', 'Джибути', 'Djibouti', Region::TYPE_COUNTRY],
                ['Dominika', 'Доминика', 'Dominica', Region::TYPE_COUNTRY],
                ['Dominikan Respublikasi', 'Доминиканская Республика', 'Dominican Republic', Region::TYPE_COUNTRY],
                ['Ekvador', 'Эквадор', 'Ecuador', Region::TYPE_COUNTRY],
                ['Misr', 'Египет', 'Egypt', Region::TYPE_COUNTRY],
                ['El-Salvador', 'Сальвадор', 'El Salvador', Region::TYPE_COUNTRY],
                ['Ekvatorial Gvineya', 'Экваториальная Гвинея', 'Equatorial Guinea', Region::TYPE_COUNTRY],
                ['Eritreya', 'Эритрея', 'Eritrea', Region::TYPE_COUNTRY],
                ['Estoniya', 'Эстония', 'Estonia', Region::TYPE_COUNTRY],
                ['Efiopiya', 'Эфиопия', 'Ethiopia', Region::TYPE_COUNTRY],
                ['Folklend orollari', 'Фолклендские острова (Мальвинские)', 'Falkland Islands (Malvinas)', Region::TYPE_COUNTRY],
                ['Farer orollari', 'Фарерские острова', 'Faroe Islands', Region::TYPE_COUNTRY],
                ['Fiji', 'Фиджи', 'Fiji', Region::TYPE_COUNTRY],
                ['Finlandiya', 'Финляндия', 'Finland', Region::TYPE_COUNTRY],
                ['Fransiya', 'Франция', 'France', Region::TYPE_COUNTRY],
                ['Fransiya Gvianasi', 'Французская Гвиана', 'French Guiana', Region::TYPE_COUNTRY],
                ['Fransiya Polineziyasi', 'Французская Полинезия', 'French Polynesia', Region::TYPE_COUNTRY],
                ['Fransiya janubiy hududlari', 'Французские Южные территории', 'French Southern Territories', Region::TYPE_COUNTRY],
                ['Gabon', 'Габон', 'Gabon', Region::TYPE_COUNTRY],
                ['Gambiya', 'Гамбия', 'Gambia', Region::TYPE_COUNTRY],
                ['Gruziya', 'Грузия', 'Georgia', Region::TYPE_COUNTRY],
                ['Germaniya', 'Германия', 'Germany', Region::TYPE_COUNTRY],
                ['Gana', 'Гана', 'Ghana', Region::TYPE_COUNTRY],
                ['Gibraltar', 'Гибралтар', 'Gibraltar', Region::TYPE_COUNTRY],
                ['Gretsiya', 'Греция', 'Greece', Region::TYPE_COUNTRY],
                ['Grenlandiya', 'Гренландия', 'Greenland', Region::TYPE_COUNTRY],
                ['Grenada', 'Гренада', 'Grenada', Region::TYPE_COUNTRY],
                ['Gvadelupa', 'Гваделупа', 'Guadeloupe', Region::TYPE_COUNTRY],
                ['Guam', 'Гуам', 'Guam', Region::TYPE_COUNTRY],
                ['Gvatemala', 'Гватемала', 'Guatemala', Region::TYPE_COUNTRY],
                ['Gvineya', 'Гвинея', 'Guinea', Region::TYPE_COUNTRY],
                ['Gvineya-Bisau', 'Гвинея-Бисау', 'Guinea-Bissau', Region::TYPE_COUNTRY],
                ['Gayana', 'Гайана', 'Guyana', Region::TYPE_COUNTRY],
                ['Gaiti', 'Гаити', 'Haiti', Region::TYPE_COUNTRY],
                ['Heard Island and Mcdonald Islands', 'Остров Херд и острова Макдональд', 'Heard Island and Mcdonald Islands', Region::TYPE_COUNTRY],
                ['Vatikan', 'Ватикан', 'Holy See (Vatican City State)', Region::TYPE_COUNTRY],
                ['Gonduras', 'Гондурас', 'Honduras', Region::TYPE_COUNTRY],
                ['Gonkong', 'Гонконг', 'Hong Kong', Region::TYPE_COUNTRY],
                ['Vengriya', 'Венгрия', 'Hungary', Region::TYPE_COUNTRY],
                ['Islandiya', 'Исландия', 'Iceland', Region::TYPE_COUNTRY],
                ['Hindiston', 'Индия', 'India', Region::TYPE_COUNTRY],
                ['Indoneziya', 'Индонезия', 'Indonesia', Region::TYPE_COUNTRY],
                ['Eron', 'Иран', 'Iran, Islamic Republic of', Region::TYPE_COUNTRY],
                ['Iroq', 'Ирак', 'Iraq', Region::TYPE_COUNTRY],
                ['Irlandiya', 'Ирландия', 'Ireland', Region::TYPE_COUNTRY],
                ['Isroil', 'Израиль', 'Israel', Region::TYPE_COUNTRY],
                ['Italiya', 'Италия', 'Italy', Region::TYPE_COUNTRY],
                ['Yamayka', 'Ямайка', 'Jamaica', Region::TYPE_COUNTRY],
                ['Yaponiya', 'Япония', 'Japan', Region::TYPE_COUNTRY],
                ['Iordaniya', 'Иордания', 'Jordan', Region::TYPE_COUNTRY],
                ['Qozogʻiston', 'Казахстан', 'Kazakhstan', Region::TYPE_COUNTRY],
                ['Keniya', 'Кения', 'Kenya', Region::TYPE_COUNTRY],
                ['Kiribati', 'Кирибати', 'Kiribati', Region::TYPE_COUNTRY],
                ['Koreya Xalq Demokratik Respublikasi', 'Корея, Народно-Демократическая Республика', 'Korea, Democratic People\'s Republic of', Region::TYPE_COUNTRY],
                ['Koreya Respublikasi', 'Республика Корея', 'Korea, Republic of', Region::TYPE_COUNTRY],
                ['Quvayt', 'Кувейт', 'Kuwait', Region::TYPE_COUNTRY],
                ['Qirgʻiziston', 'Кыргызстан', 'Kyrgyzstan', Region::TYPE_COUNTRY],
                ['Laos Xalq Demokratik Respublikasi', 'Лаосская Народно-Демократическая Республика', 'Lao People\'s Democratic Republic', Region::TYPE_COUNTRY],
                ['Latviya', 'Латвия', 'Latvia', Region::TYPE_COUNTRY],
                ['Livan', 'Ливан', 'Lebanon', Region::TYPE_COUNTRY],
                ['Lesoto', 'Лесото', 'Lesotho', Region::TYPE_COUNTRY],
                ['Liberiya', 'Либерия', 'Liberia', Region::TYPE_COUNTRY],
                ['Liviya', 'Ливия', 'Libyan Arab Jamahiriya', Region::TYPE_COUNTRY],
                ['Lixtenshteyn', 'Лихтенштейн', 'Liechtenstein', Region::TYPE_COUNTRY],
                ['Litva', 'Литва', 'Lithuania', Region::TYPE_COUNTRY],
                ['Lyuksemburg', 'Люксембург', 'Luxembourg', Region::TYPE_COUNTRY],
                ['Makao', 'Макао', 'Macao', Region::TYPE_COUNTRY],
                ['Makedoniya', 'Македония', 'Macedonia, the Former Yugoslav Republic of', Region::TYPE_COUNTRY],
                ['Madagaskar', 'Мадагаскар', 'Madagascar', Region::TYPE_COUNTRY],
                ['Malavi', 'Малави', 'Malawi', Region::TYPE_COUNTRY],
                ['Malayziya', 'Малайзия', 'Malaysia', Region::TYPE_COUNTRY],
                ['Maldiv orollari', 'Мальдивы', 'Maldives', Region::TYPE_COUNTRY],
                ['Mali', 'Мали', 'Mali', Region::TYPE_COUNTRY],
                ['Malta', 'Мальта', 'Malta', Region::TYPE_COUNTRY],
                ['Marshall orollari', 'Маршалловы острова', 'Marshall Islands', Region::TYPE_COUNTRY],
                ['Martinika', 'Мартиника', 'Martinique', Region::TYPE_COUNTRY],
                ['Mavritaniya', 'Мавритания', 'Mauritania', Region::TYPE_COUNTRY],
                ['Mavrikiy', 'Маврикий', 'Mauritius', Region::TYPE_COUNTRY],
                ['Mayotte', 'Майотта', 'Mayotte', Region::TYPE_COUNTRY],
                ['Meksika', 'Мексика', 'Mexico', Region::TYPE_COUNTRY],
                ['Mikroneziya Federativ Shtatlari', 'Федеративные Штаты Микронезии', 'Micronesia, Federated States of', Region::TYPE_COUNTRY],
                ['Moldova', 'Молдова', 'Moldova, Republic of', Region::TYPE_COUNTRY],
                ['Monako', 'Монако', 'Monaco', Region::TYPE_COUNTRY],
                ['Mongoliya', 'Монголия', 'Mongolia', Region::TYPE_COUNTRY],
                ['Montserrat', 'Монтсеррат', 'Montserrat', Region::TYPE_COUNTRY],
                ['Marokash', 'Марокко', 'Morocco', Region::TYPE_COUNTRY],
                ['Mozambik', 'Мозамбик', 'Mozambique', Region::TYPE_COUNTRY],
                ['Myanma', 'Мьянма', 'Myanmar', Region::TYPE_COUNTRY],
                ['Namibiya', 'Намибия', 'Namibia', Region::TYPE_COUNTRY],
                ['Nauru', 'Науру', 'Nauru', Region::TYPE_COUNTRY],
                ['Nepal', 'Непал', 'Nepal', Region::TYPE_COUNTRY],
                ['Niderlandiya', 'Нидерланды', 'Netherlands', Region::TYPE_COUNTRY],
                ['Niderlandiya Antillari', 'Нидерландские Антильские острова', 'Netherlands Antilles', Region::TYPE_COUNTRY],
                ['Yangi Kaledoniya', 'Новая Каледония', 'New Caledonia', Region::TYPE_COUNTRY],
                ['Yangi Zelandiya', 'Новая Зеландия', 'New Zealand', Region::TYPE_COUNTRY],
                ['Nikaragua', 'Никарагуа', 'Nicaragua', Region::TYPE_COUNTRY],
                ['Niger', 'Нигер', 'Niger', Region::TYPE_COUNTRY],
                ['Nigeriya', 'Нигерия', 'Nigeria', Region::TYPE_COUNTRY],
                ['Niue', 'Ниуэ', 'Niue', Region::TYPE_COUNTRY],
                ['Norfolk Island', 'Остров Норфолк', 'Norfolk Island', Region::TYPE_COUNTRY],
                ['Shimoliy Mariana orollari', 'Северные Марианские острова', 'Northern Mariana Islands', Region::TYPE_COUNTRY],
                ['Norvegiya', 'Норвегия', 'Norway', Region::TYPE_COUNTRY],
                ['Ummon', 'Оман', 'Oman', Region::TYPE_COUNTRY],
                ['Pokiston', 'Пакистан', 'Pakistan', Region::TYPE_COUNTRY],
                ['Palau', 'Палау', 'Palau', Region::TYPE_COUNTRY],
                ['Falastin hududi', 'Палестинская территория', 'Palestinian Territory, Occupied', Region::TYPE_COUNTRY],
                ['Panama', 'Панама', 'Panama', Region::TYPE_COUNTRY],
                ['Papua Yangi Gvineya', 'Папуа — Новая Гвинея', 'Papua New Guinea', Region::TYPE_COUNTRY],
                ['Paragvay', 'Парагвай', 'Paraguay', Region::TYPE_COUNTRY],
                ['Peru', 'Перу', 'Peru', Region::TYPE_COUNTRY],
                ['Filippin', 'Филиппины', 'Philippines', Region::TYPE_COUNTRY],
                ['Pitcairn', 'Питкэрн', 'Pitcairn', Region::TYPE_COUNTRY],
                ['Polsha', 'Польша', 'Poland', Region::TYPE_COUNTRY],
                ['Portugaliya', 'Португалия', 'Portugal', Region::TYPE_COUNTRY],
                ['Puerto-Riko', 'Пуэрто-Рико', 'Puerto Rico', Region::TYPE_COUNTRY],
                ['Qatar', 'Катар', 'Qatar', Region::TYPE_COUNTRY],
                ['Reunion', 'Реюньон', 'Reunion', Region::TYPE_COUNTRY],
                ['Ruminiya', 'Румыния', 'Romania', Region::TYPE_COUNTRY],
                ['Rossiya', 'Россия', 'Russian Federation', Region::TYPE_COUNTRY],
                ['Ruanda', 'Руанда', 'Rwanda', Region::TYPE_COUNTRY],
                ['Muqaddas Yelena', 'Святая Елена', 'Saint Helena', Region::TYPE_COUNTRY],
                ['Sent-Kitts va Nevis', 'Сент-Китс и Невис', 'Saint Kitts and Nevis', Region::TYPE_COUNTRY],
                ['Sent-Lusiya', 'Сент-Люсия', 'Saint Lucia', Region::TYPE_COUNTRY],
                ['Sent-Per va Mikelon', 'Сен-Пьер и Микелон', 'Saint Pierre and Miquelon', Region::TYPE_COUNTRY],
                ['Sent-Vinsent va Grenadinlar', 'Сент-Винсент и Гренадины', 'Saint Vincent and the Grenadines', Region::TYPE_COUNTRY],
                ['Samoa', 'Самоа', 'Samoa', Region::TYPE_COUNTRY],
                ['San-Marino', 'Сан-Марино', 'San Marino', Region::TYPE_COUNTRY],
                ['San-Tome va Prinsipi', 'Сан-Томе и Принсипи', 'Sao Tome and Principe', Region::TYPE_COUNTRY],
                ['Saudiya Arabistoni', 'Саудовская Аравия', 'Saudi Arabia', Region::TYPE_COUNTRY],
                ['Senegal', 'Сенегал', 'Senegal', Region::TYPE_COUNTRY],
                ['Serbiya va Chernogoriya', 'Сербия и Черногория', 'Serbia and Montenegro', Region::TYPE_COUNTRY],
                ['Seyshell orollari', 'Сейшельские острова', 'Seychelles', Region::TYPE_COUNTRY],
                ['Syerra-Leone', 'Сьерра-Леоне', 'Sierra Leone', Region::TYPE_COUNTRY],
                ['Singapur', 'Сингапур', 'Singapore', Region::TYPE_COUNTRY],
                ['Slovakiya', 'Словакия', 'Slovakia', Region::TYPE_COUNTRY],
                ['Sloveniya', 'Словения', 'Slovenia', Region::TYPE_COUNTRY],
                ['Solomon orollari', 'Соломоновы острова', 'Solomon Islands', Region::TYPE_COUNTRY],
                ['Somali', 'Сомали', 'Somalia', Region::TYPE_COUNTRY],
                ['Janubiy Afrika', 'Южная Африка', 'South Africa', Region::TYPE_COUNTRY],
                ['Janubiy Jorjiya va Janubiy Sendvich orollari', 'Южная Георгия и Южные Сандвичевы острова', 'South Georgia and the South Sandwich Islands', Region::TYPE_COUNTRY],
                ['Ispaniya', 'Испания', 'Spain', Region::TYPE_COUNTRY],
                ['Shri-Lanka', 'Шри-Ланка', 'Sri Lanka', Region::TYPE_COUNTRY],
                ['Sudan', 'Судан', 'Sudan', Region::TYPE_COUNTRY],
                ['Surinam', 'Суринам', 'Suriname', Region::TYPE_COUNTRY],
                ['Svalbard va Yan-Mayen', 'Шпицберген и Ян-Майен', 'Svalbard and Jan Mayen', Region::TYPE_COUNTRY],
                ['Svazilend', 'Свазиленд', 'Swaziland', Region::TYPE_COUNTRY],
                ['Shvetsiya', 'Швеция', 'Sweden', Region::TYPE_COUNTRY],
                ['Shveytsariya', 'Швейцария', 'Switzerland', Region::TYPE_COUNTRY],
                ['Suriya', 'Сирия', 'Syrian Arab Republic', Region::TYPE_COUNTRY],
                ['Tayvan', 'Тайвань', 'Taiwan, Province of China', Region::TYPE_COUNTRY],
                ['Tojikiston', 'Таджикистан', 'Tajikistan', Region::TYPE_COUNTRY],
                ['Tanzaniya', 'Танзания', 'Tanzania, United Republic of', Region::TYPE_COUNTRY],
                ['Tayland', 'Таиланд', 'Thailand', Region::TYPE_COUNTRY],
                ['Timor-Leste', 'Тимор-Лесте', 'Timor-Leste', Region::TYPE_COUNTRY],
                ['Togo', 'Того', 'Togo', Region::TYPE_COUNTRY],
                ['Tokelau', 'Токелау', 'Tokelau', Region::TYPE_COUNTRY],
                ['Tonga', 'Тонга', 'Tonga', Region::TYPE_COUNTRY],
                ['Trinidad va Tobago', 'Тринидад и Тобаго', 'Trinidad and Tobago', Region::TYPE_COUNTRY],
                ['Tunis', 'Тунис', 'Tunisia', Region::TYPE_COUNTRY],
                ['Turkiya', 'Турция', 'Turkey', Region::TYPE_COUNTRY],
                ['Turkmaniston', 'Туркменистан', 'Turkmenistan', Region::TYPE_COUNTRY],
                ['Turks va Kaykos orollari', 'Острова Теркс и Кайкос', 'Turks and Caicos Islands', Region::TYPE_COUNTRY],
                ['Tuvalu', 'Тувалу', 'Tuvalu', Region::TYPE_COUNTRY],
                ['Uganda', 'Уганда', 'Uganda', Region::TYPE_COUNTRY],
                ['Ukraina', 'Украина', 'Ukraine', Region::TYPE_COUNTRY],
                ['Birlashgan Arab Amirliklari', 'Объединённые Арабские Эмираты', 'United Arab Emirates', Region::TYPE_COUNTRY],
                ['Buyuk Britaniya', 'Великобритания', 'United Kingdom', Region::TYPE_COUNTRY],
                ['Amerika Qoʻshma Shtatlari', 'Соединённые Штаты', 'United States', Region::TYPE_COUNTRY],
                ['AQSH Kichik Tashqi Orollari', 'Малые отдалённые острова США', 'United States Minor Outlying Islands', Region::TYPE_COUNTRY],
                ['Urugvay', 'Уругвай', 'Uruguay', Region::TYPE_COUNTRY],
                ['Oʻzbekiston', 'Узбекистан', 'Uzbekistan', Region::TYPE_COUNTRY],
                ['Vanuatu', 'Вануату', 'Vanuatu', Region::TYPE_COUNTRY],
                ['Venesuela', 'Венесуэла', 'Venezuela', Region::TYPE_COUNTRY],
                ['Vyetnam', 'Вьетнам', 'Viet Nam', Region::TYPE_COUNTRY],
                ['Britaniya Virgin orollari', 'Британские Виргинские острова', 'Virgin Islands, British', Region::TYPE_COUNTRY],
                ['AQSH Virgin orollari', 'Виргинские острова, США', 'Virgin Islands, U.S.', Region::TYPE_COUNTRY],
                ['Uollis va Futuna', 'Уоллис и Футуна', 'Wallis and Futuna', Region::TYPE_COUNTRY],
                ['Gʻarbiy Sahara', 'Западная Сахара', 'Western Sahara', Region::TYPE_COUNTRY],
                ['Yaman', 'Йемен', 'Yemen', Region::TYPE_COUNTRY],
                ['Zambiya', 'Замбия', 'Zambia', Region::TYPE_COUNTRY],
                ['Zimbabve', 'Зимбабве', 'Zimbabwe', Region::TYPE_COUNTRY],
            ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown(): void
    {
        Region::deleteAll(['type' => Region::TYPE_COUNTRY]);
    }
}
