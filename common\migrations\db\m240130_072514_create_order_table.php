<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%order}}`.
 */
class m240130_072514_create_order_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp(): void
    {
        $this->createTable('{{%order}}', [
            'id' => $this->primaryKey(),
            'user_id' => $this->integer(),
            'product_id' => $this->integer(),
            'type' => $this->integer(),
            'lot_number' => $this->string(20),
            'plan_schedule_id' => $this->integer(),
            'classifier_id' => $this->integer(),
            'company_id' => $this->integer(),
            'begin_date' => $this->dateTime(),
            'organ' => $this->string(11),
            'customer_account_treasury' => $this->string(),
            'expense_item' => $this->string(),
            'receiver_fio' => $this->string(),
            'receiver_phone' => $this->string(),
            'delivery_type' => $this->integer(),
            'payment_type' => $this->integer(),
            'total_sum' => $this->bigInteger()->unsigned(),
            'shipping_sum' =>$this->double(),
            'status' => $this->integer(),
            'payment_status' => $this->integer(),
            'payment_date' => $this->integer(),
            'cancel_reason' => $this->text(),
            'cancel_date' => $this->dateTime(),
            'shop_end' => $this->dateTime(),
            'request_end' => $this->dateTime(),
            'account_number' => $this->string(),
            'account_number_id' => $this->integer(),
            'count' => $this->integer(),
            'start_one_sided_customer' => $this->dateTime(),
            'start_one_sided_producer' => $this->dateTime(),
            'one_sided_customer' => $this->dateTime(),
            'created_at' =>$this->dateTime(),
        ]);

        $this->createIndex(
            'idx-order-product_id',
            'order',
            'product_id'
        );
        $this->addForeignKey(
            'fk-order-product_id',
            'order',
            'product_id',
            'product',
            'id',
            'CASCADE',
            'CASCADE',
        );

        $this->createIndex(
            'idx-order-plan_schedule_id',
            'order',
            'plan_schedule_id',
        );
        $this->addForeignKey(
            'fk-order-plan_schedule_id',
            'order',
            'plan_schedule_id',
            'plan_schedule',
            'id',
            'CASCADE',
            'CASCADE',
        );

        $this->createIndex("idx_order_user_id", "order", "user_id");
        $this->createIndex("idx_order_company_id", "order", "company_id");


        $this->addForeignKey("fk_order_user_id", "order", "user_id", "user", "id", "cascade", "cascade");
        $this->addForeignKey("fk_order_company_id", "order", "company_id", "company", "id", "cascade", "cascade");

    }

    /**
     * {@inheritdoc}
     */
    public function safeDown(): void
    {
        $this->dropTable('{{%order}}');
    }
}
