<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%cart}}`.
 */
class m240130_082210_create_cart_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp(): void
    {
        $this->createTable('{{%cart}}', [
            'id' => $this->primaryKey(),
            'user_id' => $this->integer(),
            'company_id' => $this->integer(),
            'product_id' => $this->integer(),
            'price' => $this->bigInteger()->unsigned(),
            'quantity' => $this->integer(),
            'sess_id' => $this->string(),
        ]);
        $this->createIndex("idx_cart_user_id", "cart", "user_id");
        $this->createIndex("idx_cart_product_id", "cart", "product_id");


        $this->addForeignKey("fk_cart_user_id", "cart", "user_id", "user", "id", "cascade", "cascade");
        $this->addForeignKey("fk_cart_product_id", "cart", "product_id", "product", "id", "cascade", "cascade");
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown(): void
    {
        $this->dropTable('{{%cart}}');
    }
}
