<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%order_request}}`.
 */
class m240130_082544_create_order_request_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp(): void
    {
        $this->createTable('{{%order_request}}', [
            'id' => $this->primaryKey(),
            'type' => $this->integer(),
            'order_id' => $this->integer(),
            'price' => $this->bigInteger()->unsigned(),
            'company_id' => $this->integer(),
            'is_winner' => $this->integer(),
            'status' => $this->integer(),
            'created_at' => $this->dateTime(),
            'updated_at' => $this->dateTime(),
            'deleted_at' => $this->dateTime(),
        ]);

        $this->createIndex(
            'idx-order_request-order_id',
            'order_request',
            'order_id'
        );
        $this->addForeignKey(
            'fk-order_request-order_id',
            'order_request',
            'order_id',
            'order',
            'id',
            'CASCADE',
            'CASCADE',
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown(): void
    {
        $this->dropTable('{{%order_request}}');
    }
}
