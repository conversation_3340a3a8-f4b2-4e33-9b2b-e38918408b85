<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%order_list}}`.
 */
class m240130_082847_create_order_list_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%order_list}}', [
            'id' => $this->primaryKey(),
            'order_id' => $this->integer(),
            'product_id' => $this->integer(),
            'quantity' => $this->integer(),
            'price' => $this->bigInteger()->unsigned(),
            'cache' => $this->text(),
        ]);
        $this->createIndex("idx_order_list_order_id", "order_list", "order_id");
        $this->createIndex("idx_order_list_product_id", "order_list", "product_id");


        $this->addForeignKey("fk_order_list_order_id", "order_list", "order_id", "order", "id", "cascade", "cascade");
        $this->addForeign<PERSON>ey("fk_order_list_product_id", "order_list", "product_id", "product", "id", "cascade", "cascade");
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%order_list}}');
    }
}
