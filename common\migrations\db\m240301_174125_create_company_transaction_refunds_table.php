<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%company_transaction_refunds}}`.
 */
class m240301_174125_create_company_transaction_refunds_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp(): void
    {
        $this->createTable('{{%company_transaction_refunds}}', [
            'id' => $this->primaryKey(),
            'bank_account_id' => $this->integer(),
            'account_number' => $this->string(),
            'company_id' => $this->integer(),
            'sum' => $this->bigInteger()->unsigned(),
            'transaction_id' => $this->integer(),
            'status' => $this->integer(),
            'comment' => $this->text(),
            'created_at' => $this->dateTime(),
            'created_by' => $this->integer(),
        ]);

        $this->createIndex(
            'idx-company_transaction_refunds-bank_account_id',
            'company_transaction_refunds',
            'bank_account_id'
        );
        $this->addForeignKey(
            'fk-company_transaction_refunds-bank_account_id',
            'company_transaction_refunds',
            'bank_account_id',
            'company_bank_account',
            'id',
            'SET NULL',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown(): void
    {
        $this->dropTable('{{%company_transaction_refunds}}');
    }
}
