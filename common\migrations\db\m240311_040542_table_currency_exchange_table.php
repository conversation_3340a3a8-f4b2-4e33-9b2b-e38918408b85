<?php

use yii\db\Migration;

/**
 * Class m240311_040542_table_currency_exchange_table
 */
class m240311_040542_table_currency_exchange_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%currency_exchange}}', [
            'id' => $this->primaryKey(),
            'code' => $this->string(50),
            'name' => $this->string(50),
            'value' => $this->bigInteger()->unsigned(),
            'created_date' => $this->date(),
            'created_at' => $this->dateTime(),
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%currency_exchange}}');
    }
}
