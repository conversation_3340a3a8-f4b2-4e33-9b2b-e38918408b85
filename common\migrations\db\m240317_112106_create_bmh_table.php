<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%bmh}}`.
 */
class m240317_112106_create_bmh_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%bmh}}', [
            'id' => $this->primaryKey(),
            'price' => $this->bigInteger()->unsigned(),
            'start_date' => $this->date(),
            'end_date' => $this->date(),
            'created_by' => $this->integer(),
            'created_at' => $this->dateTime(),
            'updated_by' => $this->integer(),
            'updated_at' => $this->dateTime(),
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%bmh}}');
    }
}
