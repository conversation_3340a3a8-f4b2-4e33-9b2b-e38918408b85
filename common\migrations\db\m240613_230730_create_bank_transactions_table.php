<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%bank_transactions}}`.
 */
class m240613_230730_create_bank_transactions_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%bank_transactions}}', [
            'id' => $this->primaryKey(),
            'trans_number' => $this->String(255),
            'debit' => $this->bigInteger()->unsigned(),
            'credit' => $this->bigInteger()->unsigned(),
            'description' => $this->String(255),
            'type' => $this->integer(),
            'company_id' => $this->integer(),
            'created_at' => $this->dateTime(),
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%bank_transactions}}');
    }
}
