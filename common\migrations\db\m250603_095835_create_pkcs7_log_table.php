<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%pkcs7_log}}`.
 */
class m250603_095835_create_pkcs7_log_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp(): void
    {
        $this->createTable('{{%pkcs7_log}}', [
            'id' => $this->primaryKey(),
            'object_id' => $this->integer(),
            'pksc7' => $this->text(),
            'json_body' =>$this->text(),
            'user_id' => $this->integer(),
            'pkcs7_type' => $this->string(),
            'company_tin' => $this->integer(),
            'created_at' => $this->dateTime(),
            'update_at' => $this->dateTime(),
            'deleted_at' => $this->dateTime(),
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown(): void
    {
        $this->dropTable('{{%pkcs7_log}}');
    }
}
