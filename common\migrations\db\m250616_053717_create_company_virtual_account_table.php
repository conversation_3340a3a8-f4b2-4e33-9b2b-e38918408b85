<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%company_virtual_account}}`.
 */
class m250616_053717_create_company_virtual_account_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function up()
    {
        $this->createTable('{{%company_virtual_account}}', [
            'id' => $this->primaryKey(),
            'company_id' => $this->integer(),
            'organ' => $this->string(11),
            'prefix_account' => $this->string(), // account prefix
            'organization_type' => $this->smallInteger()->unsigned(), // organization type
            'price' => $this->bigInteger()->unsigned(),
            'account' => $this->string(27),
            'order' => $this->integer(),
            'status' => $this->integer(),
            'created_at' => $this->dateTime(),
            'updated_at' => $this->dateTime(),
            'deleted_at' => $this->dateTime(),
            'created_by' => $this->integer(),
            'updated_by' => $this->integer(),
        ]);


        $this->createIndex(
            'idx-company_virtual_account-company_id',
            'company_virtual_account',
            'company_id',
        );

        $this->addForeignKey(
            'fk-company_virtual_account-company_id',
            'company_virtual_account',
            'company_id',
            'company',
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->createIndex(
            'idx-company_virtual_account-prefix_account',
            'company_virtual_account',
            'prefix_account',
        );

        $this->createIndex(
            'idx-company_virtual_account-account',
            'company_virtual_account',
            'account',
        );
    }

    /**
     * {@inheritdoc}
     */
    public function down()
    {
        $this->dropTable('{{%company_virtual_account}}');
    }
}
