<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%virtual_transaction}}`.
 */
class m250616_105211_create_virtual_transaction_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%virtual_transaction}}', [
            'id' => $this->primaryKey(),
            'debit_company_id' => $this->integer(),
            'credit_company_id' => $this->integer(),
            'tender_id' => $this->integer(),
            'auction_id' => $this->integer(),
            'order_id' => $this->integer(),
            'product_id' => $this->integer(),
            'contract_id' => $this->integer(),
            'debit_company_tin' => $this->string(14),
            'credit_company_tin' => $this->string(14),
            'debit_account_id' => $this->integer(),
            'debit_account' => $this->string(25),
            'credit_account_id' => $this->integer(),
            'credit_account' => $this->string(25),
            'prefix_account' => $this->string(),
            'description' => $this->string(),
            'operation_type' => $this->string(),
            'credit' => $this->bigInteger()->unsigned(),
            'debit' => $this->bigInteger()->unsigned(),
            'parent_id' => $this->integer(),
            'debit_id' => $this->integer(),
            'bank_transaction_id' => $this->integer()->unique(),
            'bank_transaction_out_id' => $this->integer()->unique(),
            'created_at' => $this->dateTime(),
            'updated_at' => $this->dateTime(),
            'deleted_at' => $this->dateTime(),
            'created_by' => $this->integer(),
            'updated_by' => $this->integer(),
        ]);
        $this->createIndex(
            'idx-virtual_transaction-debit_company_id',
            '{{%virtual_transaction}}',
            'debit_company_id'
        );

        $this->addForeignKey(
            'fk-virtual_transaction-debit_company_id',
            '{{%virtual_transaction}}',
            'debit_company_id',
            '{{%company}}',
            'id',
            'SET NULL',
            'CASCADE'
        );

        $this->createIndex(
            'idx-virtual_transaction-credit_company_id',
            '{{%virtual_transaction}}',
            'credit_company_id'
        );

        $this->addForeignKey(
            'fk-virtual_transaction-credit_company_id',
            '{{%virtual_transaction}}',
            'credit_company_id',
            '{{%company}}',
            'id',
            'SET NULL',
            'CASCADE'
        );

        $this->createIndex(
            'idx-virtual_transaction-debit_account_id',
            'virtual_transaction',
            'debit_account_id'
        );

        $this->addForeignKey(
            'fk-virtual_transaction-debit_account_id',
            'virtual_transaction',
            'debit_account_id',
            'company_virtual_account',
            'id',
            'SET NULL',
            'CASCADE'
        );

        $this->createIndex(
            'idx-virtual_transaction-credit_account_id',
            'virtual_transaction',
            'credit_account_id'
        );

        $this->addForeignKey(
            'fk-virtual_transaction-credit_account_id',
            'virtual_transaction',
            'credit_account_id',
            'company_virtual_account',
            'id',
            'SET NULL',
            'CASCADE'
        );

        $this->createIndex(
            'idx-virtual_transaction-order_id',
            'virtual_transaction',
            'order_id'
        );

        $this->createIndex(
            'idx-virtual_transaction-product_id',
            'virtual_transaction',
            'product_id'
        );

        $this->createIndex(
            'idx-virtual_transaction-tender_id',
            'virtual_transaction',
            'tender_id'
        );

        $this->createIndex(
            'idx-virtual_transaction-auction_id',
            'virtual_transaction',
            'auction_id'
        );

        $this->createIndex(
            'idx-virtual_transaction-contract_id',
            'virtual_transaction',
            'contract_id'
        );

        $this->createIndex(
            'idx-virtual_transaction-prefix_account',
            'virtual_transaction',
            'prefix_account'
        );

    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%virtual_transaction}}');
    }
}
