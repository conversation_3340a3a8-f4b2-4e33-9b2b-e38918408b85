<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%saldo_daily}}`.
 */
class m250620_120003_create_saldo_daily_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%saldo_daily}}', [
            'id' => $this->primaryKey(),
            "saldo_date" => $this->date(),
            "company_tin" => $this->string(9),
            "debit" => $this->bigInteger()->unsigned(),
            "credit" => $this->bigInteger()->unsigned(),
            "company_virtual_account_id" => $this->integer(),
            "prefix_account" => $this->string(27),
            "account" => $this->string(27),
            "price_start" => $this->bigInteger()->unsigned(),
            "price_end" => $this->bigInteger()->unsigned(),
            "begin_time" => $this->dateTime(),
            "end_time" => $this->dateTime(),
            "begin_job_time" => $this->dateTime(),
            "end_job_time" => $this->dateTime(),
            "created_at" => $this->dateTime(),
            "updated_at" => $this->dateTime(),
            "deleted_at" => $this->dateTime(),
            "created_by" => $this->integer(),
            "updated_by" => $this->integer(),
            "deleted_by" => $this->integer(),
        ]);

        $this->createIndex(
            "idx-saldo_daily-company_virtual_account_id",
            "{{%saldo_daily}}",
            "company_virtual_account_id"
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%saldo_daily}}');
    }
}
