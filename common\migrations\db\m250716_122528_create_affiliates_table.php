<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%affiliates}}`.
 */
class m250716_122528_create_affiliates_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%affiliates}}', [
            'id' => $this->primaryKey(),
            'company_id' => $this->integer(),
            'company_tin' => $this->string(9),
            'pinfl' => $this->string(14),
            'full_name' => $this->string(255),
            'created_at' => $this->dateTime(),
            'updated_at' => $this->dateTime(),
            'deleted_at' => $this->dateTime(),
            'created_by' => $this->integer(),
            'updated_by' => $this->integer(),
            'deleted_by' => $this->integer(),
        ]);

        $this->createIndex(
            'idx-affiliates-company_id',
            'affiliates',
            'company_id',
        );
        $this->addForeignKey(
            'fk-affiliates-company_id',
            'affiliates',
            'company_id',
            'company',
            'id',
            'CASCADE',
            'CASCADE',
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%affiliates}}');
    }
}
