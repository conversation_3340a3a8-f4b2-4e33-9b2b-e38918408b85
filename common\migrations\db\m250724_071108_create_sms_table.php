<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%sms}}`.
 */
class m250724_071108_create_sms_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp(): void
    {
        $this->createTable('{{%sms}}', [
            'id' => $this->primaryKey(),
            'user_id' => $this->integer(),
            'phone' => $this->string(50)->notNull(),
            'code' => $this->string()->notNull(),
            'message' => $this->string(),
            'status' => $this->integer(),
            'failed_attempt' => $this->integer(),
            'last_failed_attempt' => $this->integer(),
            'created_at' => $this->integer(),
            'updated_at' => $this->integer(),
        ]);

        $this->createIndex(
            'idx-sms-user_id',
            '{{%sms}}',
            'user_id'
        );
        $this->addForeignKey(
            'fk-sms-user_id',
            '{{%sms}}',
            'user_id',
            '{{user}}',
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->createIndex(
            'idx-sms-created_at',
            '{{%sms}}',
            'created_at'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown(): void
    {
        $this->dropTable('{{%sms}}');
    }
}
