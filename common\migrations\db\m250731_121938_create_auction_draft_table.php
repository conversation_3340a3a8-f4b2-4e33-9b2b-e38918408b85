<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%auction_draft}}`.
 */
class m250731_121938_create_auction_draft_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp(): void
    {
        $this->createTable('{{%auction_draft}}', [
            'id' => $this->primaryKey(),
            'title' => $this->string(),
            'plan_schedule_id' => $this->integer(),
            'company_id' => $this->integer(),
            'organ' => $this->string(11),
            'account' => $this->string(27),
            'total_sum' => $this->bigInteger()->unsigned(),
            'delivery_period' => $this->integer(),
            'payment_period' => $this->integer(),
            'region_id' => $this->integer(),
            'district_id' => $this->integer(),
            'address' => $this->string(),
            'delivery_basis' => $this->string(10),
            'description' => $this->text(),
            'organization_phone' => $this->string(255),
            'created_at' => $this->dateTime(),
            'updated_at' => $this->dateTime(),
            'deleted_at' => $this->dateTime(),
            'created_by' => $this->integer(),
            'updated_by' => $this->integer(),
            'deleted_by' => $this->integer(),
        ]);
        $this->createIndex('idx-auction_draft-title', 'auction_draft', 'title');
        $this->createIndex('idx-plan_schedule_id-auction_draft', 'auction_draft', 'plan_schedule_id');
        $this->addForeignKey(
            'fk-plan_schedule_id-auction',
            'auction_draft',
            'plan_schedule_id',
            'plan_schedule',
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->createIndex("idx-auction_draft-company_id", 'auction_draft', 'company_id');
        $this->createIndex("idx-auction_draft-region_id", 'auction_draft', 'region_id');
        $this->createIndex("idx-auction_draft-district_id", 'auction_draft', 'district_id');
        $this->addForeignKey(
            'fk-auction_draft-company_id',
            'auction_draft',
            'company_id',
            'company',
            'id',
            'CASCADE',
            'CASCADE'
        );
        $this->addForeignKey(
            'fk-auction_draft-region_id',
            'auction_draft',
            'region_id',
            'region',
            'id',
            'CASCADE',
            'CASCADE'
        );
        $this->addForeignKey(
            'fk-auction_draft-district_id',
            'auction_draft',
            'district_id',
            'region',
            'id',
            'CASCADE',
            'CASCADE'
        );
        $this->createTable('{{%auction_classifier_draft}}', [
            'id' => $this->primaryKey(),
            'auction_id' => $this->integer(),
            'plan_schedule_id' => $this->integer(),
            'plan_schedule_classifier_id' => $this->integer(),
            'classifier_category_id' => $this->integer(),
            'classifier_id' => $this->integer(),
            'quantity' => $this->integer(),
            'unit_id' => $this->integer(),
            'order' => $this->integer(),
            'price' => $this->bigInteger()->unsigned(),
            'total_sum' => $this->bigInteger()->unsigned(),
            'description' => $this->text(),
        ]);

        $this->createIndex(
            'idx-auction_classifier_draft-auction_id',
            'auction_classifier_draft',
            'auction_id',
        );

        $this->createIndex(
            'idx-auction_classifier_draft-classifier_category_id',
            'auction_classifier_draft',
            'classifier_category_id'
        );

        $this->createIndex(
            'idx-auction_classifier_draft-classifier_id',
            'auction_classifier_draft',
            'classifier_id',
        );

        // Create foreign key for 'unit_id'
        $this->createIndex(
            'idx-auction_classifier_draft-unit_id',
            'auction_classifier_draft',
            'unit_id',
        );

        $this->addForeignKey(
            'fk-auction_classifier_draft-auction_id',
            'auction_classifier_draft',
            'auction_id',
            'auction_draft',
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-auction_classifier_draft-classifier_category_id',
            'auction_classifier_draft',
            'classifier_category_id',
            'classifier_category',
            'id',
            'CASCADE',
            'CASCADE',
        );

        $this->addForeignKey(
            'fk-auction_classifier_draft-classifier_id',
            'auction_classifier_draft',
            'classifier_id',
            'classifier',
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-auction_classifier_draft-unit_id',
            'auction_classifier_draft',
            'unit_id',
            'unit',
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->createTable('{{%auction_file_draft}}', [
            'id' => $this->primaryKey(),
            'auction_id' => $this->integer(),
            'file_id' => $this->integer(),
        ]);

        $this->createIndex('idx-auction_file_draft-auction_id', 'auction_file_draft', 'auction_id');
        $this->createIndex('idx-auction_file_draft-file_id', 'auction_file_draft', 'file_id');

        $this->addForeignKey(
            'fk-auction_file_draft-auction_id',
            'auction_file_draft',
            'auction_id',
            'auction_draft',
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-auction_file_draft-file_id',
            'auction_file_draft',
            'file_id',
            'file',
            'id',
            'CASCADE',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown(): void
    {
        $this->dropTable('{{%auction_file_draft}}');
        $this->dropTable('{{%auction_classifier_draft}}');
        $this->dropTable('{{%auction_draft}}');
    }
}
