<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%plan_schedule_file}}`.
 */
class m250801_092832_create_plan_schedule_file_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%plan_schedule_file}}', [
            'id' => $this->primaryKey(),
            'company_id' => $this->integer(),
            'plan_schedule_id' => $this->integer(),
            'file_id' => $this->integer(),
            "created_at" => $this->dateTime(),
            "updated_at" => $this->dateTime(),
            "deleted_at" => $this->dateTime(),
            "created_by" => $this->integer(),
            "updated_by" => $this->integer(),
            "deleted_by" => $this->integer(),
        ]);

        // Add foreign key for file_id
        $this->addForeignKey(
            'fk-plan_schedule_file-file_id',
            '{{%plan_schedule_file}}',
            'file_id',
            '{{%file}}',
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-plan_schedule_file-company_id',
            '{{%plan_schedule_file}}',
            'company_id',
            '{{%company}}',
            'id',
            'CASCADE',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey('fk-plan_schedule_file-file_id', '{{%plan_schedule_file}}');
        $this->dropForeignKey('fk-plan_schedule_file-company_id', '{{%plan_schedule_file}}');

        $this->dropTable('{{%plan_schedule_file}}');
    }
}
