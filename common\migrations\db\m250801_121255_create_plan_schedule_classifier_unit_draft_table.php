<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%plan_schedule_classifier_unit_draft}}`.
 */
class m250801_121255_create_plan_schedule_classifier_unit_draft_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp(): void
    {
        $this->createTable('{{%plan_schedule_classifier_unit_draft}}', [
            'id' => $this->primaryKey(),
            'plan_schedule_classifier_id' => $this->integer(),
            'classifier_property_id' => $this->integer(),
            'status' => $this->integer(),
        ]);

        $this->createIndex(
            'idx-multiple-plan_schedule_classifier_unit_draft',
            'plan_schedule_classifier_unit_draft',
            ['plan_schedule_classifier_id', 'classifier_property_id', 'status']
        );

        $this->addForeignKey(
            'fk-plan_schedule_classifier_unit_draft-plan_schedule_classifier_id',
            'plan_schedule_classifier_unit_draft',
            'plan_schedule_classifier_id',
            'plan_schedule_classifier',
            'id',
            'CASCADE',
            'CASCADE',
        );

        $this->addForeignKey(
            'fk-plan_schedule_classifier_unit_draft-classifier_property_id',
            'plan_schedule_classifier_unit_draft',
            'classifier_property_id',
            'classifier_properties',
            'id',
            'CASCADE',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown(): void
    {
        $this->dropTable('{{%plan_schedule_classifier_unit_draft}}');
    }
}
