<?php

use yii\db\Migration;

class m250807_053314_add_prefix_account_status_table_virtual_company_account extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('company_virtual_account' , 'prefix_account_status' ,   $this->integer()->after('prefix_account'));
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('company_virtual_account', 'prefix_account_status');
    }

}
