<?php

namespace common\models;

use common\components\ActiveRecordMeta;
use common\traits\NotDeleted;
use common\traits\SoftDelete;
use Yii;

/**
 * This is the model class for table "affiliates".
 *
 * @property int $id
 * @property int|null $company_id
 * @property string|null $company_tin
 * @property string|null $pinfl
 * @property string|null $full_name
 * @property string|null $created_at
 * @property string|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property int|null $deleted_by
 */
class Affiliates extends ActiveRecordMeta
{
    use SoftDelete;
    use NotDeleted;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'affiliates';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['company_id', 'company_tin', 'pinfl', 'full_name', 'created_at', 'updated_at', 'deleted_at', 'created_by', 'updated_by', 'deleted_by'], 'default', 'value' => null],
            [['company_id', 'created_by', 'updated_by', 'deleted_by'], 'default', 'value' => null],
            [['company_id', 'created_by', 'updated_by', 'deleted_by'], 'integer'],
            [['created_at', 'updated_at', 'deleted_at'], 'safe'],
            [['company_tin'], 'string', 'max' => 9],
            [['pinfl'], 'string', 'max' => 14],
            [['full_name'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('app', 'ID'),
            'company_id' => Yii::t('app', 'Company ID'),
            'company_tin' => Yii::t('app', 'Company Tin'),
            'pinfl' => Yii::t('app', 'Pinfl'),
            'full_name' => Yii::t('app', 'Full Name'),
            'created_at' => Yii::t('app', 'Created At'),
            'updated_at' => Yii::t('app', 'Updated At'),
            'deleted_at' => Yii::t('app', 'Deleted At'),
            'created_by' => Yii::t('app', 'Created By'),
            'updated_by' => Yii::t('app', 'Updated By'),
            'deleted_by' => Yii::t('app', 'Deleted By'),
        ];
    }

}
