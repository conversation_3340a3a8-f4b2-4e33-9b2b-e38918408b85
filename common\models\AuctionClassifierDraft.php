<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "auction_classifier_draft".
 *
 * @property int $id
 * @property int|null $auction_id
 * @property int|null $plan_schedule_id
 * @property int|null $plan_schedule_classifier_id
 * @property int|null $classifier_id
 * @property int|null $classifier_category_id
 * @property int|null $quantity
 * @property int|null $unit_id
 * @property int|null $price
 * @property int|null $order
 * @property int|null $total_sum
 * @property string|null $description
 *
 * @property AuctionDraft $auction
 * @property Classifier $classifier
 * @property Unit $unit
 */
class AuctionClassifierDraft extends \yii\db\ActiveRecord
{


    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'auction_classifier_draft';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['auction_id', 'plan_schedule_id', 'plan_schedule_classifier_id', 'classifier_id',  'classifier_category_id', 'quantity', 'unit_id', 'price', 'total_sum', 'description', 'order'], 'default', 'value' => null],
            [['auction_id', 'plan_schedule_id', 'plan_schedule_classifier_id', 'classifier_id',  'classifier_category_id', 'quantity', 'unit_id', 'price', 'total_sum',], 'default', 'value' => null],
            [['auction_id', 'plan_schedule_id', 'plan_schedule_classifier_id', 'classifier_id',   'classifier_category_id', 'unit_id', 'price', 'total_sum',], 'integer'],
            [['description'], 'string'],
            [['auction_id'], 'exist', 'skipOnError' => true, 'targetClass' => AuctionDraft::class, 'targetAttribute' => ['auction_id' => 'id']],
            [['classifier_id'], 'exist', 'skipOnError' => true, 'targetClass' => Classifier::class, 'targetAttribute' => ['classifier_id' => 'id']],
            [['classifier_category_id'], 'exist', 'skipOnError' => true, 'targetClass' => ClassifierCategory::class, 'targetAttribute' => ['classifier_category_id' => 'id']],
            [['unit_id'], 'exist', 'skipOnError' => true, 'targetClass' => Unit::class, 'targetAttribute' => ['unit_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'auction_id' => 'Auction ID',
            'plan_schedule_id' => 'Plan Schedule ID',
            'plan_schedule_classifier_id' => 'Plan Schedule Classifier ID',
            'classifier_id' => 'Classifier ID',
            'quantity' => 'Quantity',
            'unit_id' => 'Unit ID',
            'price' => 'Price',
            'total_sum' => 'Total Sum',
            'description' => 'Description',
        ];
    }

    /**
     * Gets query for [[Auction]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getAuction()
    {
        return $this->hasOne(AuctionDraft::class, ['id' => 'auction_id']);
    }

    /**
     * Gets query for [[Classifier]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getClassifier()
    {
        return $this->hasOne(Classifier::class, ['id' => 'classifier_id']);
    }

    /**
     * Gets query for [[Unit]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUnit()
    {
        return $this->hasOne(Unit::class, ['id' => 'unit_id']);
    }

}
