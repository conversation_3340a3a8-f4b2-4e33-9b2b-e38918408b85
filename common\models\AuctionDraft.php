<?php

namespace common\models;

use api\modules\auth\resources\CompanyResource;
use common\components\ActiveRecordMeta;
use common\traits\SoftDelete;
use yii\db\ActiveQuery;

/**
 * This is the model class for table "auction_draft".
 *
 * @property int $id
 * @property string $title
 * @property int|null $plan_schedule_id
 * @property int|null $company_id
 * @property string|null $organ
 * @property string|null $account
 * @property int|null $total_sum
 * @property int|null $delivery_period
 * @property int|null $payment_period
 * @property int|null $region_id
 * @property int|null $district_id
 * @property string|null $address
 * @property string|null $delivery_basis
 * @property string $description
 * @property string|null $organization_phone
 *
 * @property AuctionClassifierDraft[] $auctionClassifierDrafts
 * @property AuctionFileDraft[] $auctionFileDrafts
 * @property Company $company
 * @property Region $region
 */
class AuctionDraft extends ActiveRecordMeta
{
    use SoftDelete;
    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return 'auction_draft';
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [['title', 'plan_schedule_id', 'company_id', 'organ',  'total_sum','delivery_period', 'payment_period', 'region_id',  'district_id', 'address',  'delivery_basis',  'description',  'organization_phone'], 'default', 'value' => null],
            [['company_id', 'total_sum',  'delivery_period', 'payment_period', 'region_id', 'district_id',], 'default', 'value' => null],
            [['company_id', 'total_sum', 'delivery_period', 'payment_period', 'region_id', 'district_id',], 'integer'],
            [['auction_end', 'cancel_date', 'payment_date',], 'safe'],
            [['organ'], 'string', 'max' => 11],
            [['address','organization_phone', 'title'], 'string', 'max' => 255],
            [['delivery_basis'], 'string', 'max' => 10],
            [['plan_schedule_id'], 'exist', 'skipOnError' => true, 'targetClass' => PlanSchedule::class, 'targetAttribute' => ['plan_schedule_id' => 'id']],
            [['company_id'], 'exist', 'skipOnError' => true, 'targetClass' => Company::class, 'targetAttribute' => ['company_id' => 'id']],
            [['region_id'], 'exist', 'skipOnError' => true, 'targetClass' => Region::class, 'targetAttribute' => ['region_id' => 'id'], 'filter' => ['type' => Region::TYPE_REGION]],
            [['district_id'], 'exist', 'skipOnError' => true, 'targetClass' => Region::class, 'targetAttribute' => ['district_id' => 'id'], 'filter' => ['type' => Region::TYPE_DISTRICT]],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return [
            'id' => 'ID',
            'company_id' => 'Company ID',
            'total_sum' => 'Total Sum',
            'delivery_period' => 'Delivery Period',
            'payment_period' => 'Payment Period',
            'region_id' => 'Region ID',
            'district_id' => 'District ID',
            'address' => 'Address',
            'delivery_basis' => 'Delivery Basis',
            'description' => 'Description',
            'organization_phone' => 'Responsible Person Phone',
        ];
    }

    /**
     * Gets query for [[AuctionClassifierDrafts]].
     *
     * @return ActiveQuery
     */
    public function getAuctionClassifierDrafts()
    {
        return $this->hasMany(AuctionClassifierDraft::class, ['auction_id' => 'id']);
    }

    /**
     * Gets query for [[AuctionFileDrafts]].
     *
     * @return ActiveQuery
     */
    public function getAuctionFileDrafts()
    {
        return $this->hasMany(AuctionFileDraft::class, ['auction_id' => 'id']);
    }

    /**
     * Gets query for [[Company]].
     *
     * @return ActiveQuery
     */
    public function getCompany()
    {
        return $this->hasOne(CompanyResource::class, ['id' => 'company_id']);
    }

    /**
     * Gets query for [[Region]].
     *
     * @return ActiveQuery
     */
    public function getRegion()
    {
        return $this->hasOne(Region::class, ['id' => 'region_id']);
    }

}
