<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "auction_file_draft".
 *
 * @property int $id
 * @property int|null $auction_id
 * @property int|null $file_id
 *
 * @property AuctionDraft $auction
 * @property File $file
 */
class AuctionFileDraft extends \yii\db\ActiveRecord
{


    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'auction_file_draft';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['auction_id', 'file_id',], 'default', 'value' => null],
            [['auction_id', 'file_id',], 'default', 'value' => null],
            [['auction_id', 'file_id',], 'integer'],
            [['auction_id'], 'exist', 'skipOnError' => true, 'targetClass' => AuctionDraft::class, 'targetAttribute' => ['auction_id' => 'id']],
            [['file_id'], 'exist', 'skipOnError' => true, 'targetClass' => File::class, 'targetAttribute' => ['file_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'auction_id' => 'Auction ID',
            'file_id' => 'File ID',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'deleted_at' => 'Deleted At',
            'created_by' => 'Created By',
            'updated_by' => 'Updated By',
        ];
    }

    /**
     * Gets query for [[Auction]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getAuction()
    {
        return $this->hasOne(AuctionDraft::class, ['id' => 'auction_id']);
    }

    /**
     * Gets query for [[File]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getFile()
    {
        return $this->hasOne(File::class, ['id' => 'file_id']);
    }

}
