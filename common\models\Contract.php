<?php

namespace common\models;

use api\modules\shop\resources\ContractCancelRequestResource;
use common\components\ActiveRecordMeta;
use common\enums\ContractEnum;
use common\models\auction\Auction;
use common\models\shop\Order;
use common\traits\FindOrFail;
use Yii;
use yii\helpers\ArrayHelper;

/**
 * This is the model class for table "contract".
 *
 * @property integer $id
 * @property string|null $number
 * @property integer $customer_id
 * @property integer $producer_id
 * @property integer $auction_id
 * @property integer $tender_id
 * @property integer $order_id
 * @property number $price
 * @property number $need_price
 * @property integer $customer_signed
 * @property integer $producer_signed
 * @property integer $status
 * @property string $customer_sign_date
 * @property string $producer_sign_date
 * @property string $customer_pay_date
 * @property string $customer_mark_delivered_date
 * @property string $customer_cancel_date
 * @property string $producer_cancel_date
 * @property string $created_at
 * @property string $updated_at
 * @property string $deleted_at
 * @property string $payment_last_date
 * @property integer $created_by
 * @property integer $updated_by
 * @property integer $facture_file_id
 * @property integer $reserve
 * @property string $procedure_type
 *
 * @property Company $producer
 * @property Auction $auction
 * @property Tender $tender
 * @property Order $order
 * @property User $createdBy
 * @property User $updatedBy
 * @property Company $customer
 */
class Contract extends ActiveRecordMeta
{
    use FindOrFail;

    const TYPE_ESHOP = 'E-Shop';
    const TYPE_AUCTION = 'Auction';
    const TYPE_TENDER = 'Tender';

    const TYPE_ESHOP_PREFIX = 'e';
    const TYPE_AUCTION_PREFIX = 'a';
    const TYPE_TENDER_PREFIX = 't';

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'contract';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['customer_id', 'producer_id', 'price', 'status'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [['customer_id', 'producer_id', 'auction_id', 'tender_id', 'file_id', 'order_id', 'customer_signed', 'producer_signed', 'status', 'reserve'], 'integer'],
            [['price'], 'number'],
            [[
                'created_at', 'updated_at', 'payment_last_date', 'number',
                'customer_sign_date', 'producer_sign_date', 'customer_pay_date', 'customer_mark_delivered_date', 'customer_cancel_date', 'producer_cancel_date', 'facture_file_id'
            ], 'safe'],
            [['auction_id'], 'exist', 'skipOnError' => true, 'targetClass' => Auction::class, 'targetAttribute' => ['auction_id' => 'id']],
            [['order_id'], 'exist', 'skipOnError' => true, 'targetClass' => Order::class, 'targetAttribute' => ['order_id' => 'id']],
            [['tender_id'], 'exist', 'skipOnError' => true, 'targetClass' => Tender::class, 'targetAttribute' => ['tender_id' => 'id']],
            [['customer_id'], 'exist', 'skipOnError' => true, 'targetClass' => Company::class, 'targetAttribute' => ['customer_id' => 'id']],
            [['producer_id'], 'exist', 'skipOnError' => true, 'targetClass' => Company::class, 'targetAttribute' => ['producer_id' => 'id']],
            [['file_id'], 'exist', 'skipOnError' => true, 'targetClass' => File::class, 'targetAttribute' => ['file_id' => 'id']],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'customer_id' => 'Customer ID',
            'producer_id' => 'Producer ID',
            'price' => 'Price',
            'customer_signed' => 'Customer Signed',
            'producer_signed' => 'Producer Signed',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    public function getStateName()
    {
        return ArrayHelper::getValue(
            [
                ContractEnum::STATUS_SIGNED => \Yii::t('app', "To'lov kutilmoqda"),
                ContractEnum::STATUS_PAYMENT_END => \Yii::t('app', 'To`lov qilingan'),
                ContractEnum::STATUS_DONE => \Yii::t('app', 'Yetkazib berilgan'),
                ContractEnum::STATUS_CANCEL_PROCESS => \Yii::t('app', 'Bekor qilish jarayonida'),
                ContractEnum::STATUS_CANCEL => \Yii::t('app', 'Bekor qilingan'),
                ContractEnum::STATUS_RETURN_SIGNED => \Yii::t('app', 'Qayta imzolangan'),
                ContractEnum::STATUS_DISCARD => \Yii::t('app', 'Rad qilingan'),
                ContractEnum::STATUS_WAITING_RESERVE => \Yii::t('app', "Zaxira g'olib kutilmoqda"),
                ContractEnum::STATUS_CANCEL_BY_SYSTEM => \Yii::t('app', "Tizim tomonidan bekor qilindi"),
            ],
            $this->status);

    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getAuction()
    {
        return $this->hasOne(Auction::class, ['id' => 'auction_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getCustomer()
    {
        return $this->hasOne(Company::class, ['id' => 'customer_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getProducer()
    {
        return $this->hasOne(Company::class, ['id' => 'producer_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getContractCancelRequest()
    {
        return $this->hasMany(ContractCancelRequestResource::class, ['contract_id' => 'id'])->andWhere(['status' => ContractEnum::STATUS_REQUEST_NEW])->orderBy(['id' => SORT_DESC]);
    }

    public function beforeSave($insert)
    {
        if ($this->isNewRecord) {
            if ($this->tender_id == null) {
                $this->status = ContractEnum::STATUS_SIGNED;
            }
            $this->producer_signed = 1;
            $this->customer_signed = 1;
        }

//    if ($this->status == self::STATUS_CREATED && $this->customer_signed == 1 && $this->producer_signed == 1) {
//      $this->status = self::STATUS_WAITING_PAYMENT_FROM_CUSTOMER;
//    }

        return parent::beforeSave($insert);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getOrder()
    {
        return $this->hasOne(Order::class, ['id' => 'order_id']);
    }

    public function getTender()
    {
        return $this->hasOne(Tender::class, ['id' => 'tender_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFile()
    {
        return $this->hasOne(File::class, ['id' => 'file_id']);
    }

    public function getType()
    {
        if ($this->auction_id) {
            return self::TYPE_AUCTION;
        }
        if ($this->order_id) {
            return self::TYPE_ESHOP;
        }
        if ($this->tender_id) {
            return self::TYPE_TENDER;
        }

    }

    public function getPredmet()
    {
        switch ($this->type) {
            case self::TYPE_ESHOP:
            {
                return $this->order->product->title . " (" . $this->order->count . ' ' . $this->order->product->unit->title_uz . ')';
            }
            case self::TYPE_TENDER:
            {
                return $this->tender->title;
            }
            case self::TYPE_AUCTION:
            {
                return $this->auction->lot;
            }
        }
    }

    public function getStartSum()
    {
        switch ($this->type) {
            case self::TYPE_ESHOP:
            {
                return $this->order->orderStartSum->price;
            }
            case self::TYPE_AUCTION:
            {
                return $this->auction->auctionStartSum->price;
            }
        }

    }

    public function getProductSum()
    {
        switch ($this->type) {
            case self::TYPE_ESHOP:
            {
                return $this->order->product->price;
            }
            case self::TYPE_AUCTION:
            {
                return $this->auction->total_sum;
            }
        }

    }
}
