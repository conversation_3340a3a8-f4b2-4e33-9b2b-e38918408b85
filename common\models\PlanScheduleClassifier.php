<?php

namespace common\models;

use Yii;
use common\components\ActiveRecordMeta;

/**
 * This is the model class for table "plan_schedule_classifier".
 *
 * @property int $id
 * @property int|null $plan_schedule_id
 * @property int|null $classifier_id
 * @property int|null $status
 * @property int|null $enabled
 * @property string|null $created_at
 * @property string|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property string|null $description
 * @property int|null $year
 * @property int|null $month
 * @property int|null $tovarprice
 * @property int|null $summa
 * @property int|null $count
 * @property int|null $count_live tovar qanchasi tenderdan yutilgani
 * @property int|null $count_used
 * @property int|null $source_of_funding
 * @property int|null $quarter
 *
 * @property Classifier $classifier
 * @property PlanSchedule $planSchedule
 */
class PlanScheduleClassifier extends ActiveRecordMeta
{
    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return 'plan_schedule_classifier';
    }

    //    /**
    //     * {@inheritdoc}
    //     */
    //    public function rules()
    //    {
    //        return [
    //            [['plan_schedule_id', 'classifier_id', 'status', 'enabled', 'created_by', 'updated_by', 'unit_id', 'year', 'month', 'count', 'count_live', 'count_used'], 'integer'],
    //            [['created_at', 'updated_at', 'deleted_at'], 'safe'],
    //            [['description'], 'string', 'max' => 255],
    //            [['classifier_id'], 'exist', 'skipOnError' => true, 'targetClass' => Classifier::class, 'targetAttribute' => ['classifier_id' => 'id']],
    //            [['plan_schedule_id'], 'exist', 'skipOnError' => true, 'targetClass' => PlanSchedule::class, 'targetAttribute' => ['plan_schedule_id' => 'id']],
    //        ];
    //    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return [
            'id' => Yii::t('main', 'ID'),
            'plan_schedule_id' => Yii::t('main', 'Plan Schedule ID'),
            'classifier_id' => Yii::t('main', 'Classifier ID'),
            'status' => Yii::t('main', 'Status'),
            'enabled' => Yii::t('main', 'Enabled'),
            'created_at' => Yii::t('main', 'Created At'),
            'updated_at' => Yii::t('main', 'Updated At'),
            'deleted_at' => Yii::t('main', 'Deleted At'),
            'created_by' => Yii::t('main', 'Created By'),
            'updated_by' => Yii::t('main', 'Updated By'),
            'unit_id' => Yii::t('main', 'Unit ID'),
            'description' => Yii::t('main', 'Description'),
            'year' => Yii::t('main', 'Year'),
            'month' => Yii::t('main', 'Month'),
            'count' => Yii::t('main', 'Count'),
            'count_live' => Yii::t('main', 'tovar qanchasi tenderdan yutilgani'),
            'count_used' => Yii::t('main', 'Count Used'),
            'quarter' => Yii::t('main', 'Quarter'),
            'source_of_funding' => Yii::t('main', 'Source Of Funding'),
        ];
    }

    /**
     * Gets query for [[Classifier]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getClassifier()
    {
        return $this->hasOne(Classifier::class, ['id' => 'classifier_id']);
    }

    /**
     * Gets query for [[PlanSchedule]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getPlanSchedule()
    {
        return $this->hasOne(PlanSchedule::class, ['id' => 'plan_schedule_id']);
    }
}
