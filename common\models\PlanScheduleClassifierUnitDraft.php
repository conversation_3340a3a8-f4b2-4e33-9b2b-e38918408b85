<?php

namespace common\models;

use Yii;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "plan_schedule_classifier_unit_draft".
 *
 * @property int $id
 * @property int|null $plan_schedule_classifier_id
 * @property int|null $classifier_property_id
 * @property int|null $status
 *
 * @property ClassifierProperties $classifierProperty
 * @property PlanScheduleClassifier $planScheduleClassifier
 */
class PlanScheduleClassifierUnitDraft extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return 'plan_schedule_classifier_unit_draft';
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [['plan_schedule_classifier_id', 'classifier_property_id', 'status'], 'default', 'value' => null],
            [['plan_schedule_classifier_id', 'classifier_property_id', 'status'], 'default', 'value' => null],
            [['plan_schedule_classifier_id', 'classifier_property_id', 'status'], 'integer'],
            [['classifier_property_id'], 'exist', 'skipOnError' => true, 'targetClass' => ClassifierProperties::class, 'targetAttribute' => ['classifier_property_id' => 'id']],
            [['plan_schedule_classifier_id'], 'exist', 'skipOnError' => true, 'targetClass' => PlanScheduleClassifier::class, 'targetAttribute' => ['plan_schedule_classifier_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return [
            'id' => 'ID',
            'plan_schedule_classifier_id' => 'Plan Schedule Classifier ID',
            'classifier_property_id' => 'Classifier Property ID',
            'status' => 'Status',
        ];
    }

    /**
     * Gets query for [[ClassifierProperty]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getClassifierProperty()
    {
        return $this->hasOne(ClassifierProperties::class, ['id' => 'classifier_property_id']);
    }

    /**
     * Gets query for [[PlanScheduleClassifier]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getPlanScheduleClassifier()
    {
        return $this->hasOne(PlanScheduleClassifier::class, ['id' => 'plan_schedule_classifier_id']);
    }

}
