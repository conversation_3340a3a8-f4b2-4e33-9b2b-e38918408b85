<?php

namespace common\models;

use Yii;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "sms".
 *
 * @property int $id
 * @property int|null $user_id
 * @property string $phone
 * @property string $code
 * @property string|null $message
 * @property int|null $status
 * @property int|null $failed_attempt
 * @property int|null $last_failed_attempt
 * @property int|null $created_at
 * @property int|null $updated_at
 *
 * @property Company $company
 * @property User $user
 */
class Sms extends ActiveRecord
{


    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return 'sms';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['user_id', 'failed_attempt', 'last_failed_attempt', 'message', 'status', 'created_at', 'updated_at'], 'default', 'value' => null],
            [['user_id', 'status', 'created_at', 'updated_at','code'], 'default', 'value' => null],
            [['user_id', 'status', 'created_at', 'updated_at'], 'integer'],
            [['phone', 'code'], 'required'],
            [['phone'], 'string', 'max' => 50],
            [['message'], 'string', 'max' => 255],
            [['user_id'], 'exist', 'skipOnError' => true, 'targetClass' => User::class, 'targetAttribute' => ['user_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_id' => 'User ID',
            'phone' => 'Phone',
            'code' => 'Code',
            'message' => 'Message',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[User]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUser()
    {
        return $this->hasOne(User::class, ['id' => 'user_id']);
    }

}
