<?php

namespace common\models\auction;

use Yii;
use api\modules\common\resources\CompanyResource;
use api\modules\common\resources\RegionResource;
use common\components\ActiveRecordMeta;
use common\enums\AuctionEnum;
use common\models\Classifier;
use common\models\ClassifierCategory;
use common\models\Company;
use common\models\File;
use common\models\PlanSchedule;
use common\models\Region;
use console\models\AuctionOffer;
use yii\base\InvalidConfigException;
use yii\db\ActiveQuery;

/**
 * This is the model class for table "auction".
 *
 * @property int $id
 * @property string $title
 * @property int $lot
 * @property int $plan_schedule_id
 * @property int $company_id
 * @property int $classifier_category_id
 * @property int $status
 * @property float $total_sum
 * @property string $cancel_reason
 * @property string $begin_date
 * @property string $auction_end
 * @property string $cancel_date
 * @property int $payment_status
 * @property string $payment_date
 * @property int $delivery_period
 * @property int $payment_period
 * @property int $region_id
 * @property int $district_id
 * @property string $address
 * @property string $account
 * @property string|null $organ
 * @property string|null $delivery_basis
 * @property string $organization_phone
 *
 * @property int $created_at
 * @property int $updated_at
 * @property int $deleted_at
 * @property int $created_by
 * @property int $updated_by
 * @property string $description // condition
 *
 * @property Company $company
 * @property ClassifierCategory $classifierCategory
 * @property Region $region
 * @property AuctionOffer[] $offers
 * @property AuctionCondition[] $auctionConditions
 * @property AuctionClassifier[] $auctionClassifiers
 * @property int $auctionClassifierQuantity
 * @property AuctionFile[] $auctionFiles
 * @property float|int $currentPrice
 * @property float|int $nextPrice
 *
 */
class Auction extends ActiveRecordMeta
{
    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return 'auction';
    }

     /**
      * {@inheritdoc}
      */
     public function rules()
     {
       return [
         [['title', 'company_id', 'status', 'total_sum', 'payment_status', 'delivery_period', 'payment_period', 'region_id', 'district_id',], 'safe'],
         [['company_id', 'status', 'payment_status', 'delivery_period', 'payment_period', 'region_id', 'district_id',], 'integer'],
         [['total_sum'], 'number'],
         [['cancel_reason', 'address'], 'string'],
         ['title', 'string', 'max' => 255],
         [['auction_end', 'begin_date', 'cancel_date', 'payment_date'], 'safe'],
         [['company_id'], 'exist', 'skipOnError' => true, 'targetClass' => Company::class, 'targetAttribute' => ['company_id' => 'id']],
         [['plan_schedule_id'], 'exist', 'skipOnError' => true, 'targetClass' => PlanSchedule::class, 'targetAttribute' => ['plan_schedule_id' => 'id']],
         [['region_id'], 'exist', 'skipOnError' => true, 'targetClass' => Region::class, 'targetAttribute' => ['region_id' => 'id'], 'filter' => ['type' => Region::TYPE_REGION]],
         [['district_id'], 'exist', 'skipOnError' => true, 'targetClass' => Region::class, 'targetAttribute' => ['district_id' => 'id'], 'filter' => ['type' => Region::TYPE_DISTRICT]],
       ];
     }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return [
            'id' => Yii::t('main', 'ID'),
            'title' => Yii::t('main', 'Title'),
            'company_id' => Yii::t('main', 'Company ID'),
            'status' => Yii::t('main', 'Status'),
            'total_sum' => Yii::t('main', 'Total Sum'),
            'cancel_reason' => Yii::t('main', 'Cancel Reason'),
            'begin_end' => Yii::t('main', 'Begin Date'),
            'auction_end' => Yii::t('main', 'Auction End'),
            'cancel_date' => Yii::t('main', 'Cancel Date'),
            'payment_status' => Yii::t('main', 'Payment Status'),
            'payment_date' => Yii::t('main', 'Payment Date'),
            'delivery_period' => Yii::t('main', 'Delivery Period'),
            'payment_period' => Yii::t('main', 'Payment Period'),
            'region_id' => Yii::t('main', 'Region ID'),
            'district_id' => Yii::t('main', 'District ID'),
            'address' => Yii::t('main', 'Address'),
            'description' => Yii::t('main', 'description'),
            'created_at' => Yii::t('main', 'Created At'),
            'updated_at' => Yii::t('main', 'Updated At'),
            'deleted_at' => Yii::t('main', 'Deleted At'),
            'created_by' => Yii::t('main', 'Created By'),
            'updated_by' => Yii::t('main', 'Updated By'),
        ];
    }

    /**
     * @return ActiveQuery
     */
    public function getOffers()
    {
        return $this->hasMany(AuctionOffer::class, ['auction_id' => 'id']);
    }

    /**
     * @return ActiveQuery
     */
    public function getAuctionClassifiers()
    {
        return $this->hasMany(AuctionClassifier::class, ['auction_id' => 'id']);
    }

    /**
     * @return ActiveQuery
     */
    public function getClassifiers()
    {
        return $this->hasMany(Classifier::class, ['id' => 'classifier_id'])->via('auctionClassifiers');
    }

    /**
     * Gets query for [[AuctionFiles]].
     *
     * @return ActiveQuery
     */
    public function getAuctionFiles()
    {
        return $this->hasMany(AuctionFile::class, ['auction_id' => 'id']);
    }

    /**
     * Gets query for [[Files]].
     *
     * @return ActiveQuery
     * @throws InvalidConfigException
     */
    public function getFiles()
    {
        return $this->hasMany(File::class, ['id' => 'file_id'])->viaTable('auction_file', ['auction_id' => 'id']);
    }

    public function getCurrentPrice(): float|int
    {
        /** @var AuctionOffer|null $last_offer */
        $last_offer = AuctionOffer::find()->where(['auction_id' => $this->id, 'deleted_at' => null])->orderBy("price asc")->one();
        return $last_offer ? $last_offer->price / 100 : $this->total_sum / 100;
    }

    public function getNextPrice(): float
    {
        return $this->currentPrice - $this->total_sum / 100 * 0.02;
    }

    public function getCompany()
    {
        return $this->hasOne(CompanyResource::class, ['id' => 'company_id']);
    }

    /**
     * @return ActiveQuery
     */
    public function getRegion()
    {
        return $this->hasOne(RegionResource::class, ['id' => 'region_id']);
    }

    /**
     * Gets query for [[AuctionRequests]].
     *
     * @return ActiveQuery
     */
    public function getAuctionRequests()
    {
        return $this->hasMany(AuctionOffer::class, ['auction_id' => 'id']);
    }

    public static function getStatuses(): array
    {
        return [
            AuctionEnum::STATUS_ACTIVE => t("Активный"),
            AuctionEnum::STATUS_FINISHED => t("Завершен"),
            AuctionEnum::STATUS_MODERATING => t("Модерируется"),
            AuctionEnum::STATUS_REJECTED => t("Отклонен"),
            AuctionEnum::STATUS_NOT_HELD => t("Amalga oshmagan"),
            AuctionEnum::STATUS_DMBAT => t("Dmbat"),
            AuctionEnum::STATUS_DMBAT_REJECT => t("Dmbat reject"),
        ];
    }

    public function getStatusName()
    {
        return array_key_exists($this->status, self::getStatuses()) ? self::getStatuses()[$this->status] : "(Не известно)";
    }

    public static function generateLotNumber($id): string
    {
        $YY = substr(date('Y'), -2);
        $P = env('LOT_OPERATOR_NUMBER', 5);
        $CUSTOMER_TYPE = env('LOT_CUSTOMER_TYPE', 2);
        $K = 1;
        $CCC = '007';
        return $YY . $P . $CUSTOMER_TYPE . $K . $CCC . sprintf('%06d', $id);
    }
}
