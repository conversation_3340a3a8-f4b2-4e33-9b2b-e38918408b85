<?php

namespace common\models\auction;

use common\models\PlanScheduleClassifier;
use Yii;
use common\components\ActiveRecordMeta;
use common\models\Classifier;
use common\models\Unit;

/**
 * This is the model class for table "auction_classifier".
 *
 * @property int $id
 * @property int|null $auction_id
 * @property int|null $classifier_category_id
 * @property int|null $classifier_id
 * @property int|null $plan_schedule_id
 * @property int|null $plan_schedule_classifier_id
 * @property int|null $order
 * @property int|null $quantity
 * @property int|null $unit_id
 * @property int|null $status
 * @property float|null $price
 * @property int|null $total_sum
 * @property string|null $description
 * @property string|null $created_at
 * @property string|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $created_by
 * @property int|null $updated_by
 * 
 * @property Classifier $classifier
 * @property Auction $auction
 * @property PlanScheduleClassifier $planScheduleClassifier
 */
class AuctionClassifier extends ActiveRecordMeta
{
  /**
   * {@inheritdoc}
   */
  public static function tableName()
  {
    return 'auction_classifier';
  }

  /**
   * {@inheritdoc}
   */
  public function attributeLabels()
  {
    return [
      'id' => Yii::t('main', 'ID'),
      'auction_id' => Yii::t('main', 'Auction ID'),
      'classifier_id' => Yii::t('main', 'Classifier ID'),
      'quantity' => Yii::t('main', 'Quantity'),
      'unit_id' => Yii::t('main', 'Unit ID'),
      'price' => Yii::t('main', 'Price'),
      'total_sum' => Yii::t('main', 'Total Sum'),
      'description' => Yii::t('main', 'Description'),
      'created_at' => Yii::t('main', 'Created At'),
      'updated_at' => Yii::t('main', 'Updated At'),
      'deleted_at' => Yii::t('main', 'Deleted At'),
      'created_by' => Yii::t('main', 'Created By'),
      'updated_by' => Yii::t('main', 'Updated By'),
    ];
  }

  /**
   * Gets query for [[Classifier]].
   *
   * @return \yii\db\ActiveQuery
   */
  public function getClassifier()
  {
    return $this->hasOne(Classifier::class, ['id' => 'classifier_id']);
  }

  public function getPlanScheduleClassifier()
  {
    return $this->hasOne(PlanScheduleClassifier::class, ['id' => 'plan_schedule_classifier_id']);
  }

  /**
   * Gets query for [[Classifier Unit]].
   *
   * @return \yii\db\ActiveQuery
   */
  public function getUnit()
  {
    return $this->hasOne(Unit::class, ['id' => 'unit_id']);
  }

  /**
   * Gets query for [[Auction]].
   *
   * @return \yii\db\ActiveQuery
   */
  public function getAuction()
  {
    return $this->hasOne(Auction::class, ['id' => 'auction_id']);
  }
}
