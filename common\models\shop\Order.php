<?php

namespace common\models\shop;

use api\modules\shop\resources\OrderRequestResource;
use common\enums\ShopEnum;
use common\models\Classifier;
use common\models\Company;
use common\models\PlanSchedule;
use common\models\Region;
use common\models\User;
use Yii;

/**
 * This is the model class for table "order".
 *
 * @property int $id
 * @property int|null $type
 * @property int|null $user_id
 * @property int|null $company_id
 * @property int|null $product_id
 * @property string|null $customer_account_treasury
 * @property string|null $expense_item
 * @property string|null $receiver_fio
 * @property string|null $receiver_phone
 * @property int|null $delivery_type
 * @property int|null $payment_type
 * @property float|null $total_sum
 * @property string|null $created_at
 * @property string|null $begin_date
 * @property float|null $shipping_sum
 * @property int|null $status
 * @property int|null $payment_status
 * @property int|null $payment_date
 * @property string|null $cancel_reason
 * @property string|null $cancel_date
 * @property string|null $shop_end
 * @property string|null $request_end
 * @property string|null $lot_number
 * @property string|null $start_one_sided_customer
 * @property string|null $start_one_sided_producer
 * @property string|null $one_sided_customer
 * @property int|null $count
 *
 * @property Company $company
 * @property OrderList[] $orderLists
 * @property User $user
 * @property OrderRequest $winner
 * @property OrderRequest $reserve
 * @property Product $product
 */
class Order extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'order';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['type', 'user_id', 'company_id', 'delivery_type', 'payment_type', 'status', 'payment_status', 'payment_date'], 'integer'],
            [['shipping_sum'], 'number'],
            [['created_at', 'begin_date', 'cancel_date', 'shop_end', 'request_end', 'total_sum', 'lot_number'], 'safe'],
            [['cancel_reason'], 'string'],
            [['one_sided_customer', 'start_one_sided_producer', 'start_one_sided_customer'], 'safe'],
            [['customer_account_treasury', 'expense_item', 'receiver_fio', 'receiver_phone'], 'string', 'max' => 255],
            [['company_id'], 'exist', 'skipOnError' => true, 'targetClass' => Company::class, 'targetAttribute' => ['company_id' => 'id']],
            [['user_id'], 'exist', 'skipOnError' => true, 'targetClass' => User::class, 'targetAttribute' => ['user_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'type' => 'Type',
            'user_id' => 'User ID',
            'company_id' => 'Company ID',
            'customer_account_treasury' => 'Customer Account Treasury',
            'expense_item' => 'Expense Item',
            'receiver_fio' => 'Receiver Fio',
            'receiver_phone' => 'Receiver Phone',
            'delivery_type' => 'Delivery Type',
            'payment_type' => 'Payment Type',
            'total_sum' => 'Total Sum',
            'created_at' => 'Created At',
            'shipping_sum' => 'Shipping Sum',
            'status' => 'Status',
            'payment_status' => 'Payment Status',
            'payment_date' => 'Payment Date',
            'cancel_reason' => 'Cancel Reason',
            'cancel_date' => 'Cancel Date',
            'shop_end' => 'Shop End',
            'request_end' => 'Request End',
        ];
    }

    /**
     * Gets query for [[Company]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCompany()
    {
        return $this->hasOne(Company::class, ['id' => 'company_id']);
    }

    /**
     * Gets query for [[OrderLists]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getOrderLists()
    {
        return $this->hasMany(OrderList::class, ['order_id' => 'id']);
    }

    public function getOrderList()
    {
        return $this->hasOne(OrderList::class, ['order_id' => 'id'])->orderBy(['id' => SORT_DESC]);
    }


    public function getPlanSchedule()
    {
        return $this->hasOne(PlanSchedule::class, ['id' => 'plan_schedule_id']);
    }

    /**
     * Gets query for [[User]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUser()
    {
        return $this->hasOne(User::class, ['id' => 'user_id']);
    }

    public function getOrderRegion()
    {
        return $this->hasOne(OrderRegion::class, ['order_id' => 'id']);
    }

    public function getRegion()
    {
        return $this->hasOne(Region::class, ['id' => 'region_id'])->via('orderRegion');
    }

    /**
     * Gets query for [[User]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getClassifier()
    {
        return $this->hasOne(Classifier::class, ['id' => 'classifier_id']);
    }

    /**
     * Gets query for [[User]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getProduct()
    {
        return $this->hasOne(Product::class, ['id' => 'product_id']);
    }

    /**
     * Gets query for [[User]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getOrderRequest()
    {
        return $this->hasOne(OrderRequestResource::class, ['order_id' => 'id'])->andWhere(['company_id' => Yii::$app->user->identity->company_id])->orderBy([OrderRequestResource::tableName() . '.id' => SORT_DESC]);
    }

    /**
     * Gets query for [[User]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getOrderRequestCancel()
    {
        return $this->hasOne(OrderRequestResource::class, ['order_id' => 'id'])->andWhere(['!=', 'status', ShopEnum::ORDER_REQUEST_STATUS_DONE])->orderBy([OrderRequestResource::tableName() . '.id' => SORT_DESC]);
    }

    /**
     * Gets query for [[User]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getAllOrderRequests()
    {
        return $this->hasMany(OrderRequestResource::class, ['order_id' => 'id'])->andWhere(['status' => ShopEnum::ORDER_REQUEST_STATUS_DONE]);
    }

    public function getWinner()
    {
        return OrderRequest::findOne(['order_id' => $this->id, 'is_winner' => 1]);
    }

    public function getReserve()
    {
        return OrderRequest::findOne(['order_id' => $this->id, 'is_winner' => 2]);
    }

    public function getOrderStartSum()
    {
        return $this->hasOne(OrderRequestResource::class, ['order_id' => 'id'])->orderBy([OrderRequestResource::tableName() . '.id' => SORT_ASC]);
    }
}
