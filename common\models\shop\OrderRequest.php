<?php

namespace common\models\shop;

use api\modules\shop\resources\OrderResource;
use common\models\Company;
use common\traits\FindOrFail;
use Yii;

/**
 * This is the model class for table "order_request".
 *
 * @property int $id
 * @property int|null $order_id
 * @property float|null $price
 * @property int|null $company_id
 * @property int|null $is_winner
 * @property string|null $created_at
 * @property string|null $updated_at
 * @property string|null $status
 * @property string|null $type
 * @property string|null $deleted_at
 */
class OrderRequest extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'order_request';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['order_id', 'company_id', 'is_winner'], 'integer'],
            [['price'], 'number'],
            [['created_at', 'updated_at','status','type','deleted_at'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'order_id' => 'Order ID',
            'price' => 'Price',
            'company_id' => 'Company ID',
            'is_winner' => 'Is Winner',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }
    /**
     * Gets query for [[User]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCompany()
    {
        return $this->hasOne(Company::class, ['id' => 'company_id']);
    }
    /**
     * Gets query for [[User]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getOrder()
    {
        return $this->hasOne(OrderResource::class, ['id' => 'order_id']);
    }
}
