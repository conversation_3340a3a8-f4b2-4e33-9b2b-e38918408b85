<?php

namespace common\models\shop;

use api\modules\common\resources\FileResource;
use api\modules\shop\resources\FavoriteResource;
use api\modules\shop\resources\ProductCommentResource;
use api\modules\shop\resources\ProductModeratorLogResource;
use common\components\ActiveRecordMeta;
use common\models\Classifier;
use common\models\ClassifierCategory;
use common\models\Company;
use common\models\Region;
use common\models\Unit;
use common\traits\FindOrFail;
use common\traits\NotDeleted;
use common\traits\SoftDelete;
use Yii;

/**
 * This is the model class for table "product".
 *
 * @property int $id
 * @property int|null $classifier_category_id
 * @property int|null $classifier_id
 * @property int|null $company_id
 * @property int|null $region_id
 * @property int|null $unit_id
 * @property string|null $title
 * @property string|null $brand_title
 * @property string|null $description_uz
 * @property string|null $description_ru
 * @property int|null $year
 * @property int|null $quantity
 * @property float|null $price
 * @property int|null $min_order
 * @property int|null $max_order
 * @property int|null $delivery_period
 * @property int|null $warranty_period
 * @property int|null $expiry_period
 * @property int|null $status
 * @property int|null $is_have_license
 * @property string|null $created_at
 * @property string|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property int|null $state
 * @property int|null $type
 * @property string|null $active_date
 * @property string|null $inactive_date
 * @property int|null $unit_price
 * @property string|null $delete_reason
 * @property string|null $platform_display
 * @property boolean|null $auto_renewal
 *
 * @property Classifier $classifier
 * @property ClassifierCategory $classifierCategory
 * @property Company $company
 * @property ProductFile[] $productFiles
 * @property ProductRegion[] $productRegions
 * @property Region $region
 * @property Unit $unit
 * @property Order $order
 */
class Product extends ActiveRecordMeta
{
    use SoftDelete;
    use NotDeleted;
    use FindOrFail;

//    public static function getPeriodType($index = null){
//        $arr = [
//            1 => 'Yil',
//            2 => 'Oy',
//            3 => 'Kun',
//        ];
//        return isset($arr[$index]) && $arr[$index] ? $arr[$index] : $index;
//    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'product';
    }

    /**
     * {@inheritdoc}
     */
    // public function rules()
    // {
    //     return [
    //         [['classifier_category_id', 'classifier_id', 'company_id', 'region_id', 'unit_id', 'year', 'quantity', 'min_order', 'max_order', 'delivery_period', 'delivery_period_type', 'warranty_period', 'warranty_period_type', 'expiry_period', 'expiry_period_type', 'status', 'is_have_license', 'created_by', 'updated_by'], 'integer'],
    //         [['description'], 'string'],
    //         [['price'], 'number'],
    //         [['created_at', 'updated_at', 'deleted_at'], 'safe'],
    //         [['title', 'brand_title'], 'string', 'max' => 255],
    //         [['classifier_category_id'], 'exist', 'skipOnError' => true, 'targetClass' => ClassifierCategory::class, 'targetAttribute' => ['classifier_category_id' => 'id']],
    //         [['classifier_id'], 'exist', 'skipOnError' => true, 'targetClass' => Classifier::class, 'targetAttribute' => ['classifier_id' => 'id']],
    //         [['company_id'], 'exist', 'skipOnError' => true, 'targetClass' => Company::class, 'targetAttribute' => ['company_id' => 'id']],
    //         [['region_id'], 'exist', 'skipOnError' => true, 'targetClass' => Region::class, 'targetAttribute' => ['region_id' => 'id']],
    //         [['unit_id'], 'exist', 'skipOnError' => true, 'targetClass' => Unit::class, 'targetAttribute' => ['unit_id' => 'id']],
    //     ];
    // }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'classifier_category_id' => 'Classifier Category ID',
            'classifier_id' => 'Classifier ID',
            'company_id' => 'Company ID',
            'region_id' => 'Region ID',
            'unit_id' => 'Unit ID',
            'title' => 'Title',
            'brand_title' => 'Brand Title',
            'description' => 'Description',
            'auto_renewal' => 'Auto Renewal',
            'year' => 'Year',
            'quantity' => 'Quantity',
            'price' => 'Price',
            'min_order' => 'Min Order',
            'max_order' => 'Max Order',
            'delivery_period' => 'Delivery Period',
            'warranty_period' => 'Warranty Period',
            'expiry_period' => 'Expiry Period',
            'status' => 'Status',
            'is_have_license' => 'Is Have License',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'deleted_at' => 'Deleted At',
            'created_by' => 'Created By',
            'updated_by' => 'Updated By',
        ];
    }

    /**
     * Gets query for [[Classifier]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getClassifier()
    {
        return $this->hasOne(Classifier::class, ['id' => 'classifier_id']);
    }

    /**
     * Gets query for [[ClassifierCategory]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getClassifierCategory()
    {
        return $this->hasOne(ClassifierCategory::class, ['id' => 'classifier_category_id']);
    }

    /**
     * Gets query for [[Company]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCompany()
    {
        return $this->hasOne(Company::class, ['id' => 'company_id']);
    }

    /**
     * Gets query for [[ProductFiles]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getProductFiles()
    {
        return $this->hasMany(ProductFile::class, ['product_id' => 'id'])->andWhere([ProductFile::tableName() . '.type' => ProductFile::TYPE_FILE]);
    }

    /**
     * Gets query for [[ProductFiles]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getProductImages()
    {
        return $this->hasMany(ProductFile::class, ['product_id' => 'id'])->andWhere([ProductFile::tableName() . '.type' => ProductFile::TYPE_IMAGE]);
    }

    /**
     * Gets query for [[ProductRegions]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getProductRegions()
    {
        return $this->hasMany(ProductRegion::class, ['product_id' => 'id']);
    }

    /**
     * Gets query for [[Region]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getRegion()
    {
        return $this->hasMany(Region::class, ['id' => 'region_id'])->via('productRegions');
    }

    /**
     * Gets query for [[Unit]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUnit()
    {
        return $this->hasOne(Unit::class, ['id' => 'unit_id']);
    }

    /**
     * Gets query for [[Region]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getFiles()
    {
        return $this->hasMany(FileResource::class, ['id' => 'file_id'])->via('productFiles');
    }

    public function getImages()
    {
        return $this->hasMany(FileResource::class, ['id' => 'file_id'])->orderBy(['is_main' => SORT_DESC])->via('productImages');
    }

    public function getFavorite()
    {
        return $this->hasOne(FavoriteResource::class, ['product_id' => 'id'])->andWhere(['user_id' => Yii::$app->user->id]);
    }

    public function getComments()
    {
        return $this->hasMany(ProductCommentResource::class, ['product_id' => 'id']);
    }

    /**
     * Gets query for [[Region]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCountry()
    {
        return $this->hasOne(Region::class, ['id' => 'country_id']);
    }

    public function getHasOrder()
    {
        return $this->hasMany(Order::class, ['product_id' => 'id'])->andWhere(['>', 'request_end', date("Y-m-d H:i:s")])->exists();
    }

    public function getIsOwnerProduct()
    {
        if (Yii::$app->user->isGuest) {
            return false;
        }
        return Yii::$app->user->identity->company_id == $this->company_id;
    }

    public function getModeratorLog()
    {
        return $this->hasMany(ProductModeratorLogResource::class, ['product_id' => 'id']);
    }

    public function getOrder()
    {
        return $this->hasOne(Order::class , ['product_id' => 'id']);
    }
}
