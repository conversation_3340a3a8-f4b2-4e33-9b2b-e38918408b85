<?php

namespace common\services;

use Yii;
use yii\base\Component;
use yii\base\InvalidConfigException;

class SmsService extends Component
{
    const string SMS_HOST = 'https://notify.eskiz.uz/api/message/sms/send';
    const string SMS_LOGIN_HOST = 'https://notify.eskiz.uz/api/auth/login';
    public static function generateSmsMessage(string $message,int $rand, string $pattern = "0000"): array|string|null
    {
        return preg_replace("/$pattern/", $rand, $message);
    }

    /**
     * @throws InvalidConfigException
     */
    public static function getToken(): ?string
    {
        $response = (new RequestService())
            ->setBaseUrl(self::SMS_LOGIN_HOST)
            ->setRequestMethod('POST')
            ->setParam([
                'email' => env('SMS_USERNAME'),
                'password' => env('SMS_PASSWORD'),
            ])
            ->send();
        return $response['data']['token'] ?? null;
    }

    /**
     * @throws InvalidConfigException
     * @throws \Exception
     */
    public static function sendSms(string $phone, string $message): bool
    {
        $params = [
            'mobile_phone' => $phone,
            'message' => $message,
            'from' => 4546,
            'callback_url' => 'https://xarid.ebirja.uz/',
        ];
        $request = (new RequestService())
            ->setBaseUrl(self::SMS_HOST)
            ->setRequestMethod('POST')
            ->setToken(self::_getToken())
            ->setParam($params);
        $response = $request->send();
        if ($response->httpStatusCode == 401) {
            $request->setToken(self::getToken());
            $response = $request->send();
        }
        if ($response === null)
        {
            throw new \Exception('SMS bilan aloqa yoq. ');
        }

        return isset($response['status']) && $response['status'] === 'waiting';
    }

    /**
     * @throws InvalidConfigException
     */
    private static function _getToken(): string
    {
        $token = Yii::$app->cache->get('sms_token');
        if (!$token)
        {
            $token = self::getToken();
            Yii::$app->cache->set('sms_token', $token, 29 * 86400); // expire 30 days from sms service
        }
        return $token;
    }
}