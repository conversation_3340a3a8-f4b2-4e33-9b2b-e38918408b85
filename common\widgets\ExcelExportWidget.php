<?php

namespace common\widgets;

use Yii;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Exception;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use yii\base\Widget;
use yii\db\ActiveRecord;
use yii\web\Response;

class ExcelExportWidget extends Widget
{
    public ?string $fileName = null;

    /**
     * @var array Export fields
     */
    public array $fields = [];

    /**
     * @var array Export labels
     */
    public array $labels = [];

    /**
     * @var ActiveRecord[]
     */
    public array $models = [];

    /**
     * @var ActiveRecord|null - First Model
     */
    public ?ActiveRecord $_model = null;

    /**
     * @throws \Exception
     */
    public function init(): void
    {
        parent::init();
        $this->_model = $this->models[0] ?? null;
        if (!$this->fileName) {
            $this->setFileName();
        }
        $this->setFields();
    }

    public function setFields(): void
    {
        if (empty($this->fields)) {
            $this->fields = $this->_model->fields();
        }
    }

    /**
     * @throws Exception
     */
    public function run()
    {
        // Create new Spreadsheet object
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        // Set headers for the Excel file
        $_index = 0;
        foreach ($this->fields as $key => $field) {
            $_index = $_index + 1;
            if (is_string($key)) {
                $attribute = $key;
            } else {
                $attribute = $field;
            }
            $sheet->setCellValue(Coordinate::stringFromColumnIndex($_index) . 1, $this->generateAttributeLabel($attribute));
        }

        $row = 2;
        foreach ($this->models as $model) {
            $__index = 0;
            foreach ($this->fields as $key => $field) {
                $__index = $__index + 1;
                if (is_string($key) && is_callable($field)) {
                    $value = $field($model);
                } else {
                    $value = $model->{$field};
                }
                $sheet->setCellValue(Coordinate::stringFromColumnIndex($__index) . $row, $value);
                if (strpos($value, "\n") !== false) {
                    $sheet->getStyle(Coordinate::stringFromColumnIndex($__index) . $row)->getAlignment()->setWrapText(true);
                }
            }
            $row++;
        }

        $filename = $this->fileName . '.xlsx';
        // Set response headers for Excel download
        Yii::$app->response->format = Response::FORMAT_RAW;
        Yii::$app->response->headers->add('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        Yii::$app->response->headers->add('Content-Disposition', "attachment;filename={$filename}");
        Yii::$app->response->headers->add('Cache-Control', 'max-age=0');

        // Create Excel writer and output to buffer
        $writer = new Xlsx($spreadsheet);
        ob_start();
        @$writer->save('php://output');
        // Return the Excel file content
        return ob_get_clean();
    }

    private function getAttributeLabel($field): ?string
    {
        if ($this->_model->hasAttribute($field)) {
            return $this->_model->getAttributeLabel($field);
        }
        return is_string($field) ? $field : null;
    }

    private function setFileName(): void
    {
        $this->fileName = 'export_' . date('Y-m-d_H-i-s') . '.xlsx';
    }

    public function generateAttributeLabel($attribute): string
    {
        return $this->labels[$attribute] ?? $this->getAttributeLabel($attribute);
    }
}