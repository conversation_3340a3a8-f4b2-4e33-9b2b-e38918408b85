<?php


namespace console\controllers;


use common\enums\BankServiceEnum;
use common\enums\OperationTypeEnum;
use common\enums\SystemEnum;
use common\models\bank\BankTransactionIn;
use common\models\Company;
use common\models\CompanyBankAccount;
use common\models\CompanyVirtualAccount;
use common\models\VirtualTransaction;
use yii\base\Exception;
use yii\console\Controller;

class BankController extends Controller
{
    /**
     * @var $lastOne BankTransactionIn
     */

    public function actionKirim()
    {

        echo "start \n";
        $lastOne = BankTransactionIn::find()->orderBy(['bank_id' => SORT_DESC])->one();
        $lastId = 0;
        if ($lastOne != null) {
            $lastId = $lastOne->bank_id;
        }

        $fromDate = date('Y-m-d', strtotime("2024-09-01"));
        if (date('Y-m-d') != date('Y-m-d', strtotime("2024-09-28"))) { //vaqtincha
            $fromDate = date('Y-m-d');
        }
        echo $fromDate . "\n";

        $requestData = [
            "type" => "1", // 0-barchasi, 1-krim, 2-chiqim
            "lastId" => $lastId,
            "fromDate" => $fromDate,
            "toDate" => date('Y-m-d'),
            "serviceId" => BankServiceEnum::SERVICE_ID,
        ];

//        $inPayments = $this->getAccountPayments($requestData);
        $inPayments = [
            // bu misollar, haqiqiy javob API'ga bog‘liq
            'status' => 'success',
            'message' => 'Payments retrieved successfully',
            'data' => [
                [
                    'id' => rand(100000, 999999),
                    'docNumber' => 'INV-2025-001',
                    'docDate' => '2025-06-24',
                    'paymentDate' => '2025-06-25',
                    'type' => 1,
                    'mfoPayer' => '01095',
                    'accountPayer' => '20208*********001001',
                    'namePayer' => 'MCHJ "Innovatsiya Tech"',
                    'innPayer' => '*********', // noma'lum kompaniya
                    'mfoReceiver' => '00444',
                    'accountReceiver' => '20208*********002001',
                    'nameReceiver' => 'ООО "Texno Park"',
                    'innReceiver' => '*********',
                    'amount' => '********', // so‘mda (masalan: 1 500 000 so‘m)
                    'purpose' => 'Shartnoma bo‘yicha to‘lov',
                    'status' => 1, // yoki 0, API qanday berishiga qarab
                ],
//                [
//                    'id' => rand(100000, 999999),
//                    'docNumber' => 'INV-2025-001',
//                    'docDate' => '2025-06-24',
//                    'paymentDate' => '2025-06-25',
//                    'type' => 1,
//                    'mfoPayer' => '01095',
//                    'accountPayer' => '20208*********001001',
//                    'namePayer' => 'MCHJ "Innovatsiya Tech"',
//                    'innPayer' => '*********',
//                    'mfoReceiver' => '00444',
//                    'accountReceiver' => '20208*********002001',
//                    'nameReceiver' => 'ООО "Texno Park"',
//                    'innReceiver' => '*********',
//                    'amount' => 1500000, // so‘mda (masalan: 1 500 000 so‘m)
//                    'purpose' => 'Shartnoma bo‘yicha to‘lov',
//                    'status' => 1, // yoki 0, API qanday berishiga qarab
//                ],
            ]
        ];

        if (!empty($inPayments)) {
            if ($inPayments['status'] == "success") {
                echo "soni: " . count($inPayments['data']) . "\n";
                foreach ($inPayments['data'] as $account) {

                    $check = BankTransactionIn::find()->where(['bank_id' => $account['id']])->one();
                    if ($check == null) {
                        $tran = $this->saveTransactionIn($account);
                        try {
                            $debit = VirtualTransaction::debit50111($tran);
                            $this->setBalance($tran, true, $debit);
                        }catch (Exception $e){
                            echo "Xatolik: " . $e->getMessage() . "\n";
                            echo "pul yechilmadi\n";
                        }
                    }
                }
                echo "tugadi\n";
            }

        } else {
            echo "kelmadi\n";
        }
    }

    /*
     * birikmagan pullarni biriktiradi
     * */
    public function actionBirikish()
    {
        $bankTransactionIns = BankTransactionIn::find()
            ->where(['transaction_state' => BankTransactionIn::STATE_COMPANY_NOT_FOUND])
            ->andWhere(['!=', 'inn_payer', BankServiceEnum::BANK_YATT_PAYER_INN])
            ->all();

        echo "++++++ Korxonasi yo'qlar soni=" . count($bankTransactionIns) . "\n";

        if (!empty($bankTransactionIns)) {
            foreach ($bankTransactionIns as $bankTransactionIn) {
                $res = $this->setBalance($bankTransactionIn, false, null);
                if ($res) {
                    BankTransactionIn::updateAll(['transaction_state' => BankTransactionIn::STATE_ATTACHED], 'id=' . $bankTransactionIn->id);
                }
            }
        }
    }

    /**
     * @var $bankTransactionIn BankTransactionIn
     */
    private function saveTransactionIn($transactionDto)
    {
        $bankTransactionIn = new BankTransactionIn();
        $bankTransactionIn->bank_id = $transactionDto['id'];
        $bankTransactionIn->doc_number = $transactionDto['docNumber'];
        $bankTransactionIn->doc_date = $transactionDto['docDate'];
        $bankTransactionIn->payment_date = $transactionDto['paymentDate'];
        $bankTransactionIn->type = $transactionDto['type'];
        $bankTransactionIn->mfo_payer = $transactionDto['mfoPayer'];
        $bankTransactionIn->account_payer = $transactionDto['accountPayer'];
        $bankTransactionIn->name_payer = $transactionDto['namePayer'];
        $bankTransactionIn->inn_payer = $transactionDto['innPayer'];
        $bankTransactionIn->mfo_receiver = $transactionDto['mfoReceiver'];
        $bankTransactionIn->account_receiver = $transactionDto['accountReceiver'];
        $bankTransactionIn->name_receiver = $transactionDto['nameReceiver'];
        $bankTransactionIn->inn_receiver = $transactionDto['innReceiver'];
        $bankTransactionIn->amount = $transactionDto['amount'];
        $bankTransactionIn->purpose = $transactionDto['purpose'];
        $bankTransactionIn->status = $transactionDto['status'];
        $bankTransactionIn->transaction_state = BankTransactionIn::STATE_ATTACHED;
        $bankTransactionIn->save();
        return $bankTransactionIn;
    }

    private function checkCompanyAccount($transactionIn, $company)
    {
        $companyAccount = CompanyBankAccount::findOne([
            'company_id' => $company->id,
            'account' => $transactionIn->account_payer,
        ]);

        if ($companyAccount === null) {
//            $banks = Bank::findOne(['mfo' => $transactionIn->mfoPayer]);
            $isMainCompanyAccount = CompanyBankAccount::find()
                ->where(['company_id' => $company->id, 'is_main' => 1])
                ->one();

            $companyAccount = new CompanyBankAccount();
//            $companyAccount->bank = $banks ? $banks->name : '-';
            $companyAccount->account = $transactionIn->account_payer;
            $companyAccount->mfo = $transactionIn->mfo_payer;
            $companyAccount->company_id = $company->id;
            if ($isMainCompanyAccount === null) {
                $companyAccount->is_main = 1;
            } else {
                $companyAccount->is_main = 0;
            }
            $companyAccount->save();
        }
    }

    private function setBalance($dataIn, $isNew, $debit)
    {
        if ($dataIn->inn_payer !== BankServiceEnum::BANK_YATT_PAYER_INN) {
            $company = Company::findOne(['tin' => $dataIn->inn_payer]);
            if ($company !== null) {
                // TransactionCreateDto obyektini yaratish va TransactionService orqali saqlash
                try {
                    VirtualTransaction::credit30101(
                        $company,
                        OperationTypeEnum::P_K_30101,
                        $dataIn->amount,
                        "Bankdan hisob to'ldirildi",
                        $debit,
                        OperationTypeEnum::FILL_ACCOUNT
                    );
                }catch (Exception $e){
                    echo "Credit30101 xatolik: " . $e->getMessage() . "\n";
                    return false;
                }

//                $cc = new CompanyTransaction([
//                    'company_id' => $company->id,
//                    'amount' => $dataIn->amount,
//                    'type' => CompanyTransactionEnum::TYPE_REFILL,
//                    'description' => "Bankdan hisob to'ldirildi",
//                    'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                    'transaction_date' => date("Y-m-d H:i:s"),
//                ]);
//                if (!$cc->save()) {
//                    return false;
//                }

                // Kompaniya hisobini tekshirish
                $this->checkCompanyAccount($dataIn, $company);
                return true;
            }
        }

        // Aniqlanmagan tranzaksiya - kompaniya topilmagan holat
        try {
            VirtualTransaction::credit30901($debit);
            echo "Aniqlanmagan tranzaksiya 30901 ga kiritildi.\n";
        } catch (Exception $e) {
            echo "Credit30901 xatolik: " . $e->getMessage() . "\n";
            return false;
        }

        return false;
    }

    private function getAccountPayments($requestData)
    {
//        $ch = curl_init();
//
//        curl_setopt($ch, CURLOPT_URL, "https://rpay.aloqabank.uz:4041/api/v2/account/payments");
//        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
//        curl_setopt($ch, CURLOPT_POST, 1);
//        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($requestData));
//
//        $headers = [
//            'Authorization: Basic ' . base64_encode('dtxarid:2hhnBJ6P8lsg'),
//            'Content-Type: application/json',
//        ];
//        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
//
//        $response = curl_exec($ch);
//        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
//
//        curl_close($ch);
//
//        if ($httpCode == 200) {
//            $responseData = json_decode($response, true);
//            return $responseData;
//        } else {
//            return [];
//        }

    }

}